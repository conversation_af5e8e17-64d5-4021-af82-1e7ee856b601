import type { CodegenConfig } from '@graphql-codegen/cli'
import { wpGraphQlApiEndpoint } from './src/lib/utils/endpoints';

const config: CodegenConfig = {
	schema: wpGraphQlApiEndpoint,
	documents: './src/**/*.gql',
	generates: {
		'./src/lib/shared/api-dtos/external-graphql/generated.ts': {
			plugins: ['typescript', 'typescript-operations', 'typescript-generic-sdk'],
		}
	}
};
export default config