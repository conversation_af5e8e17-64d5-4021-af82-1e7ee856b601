{"name": "free-referral", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev --host", "build": "vite build", "preview": "vite preview", "test": "playwright test", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:unit": "vitest", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "gql": "DOTENV_CONFIG_PATH=.env graphql-codegen -r dotenv/config --config codegen.ts", "map-routes": "npx ts-node --project ./scripts/tsconfig.json ./scripts/generate-routes-map.ts --trace-warnings"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-generic-sdk": "^3.1.0", "@graphql-codegen/typescript-operations": "^4.0.1", "@playwright/test": "^1.28.1", "@sveltejs/adapter-auto": "^2.0.0", "@sveltejs/kit": "^1.5.0", "@types/gtag.js": "^0.0.12", "@types/sanitize-html": "^2.9.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "autoprefixer": "^10.4.13", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-svelte3": "^4.0.0", "graphql-codegen-svelte-apollo": "^1.1.0", "postcss": "^8.4.21", "prettier": "^2.8.0", "prettier-plugin-svelte": "^2.8.1", "radash": "^11.0.0", "svelte": "^3.54.0", "svelte-check": "^3.0.1", "tailwindcss": "^3.2.7", "ts-node": "^10.9.1", "tslib": "^2.4.1", "typescript": "^4.9.5", "vite": "^4.0.0", "vitest": "^0.25.3", "zod": "^3.22.2"}, "type": "module", "dependencies": {"@apollo/client": "^3.7.15", "@dotenvx/dotenvx": "1.9.1", "@honeybadger-io/js": "^6.1.1", "@types/marked": "^4.0.8", "algoliasearch": "^4.15.0", "dotenv": "^16.3.1", "graphql": "^16.6.0", "marked": "^4.3.0", "nanoid": "^4.0.2", "sanitize-html": "^2.10.0", "svelte-icons": "^2.1.0", "swiper": "^11.1.4"}, "engines": {"node": "18.12.0"}}