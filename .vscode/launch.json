{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Dev Server",
      "type": "node-terminal",
      "command": "npm run dev",
      "request": "launch",
      "smartStep": true
    },
    {
      "name": "Build",
      "type": "node-terminal",
      "command": "npm run build",
      "request": "launch",
      "smartStep": true
    },
    {
      "name": "Chrome Browser",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:5173",
      "preLaunchTask": "run-vercel-dev", // @TODO: need script
      "postDebugTask": "stop-vercel-dev", // @TODO: need script
      "smartStep": true,
      "webRoot": "${workspaceFolder}"
    },
    {
      "name": "MS Edge Browser",
      "type": "msedge",
      "request": "launch",
      "url": "http://localhost:5173",
      "preLaunchTask": "run-vercel-dev", // @TODO: need script
      "postDebugTask": "stop-vercel-dev", // @TODO: need script
      "smartStep": true
    },
    //{
    //  "name": "Current Test File",
    //  "type": "node",
    //  "args": ["run", "${relativeFile}"],
    //  "autoAttachChildProcesses": true,
    //  "console": "integratedTerminal",
    //  "program": "${workspaceRoot}/node_modules/vitest/vitest.mjs",
    //  "request": "launch",
    //  "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
    //  "smartStep": true
    //},
    //{
    //  "name": "App Test Files",
    //  "type": "node",
    //  "args": ["src"],
    //  "console": "integratedTerminal",
    //  "internalConsoleOptions": "neverOpen",
    //  "program": "${workspaceRoot}/node_modules/vitest/vitest.mjs",
    //  "request": "launch",
    //  "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
    //  "smartStep": true
    //}
  ]
}
