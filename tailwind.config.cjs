/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	darkMode: 'class',
	theme: {
		extend: {
			colors: {
				'fr-navy': '#0C1E33',
				'fr-royal-blue': '#3553EF',
				'fr-charcoal': '#1E1E1E',
				'fr-white': '#ffffff',
				'fr-white-rgba': 'rgba(255, 255, 255, 0.7)',
				'fr-charcoal-rgba': 'rgba(30, 30, 30, 0.7)',
				'fr-light-gray': '#F2F2F2',
				'fr-orange': '#FC5A2B',
				'fr-orange-rgba': '	rgba(252, 90, 43, 0.7)',
				'fr-black': '#010101',
        		'fr-tru-grey': '#D9D9D9',
			},
			fontFamily: {
				'oswald': "'<PERSON>', sans-serif"
			},
			minWidth: {
				'60': "240px"
			}
		}
	},
	plugins: []
};
