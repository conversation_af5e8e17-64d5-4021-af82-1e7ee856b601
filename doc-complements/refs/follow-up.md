# Follow Up





## Importing webresources
**Webresource**: a dataset (csv file) which content should be translate it  into Wordpress custom posts (Experts), or custom Taxonomies (Expert Categories, Countries, Groups, Regions, States) or pages (Keyword Content pages).

The webresources importing takes place using the [WP All Import plugin](https://www.wpallimport.com/documentation/), and there are some templates defined for the most importing process such as:

- **Import_Existing_Experts_Template**: updates the data of the existing Experts, and creates new ones if for an specific row in the csv file a matching Expert does not exist in Wordpress.
- **Import_Existing_ExpertCategories_Template**: updates the data of the existing Expert Categories, and creates new ones if for an specific row in the csv file a matching Expert Category does not exist in Wordpress.
- **Import_New_Keyword_Pages**: creates Keyword Content Pages.


Heads up: the Templates expects an specific csv structure, so if the structure vary then the template should be adapted, including their custom functions.