# Software Design Overview





## Existing Setup
![existing_setup](../images/existing_setup.png)

## Current Setup
Please note in the current setup that actually this application is not on top of NextJS but Svelte/Svelte-kit instead.

![existing_setup](../images/new_setup.png)

### Wordpress
A Wordpress instance is used as a headless CMS. It is hosted in Siteground, as a sub-domain of the COAK domain there, which means you will need to ask about the specific URL to the Project Head for accessing the Admin Panel.
It synchronizes with the Algolia instance using a plugin called "WP Search with Algolia". The indexes synchronized are:
- Experts
- Expert Categories
- Expert Groups
- Expert Countries
- Expert Regions
- Expert States


>NOTE:
The synchronization should be triggered everytime a data related to the items above is created/updated/removed.
In the other hand, a routine (cron job) was implemented to do this task everyday at 12:00am. You can find it at src/routes/api/cron-experts-algolia

### Algolia
An Algolia instance is used to support the Search feature in the Customer site, providing a fast searching process across all the data indexed related to the items described in the section above.
The indexes are:
- **wp-posts_experts**: used for searching Experts (Search feature).
- **wp_terms_expert-category**, **wp_terms_expert-group**, **wp_terms_expert-country**, **wp_terms_expert-region**, **wp_terms_expert-state**: used to populate filtering options in the UI.

### Customer site (Svelte frontend)
Is a web application(this repository) hosted in Vercel, built on top of Svelte/svelte-kit.



