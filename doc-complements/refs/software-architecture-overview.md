# Software Architecture Overview





## Paradigms
The way component, classes, services, and functions were implemented was following a simple paradigm: separation of concerns.
There is no Domain Driven Design, also not a Modular Monolithic at all, just a simple way to connect dependencies following more like a Screaming Architecture with a well defined path:
- All the communication between the site and the external services/data-sources such as Algolia and Wordpress is made through the Internal Rest-API (routes/api), with their respective DTOs.
- All the communication between the internal api and Wordpress is using GraphQL. The client used to communicate with the GraphQL server in Wordpress is the built-in Fetch feature of NodeJS, using CodeGen to generate the Types from the Schema declaration.
- All the communication between the internal api and Algolia is using the built-in Fetch feature of NodeJS too.

### Code Structure
All the code structure was done following the paradigms declared above and the way <PERSON><PERSON><PERSON> organize the code structure by default:
- **src/lib**: contains all the solution layers(presentation, business logic, data-acces).
  - **server**: contains the server-side business logic and the data-access, including communication schema such as GraphQL fragments and queries.
  - **ui**: contains the UI view and the UI logic(components, state managements).
  - **services**: contains the UI communication layer (services that knows how to connect with the Internal API).
  - **shared**: contains all the common utilities used in the presentation layer and in the server-side (validation schemas, API DTOs, controlled response definitions)
- **src/routes**: contain all the routes (client side, server side/endpoints).
