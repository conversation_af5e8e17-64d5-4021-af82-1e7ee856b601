[<img src=".github/_images/free-referral.png" alt="Free Referral home page">](https://freereferral.com/)

# Free Referral Front End Repo 🏗️

Welcome! This is the repo which holds all the code related to [`freereferral.com`](https://freereferral.com)'s customer site. This repo is generated for an Svelte/Svelte-kit using SSR.

## 🎟️ Setup

1. Clone the repo:

```bash
git clone https://github.com/Cause-of-a-Kind/grow-therapy.git
```

2. Install dependencies:

```bash
npm i
```

3. Make sure you have the Vercel CLI [installed and authenticated](https://vercel.com/docs/cli).
<details>
<summary>Setup Vercel CLI</summary>
Install Vercel as a global package (as it maintains a local keystore across all of your projects):

```bash
npm install -g vercel
```

Vercel CLI requires you to log in and authenticate before accessing resources or performing administrative tasks:

```bash
$ vercel login
```
Answer the interactive questions:

```bash
vercel
? Set up and deploy "~/path/to/grow-therapy"? [Y/n] y
? Which scope do you want to deploy to? Cause of a Kind
? Link to existing project? [y/N] y
? What’s the name of your existing project? grow-therapy
🔗 Linked to cause-of-a-kind/grow-therapy (created .vercel and added it to .gitignore)
```

</details>

4. Pull environmental variables from Vercel:

```bash
vercel env pull .env.local
```

5. Start the development server:

```bash
vercel dev
```
<details>
<summary>Dev Server</summary>

The Vercel CLI is framework-agnostic. It calls the framework (Astro)-specific `build` and `dev` scripts set in `package.json`, so you cannot add the `vercel dev` command as an NPM script.

The Vercel development server requires Docker to run. It serves your dev environment from a container matching the Vercel deployment environment, and will serve Vercel Functions in dev.

</details>

6. Run the test watcher:

```bash
npm run test
```

## 🎯 Deployment

- The default Git branch on the repo is `main`. PRs should be opened against the production branch (`main`).

## 🪙 Branching and Deployment strategy

At this point there is no **branching strategy** to follow. Be sure to modify this section if that changes. So far all the branches(feature/fix) have being crated from the `main` branch and then merged back into it directly, without a PR.

The **deployment strategy** is simple: everytime a change is merged/pushed onto the `main` branch, it triggers a deployment process on [Vercel](https://vercel.com/cause-of-a-kind/free-referral-customer-preview).

## 🏆 Links to Project Details

- [Software design overview](./doc-complements/refs/software-design-overview.md)
- [Architecture overview](./doc-complements/refs/software-architecture-overview.md)
- [More](./doc-complements/refs/follow-up.md)

## 🔗 The Global Options page

This page is added via a code snippet in the default template for the WordPress install (currently twentytwentyfour, changes will be lost if the template is changed or upgraded).

```php
function coak_options_page() {
  acf_add_options_page( array(
    'page_title' => 'Global Settings',
    'menu_slug' => 'global-settings',
    'position' => 15,
    'redirect' => false,
  ));
}

add_action('acf/init',  'coak_options_page');
```

## 🔍 Email admin when author requests permission to publish

This page is added via a code snippet in the default template for the WordPress install (currently twentytwentyfour, changes will be lost if the template is changed or upgraded).

```php
function notify_admin_on_author_publish($new_status, $old_status, $post) {
  if ('post' !== $post->post_type) {
      return;
  }

  if ('pending' === $new_status) {
    $user = wp_get_current_user();
    if (in_array('author', $user->roles)) {
      // Set the admin email and subject
      $admin_email = get_option('admin_email');
      $subject = 'A post has been submitted by an Author';

      // Construct the message
      $message = sprintf(
        'A new post has been submitted by %s.' . "\n\n" .
        'Post Title: %s' . "\n" .
        'Post URL: %s',
        $user->display_name,
        $post->post_title,
        get_permalink($post->ID)
      );

      // Send the email
      wp_mail($admin_email, $subject, $message);
    }
  }
}

add_action('transition_post_status', 'notify_admin_on_author_publish', 10, 3);

function remove_author_publish_capability() {
  $role = get_role( 'author' );
  $role->remove_cap( 'publish_posts' );
}

add_action( 'init', 'remove_author_publish_capability' );
```

## 🔑 Indexing ACF fields with Algolia

This page is added via a code snippet in the default template for the WordPress install (currently twentytwentyfour, changes will be lost if the template is changed or upgraded).

```php
function wds_algolia_custom_fields( array $attributes, WP_Post $post ) {
  // Eligible post meta fields.
  $non_boolean_fields = [
    'canonical_url',
    'expert_id',
    'feature_image',
    'inactive_expert',
    'algolia_search_keyword',
    'page_type',
    'page_type',
    'permalink',
  ];

  // Loop over each field that's not a boolean type
  foreach ( $non_boolean_fields as $field ) {
    // Advanced Custom Fields.
    // @see https://www.advancedcustomfields.com/resources/get_field/
    $data = get_field( $field, $post->ID );

    // Only index when a field has content (non-boolean data types)
    if ( ! empty( $data )  ) {
      $attributes[ $field ] = $data;
    }

    if ( 'inactive_expert' == $field && empty( $data ) ) {
      $attributes[ $field ] = 0;
    }
  }

    $boolean_fields = [
      'inactive_expert',
    ];

  // Loop over each field that's not a boolean type
  foreach ( $boolean_fields as $field ) {
    // Advanced Custom Fields.
    // @see https://www.advancedcustomfields.com/resources/get_field/
    $data = get_field( $field, $post->ID );

    // Only index when a field has content (non-boolean data types)
    if ( ! empty( $data )  ) {
      $attributes[ $field ] = 1;
    }

  if ( 'inactive_expert' == $field && empty( $data ) ) {
    $attributes[ $field ] = 0;
  }
  }

  return $attributes;
}

add_filter( 'algolia_post_shared_attributes', 'wds_algolia_custom_fields', 10, 2 );
add_filter( 'algolia_searchable_post_shared_attributes', 'wds_algolia_custom_fields', 10, 2 );
```

## 📛 Known Issues

- Reindexing the Algolia database is resource intensive. Reindexes are triggered through the Algolia plugin in the WordPress backend.

## 🗨️ Contact

- Cause of a Kind <<EMAIL>>
