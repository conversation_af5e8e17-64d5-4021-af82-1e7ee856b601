import type { IDomainError, IResult, IResultProps } from "../_types-interfaces";

class Result<T extends IDomainError> implements IResult<T> {
  constructor(private _isSuccess: boolean, private _value: T) {}

  isSuccess = (): boolean => this._isSuccess;

  getValue = (): T => this._value;

  getResultProps = (): IResultProps<T> => {
    return {
      isSuccess: this._isSuccess,
      value: this._value,
      onErrorMessage:
        typeof this._value == 'string'
          ? this._value
          : this._getOnErrorMessage(),
    };
  };

  private _getOnErrorMessage = (): string => {
    return !this._isSuccess && this._value?.message ? this._value.message : '';
  };
}

export default Result;
