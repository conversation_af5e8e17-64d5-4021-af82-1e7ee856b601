import { CONTROLLED_ERROR_RESPONSE_CODES } from '../_constants';
import BaseControlledError from './base-controlled-error';

class InternalError<I> extends BaseControlledError<I> {
  constructor(errorReferenceValue: I, message?: string) {
    super(
      message ?? 'An internal error occurred',
      CONTROLLED_ERROR_RESPONSE_CODES.INTERNAL_ERROR,
      errorReferenceValue
    );
  }
}

export default InternalError;
