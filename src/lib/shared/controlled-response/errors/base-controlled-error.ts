import type { CONTROLLED_ERROR_RESPONSE_CODES } from "../_constants";
import type { IBaseControlledError } from "../_types-interfaces";

class BaseControlledError<E> extends Error implements IBaseControlledError<E> {
  constructor(
    message: string,
    private _errorCode: CONTROLLED_ERROR_RESPONSE_CODES,
    private _controlledErrorReference: E
  ) {
    super(message);
  }

  getErrorCode = (): CONTROLLED_ERROR_RESPONSE_CODES => this._errorCode;

  getControlledErrorReference = (): E => this._controlledErrorReference;

  getMessage = (): string => this.message;
}

export default BaseControlledError;
