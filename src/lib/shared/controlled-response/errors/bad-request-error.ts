import { CONTROLLED_ERROR_RESPONSE_CODES } from '../_constants';
import BaseControlledError from './base-controlled-error';

class BadRequestError<I> extends BaseControlledError<I> {
  constructor(errorReferenceValue: I, message?: string) {
    super(
      message ?? 'Bad Request',
      CONTROLLED_ERROR_RESPONSE_CODES.BAD_REQUEST,
      errorReferenceValue
    );
  }
}

export default BadRequestError;
