import { CONTROLLED_ERROR_RESPONSE_CODES } from '../_constants';
import BaseControlledError from './base-controlled-error';

class NotFoundError<I> extends BaseControlledError<I> {
  constructor(errorReferenceValue: I, message?: string) {
    super(
      message ?? 'Element not found',
      CONTROLLED_ERROR_RESPONSE_CODES.NOT_FOUND,
      errorReferenceValue
    );
  }
}

export default NotFoundError;
