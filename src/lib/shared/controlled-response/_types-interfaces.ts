import type { CONTROLLED_ERROR_RESPONSE_CODES } from "./_constants";

export interface IBaseEntityProps {
  id?: string | number;
}

export interface IDomainError {
  message?: string;
}

export interface IResultProps<T> {
  isSuccess: boolean;
  value: T;
  onErrorMessage?: string;
}

export interface IResult<T> {
  isSuccess: () => boolean;
  getValue: () => T;
  getResultProps: () => IResultProps<T>;
}

export interface IBaseControlledError<E> {
  getErrorCode: () => CONTROLLED_ERROR_RESPONSE_CODES;
  getControlledErrorReference: () => E;
  getMessage: () => string;
}
