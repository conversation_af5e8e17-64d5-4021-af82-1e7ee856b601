import type { z } from 'zod';
import type {BecomeExpertFormSchema} from '$lib/shared/validation/schemas/contact-forms/become-expert.schema';
import type {ContactUsFormSchema} from '$lib/shared/validation/schemas/contact-forms/contact-us.schema';
import type {RequestExpertFormSchema} from '$lib/shared/validation/schemas/contact-forms/request-expert.schema';
import type {BecomeAuthorFormSchema} from '$lib/shared/validation/schemas/contact-forms/become-contributing-author.schema';
import type SubmitArticleFormSchema from '$lib/shared/validation/schemas/contact-forms/submit-article.schema';

export enum GRAVITYFORMS_REF {
	REQUEST_AN_EXPERT = 'REQUEST_AN_EXPERT',
	REQUEST_THIS_EXPERT = 'REQUEST_THIS_EXPERT',
	BECOME_EXPERT = 'BECOME_EXPERT',
	BECOME_AUTHOR = 'BECOME_AUTHOR',
	SUBMIT_ARTICLE = 'SUBMIT_ARTICLE',
	CONTACT_US = 'CONTACT_US'
}

export type TRequestExpertFormFieldsDto = z.infer<typeof RequestExpertFormSchema>;

export type TBecomeExpertFormFieldsDto = z.infer<typeof BecomeExpertFormSchema>;

export type TContactUsFormFieldsDto = z.infer<typeof ContactUsFormSchema>;

export type TBecomeAuthorFormFieldsDto = z.infer<typeof BecomeAuthorFormSchema>;

export type TSubmitArticleFormFieldsDto = z.infer<typeof SubmitArticleFormSchema>;

// Input DTOs
export type TBaseContactFormInput = {
	readonly formRef: GRAVITYFORMS_REF;
};

export type TSubmitContactFormInput = TBaseContactFormInput & {
	readonly fields:
		| TRequestExpertFormFieldsDto
		| TBecomeExpertFormFieldsDto
		| TContactUsFormFieldsDto
		| TBecomeAuthorFormFieldsDto
		| TSubmitArticleFormFieldsDto;
};

export type TSubmitFormSuccessDto = {
	message: string;
	formFields:
		| TRequestExpertFormFieldsDto
		| TBecomeExpertFormFieldsDto
		| TContactUsFormFieldsDto
		| TBecomeAuthorFormFieldsDto
		| TSubmitArticleFormFieldsDto;
};

export type TSubmitFormErrorsDto = {
	isAValidationError: string;
	errorMessage:
		| string
		| {
				[key in keyof (
					| TRequestExpertFormFieldsDto
					| TBecomeExpertFormFieldsDto
					| TContactUsFormFieldsDto
					| TBecomeAuthorFormFieldsDto
					| TSubmitArticleFormFieldsDto
				)]: string;
		  };
	formFields: Partial<
		| TRequestExpertFormFieldsDto
		| TBecomeExpertFormFieldsDto
		| TContactUsFormFieldsDto
		| TBecomeAuthorFormFieldsDto
		| TSubmitArticleFormFieldsDto
	>;
};
