import type { TSeo } from "./base-dtos";
import type { TReducedCategoryDto } from "./category-dtos";
import { TReducedPageDto } from "./page-dtos";

// Output DTOs
export type TBaseExpertDto = {
    readonly title: string;
    readonly summary?: string;
};

export type TReducedExpertDto = TBaseExpertDto & {
    readonly id: string;
    readonly state: string;
    readonly region: string;
    readonly country: string;  
    readonly permalink: string;
    readonly permalinkSlugSegment: string;
    readonly inactiveExpert?: boolean | null;
}

export type TExtendedExpertDto = TReducedExpertDto & {
    readonly description?: string;
    readonly excerpt?: string;
    readonly imageUrl?: string | null;
    readonly categories?: TReducedCategoryDto[];
    readonly seo?: TSeo;
    readonly algoliaId?: string;
    readonly dbId?: number;
    readonly featureFlag?: string | null;
    readonly topicClusters?: TReducedPageDto[];
};

export type TPaginatedExpertCollection = {
    start: number;
    limit: number;
    total: number;
    items: TReducedExpertDto[] | TExtendedExpertDto[];
};

// Input DTOs
export type TGetExpertsInput = {
    readonly start?: number;
    readonly limit?: number;
    readonly criteria?: string;
    readonly expertNumber?: string;
    readonly category?: string[];
    readonly country?: string;
    readonly region?: string[];
    readonly state?: string[];
    readonly reduced?: boolean;
    readonly exactMatch?: boolean;
    readonly includeInactive?: boolean;
}