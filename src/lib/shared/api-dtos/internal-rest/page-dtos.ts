import type { TSeo } from "./base-dtos";

export enum PAGE_TYPES {
    STANDARD = "standard",
    SUBDIRECTORY = "subdirectory",
    KEYWORD = "keyword"
}

export const PAGES_TYPES_MAP: Map<string | PAGE_TYPES, PAGE_TYPES | string> = new Map([
    ['keyword', PAGE_TYPES.KEYWORD],
    [PAGE_TYPES.KEYWORD, 'keyword'],
]);

// PAGES
export type TReducedPageDto = {
    readonly title: string;
    readonly slug: string;
    readonly link?: string;
}

export type TExtendedPageDto = TReducedPageDto & {
    readonly imageUrl?: string;
    readonly description?: string;
    readonly author?: {email?: string};
    readonly seo?: TSeo;
    readonly pageType?: PAGE_TYPES;
    readonly algoliaSearchKeyword?: string | null;
    readonly content?: string | null;
    readonly subtitle?: string | null;
    readonly featuredImage?: {
        url?: string;
        altText?: string;
        caption?: string;
    };
}