// Global Settings
type TSublink = {
	sublinkLabel: string;
	sublinkUrl: string;
};

type TFooterLink = {
	heading: string;
	sublinks: TSublink[];
};

type TSocial = {
	icon: string;
	profileUrl: string;
};

type THeaderLink = {
	label: string;
	url: string
};

export type TTestimonialDto = {
	clientName: string;
	clientPosition: string;
	review: string;
};

export type THomepageCategoryDto = {
	slug: string;
	title: string;
	image: string;
};

export type THomepageSettingsDto = {
	categories?: THomepageCategoryDto[];
	testimonials?: TTestimonialDto[];
};

export type TGlobalSettingsDto = {
	companyInfo?: {
		address: string;
		email: string;
		fax: string;
		intl: string;
		phone: string;
		tollFree: string;
		copyRight?: string;
	};
	socials?: TSocial[];
	footerLinks?:  TFooterLink[];
	headerLinks?: THeaderLink[];
	
};
