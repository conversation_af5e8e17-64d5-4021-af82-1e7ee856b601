import { TSeo } from "./base-dtos";
import { TReducedCategoryDto } from "./category-dtos";

// Output DTOs
export type TBasePostDto = {
    readonly title: string;
    readonly summary?: string;
};

export type TReducedPostDto = TBasePostDto & {
    readonly id: string;
    readonly permalink: string;
    readonly permalinkSlugSegment: string;
    readonly category?: string | null;
    readonly thumbnailUrl?: string | null;
}

export type TExtendedPostDto = TReducedPostDto & {
    readonly description?: string;
    readonly excerpt?: string;
    readonly imageUrl?: string | null;
    readonly categories?: TReducedCategoryDto[];
    readonly seo?: Pick<TSeo, 'title' | 'description' | 'focuskw' | 'canonicalUrl' | 'isCanonical'> & { url?: string | null };
    readonly algoliaId?: string;
    readonly dbId?: number;
};

export type TTopicClusterBlogPostItemsDto = {
    readonly type: 'post';
    readonly items: TReducedPostDto[];
};