export enum SEO_REDIRECT_CODES {
    MOVED_PERMANENTLY = "301"
 };

 export enum TOPIC_CLUSTER_ITEMS_TYPE {
    POST = "post"
}


export type TSeoBreadcrumb = {
    text: string;
    url: string;
};

export type TSeo = {
    url: string;
    title?: string | null;
    description?: string | null;
    isCanonical?: boolean;
    canonicalUrl?: string | null;
    focuskw?: string | null;
    schema?: string | null;
    breadcrumbs?: TSeoBreadcrumb[];
    metaRobotsNoIndex?: 'noindex' | 'index' | null;
    metaRobotsNoFollow?: 'nofollow' | 'follow' | null
}

export type TSeoRedirectDto = {
    origin?: string | null;
    target?: string | null;
    status?: SEO_REDIRECT_CODES | null;
}


export type TModel = {
    content?: boolean;
    search?: boolean;
    searchingCriterias?: {
        criteria?: string;
    }
}

export type TImage = {
    title: string;
    sourceUrl: string;
    altText?: string;
}