import { TSeo } from "./base-dtos";

// BASE LOCATION
export type TBaseLocation = {
    readonly title: string;
    readonly slug: string;
    readonly link: string;
};
export type TLocationType = 'expert-country' | 'expert-region' | 'expert-state';
export type TExtendedLocationDto = TBaseLocation & {
    readonly locationType?: TLocationType,
    readonly description?: string;
	readonly seo?: TSeo;
};
// COUNTRY
export type TReducedCountryDto = TBaseLocation & unknown;
export type TExtendedCountryDto = TReducedCountryDto & {
    readonly description?: string;
	readonly seo?: TSeo;
};

// REGION
export type TReducedRegionDto = TBaseLocation & unknown;
export type TExtendedRegionDto = TReducedRegionDto & {
    readonly description?: string;
	readonly seo?: TSeo;
};;

// STATES
export type TReducedStateDto = TBaseLocation & unknown;
export type TExtendedStateDto = TReducedStateDto & {
    readonly description?: string;
	readonly seo?: TSeo;
};;

export type TLocationsDto = {
    countries?: TReducedCountryDto[];
    regions?: TReducedRegionDto[];
    states?: TReducedStateDto[];
}

