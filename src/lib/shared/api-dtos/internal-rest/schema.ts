import type { TExtendedCategoryDto, TReducedCategoryDto } from "./category-dtos";
import type { TSubmitFormErrorsDto, TSubmitFormSuccessDto } from "./contact-dto";
import type { TPaginatedExpertCollection } from "./expert-dtos";
import type { TExtendedCountryDto, TExtendedRegionDto, TExtendedStateDto, TReducedCountryDto, TReducedRegionDto, TReducedStateDto } from "./location-dtos";

/**
 * RESPONSES
 */

// Base Response Status
export type TResponseStatus = {
    success: boolean;
    message: string;
    code: string;
};

//Experts
export type TSearchExpertResponse = {
    status: TResponseStatus;
    values: TPaginatedExpertCollection;
};

//Categories
export type TGetCategoriesResponse = {
    status: TResponseStatus;
    values: TReducedCategoryDto[] | TExtendedCategoryDto[];
};

//Countries
export type TGetCountriesResponse = {
    status: TResponseStatus;
    values: TReducedCountryDto[] | TExtendedCountryDto[];
};

//Regions
export type TGetRegionsResponse = {
    status: TResponseStatus;
    values: TReducedRegionDto[] | TExtendedRegionDto[];
};

//States
export type TGetStatesResponse = {
    status: TResponseStatus;
    values: TReducedStateDto[] | TExtendedStateDto[];
};

// Content
export type TGetKeywordContentResponse = {
    status: TResponseStatus;
    values: string | null;
};

// Forms
export type TSubmitFormResponse = {
    status: TResponseStatus;
    values?: TSubmitFormSuccessDto | TSubmitFormErrorsDto
};