import type { TSeo } from './base-dtos';

export type TPageInfo = {
	pageInfo?: {
		endCursor: string | null | undefined;
		hasNextPage: boolean | undefined;
		hasPreviousPage: boolean | undefined;
	};
};

// EXPERT
export type TReducedCategoryDto = {
	title: string;
	slug: string;
	readonly uri?: string; // @TODO: Remove this
	readonly link?: string;
	readonly image?: string;
	readonly hierarchicalLevel?: number;
};

export type TExtendedCategoryDto = TReducedCategoryDto & {
	readonly description?: string;
	readonly subCategories?: Omit<TExtendedCategoryDto, 'subCategories'>[];
	readonly seo?: TSeo;
	readonly parent?: TReducedCategoryDto | null;
} & TPageInfo;
//TODO: Remove TPageInfo from the DTO.
