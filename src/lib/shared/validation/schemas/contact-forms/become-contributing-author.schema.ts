import { z } from 'zod';
import RequestFormBaseSchema from './base.schema';

// BECOME CONTRIBUTOR
export const BecomeAuthorFormSchema = RequestFormBaseSchema.omit({
	description: true
}).extend({
	company: z.string().optional().nullable(),
	phone: z.string().optional().nullable(),
	expertise: z.string().optional().nullable(),
	address: z
		.string({
			invalid_type_error: 'This field is required.',
			required_error: 'This field is required.'
		})
		.nonempty('This field is required.'),
	position: z
		.string({
			invalid_type_error: 'This field is required.',
			required_error: 'This field is required.'
		})
		.nonempty('This field is required.')
});
export const BecomeAuthorModelSchema = BecomeAuthorFormSchema.refine(
	(data) => data.email === data.confirmEmail,
	{
		message: 'Your emails do not match.',
		path: [ 'email']
	}
);


