import { z } from "zod";
import RequestFormBaseSchema from "./base.schema";

// CONTACT US
export const ContactUsFormSchema = RequestFormBaseSchema.extend({
    //optional fields
    company: z.string().optional().nullable(),
});
export const ContactUsModelSchema = ContactUsFormSchema.refine(
    (data) => data.email === data.confirmEmail,
	{
		message: 'Your emails do not match.',
		path: [ 'email']
	}
);