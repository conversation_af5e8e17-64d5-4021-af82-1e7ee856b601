import { z } from "zod";
import RequestFormBaseSchema from "./base.schema";

// REQUEST EXPERT
export const RequestExpertFormSchema = RequestFormBaseSchema.extend({
    //required fields
    position: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    }).nonempty('This field is required.'),
    typeOfCase: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    }).nonempty('This field is required.'),
    //optional fields
    expertLink: z.string().url().optional().nullable(),
    caseCaption: z.string().optional().nullable(),
    timeFrame: z.string().optional().nullable(),
    company: z.string().optional().nullable(),
});

export const RequestExpertModelSchema = RequestFormBaseSchema.refine(
	(data) => data.email === data.confirmEmail,
	{
		message: 'Your emails do not match.',
		path: [ 'email']
	}
);