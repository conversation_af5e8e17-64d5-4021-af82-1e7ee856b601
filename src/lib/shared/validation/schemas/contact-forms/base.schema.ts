import { z } from "zod";

// BASE DEFINITIONS
const EmailFieldsFormBaseSchema = z.object({
    email: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    })
    .email('Please supply a valid email address.')
    .trim().nonempty('This field is required.'),
    confirmEmail: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    })
    .email('Please supply a valid email address.')
    .trim().nonempty('This field is required.')
});

const RequestFormBaseSchema = EmailFieldsFormBaseSchema.extend({
    //required fields
    firstName: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    }).nonempty('This field is required.'),
    lastName: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    }).nonempty('This field is required.'),
    phone: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    }).nonempty('This field is required.'),
    description: z.string({
        invalid_type_error: 'This field is required.',
        required_error: 'This field is required.'
    }).nonempty('This field is required.'),
    //optional fields
    address: z.string().optional().nullable(),
    city: z.string().optional().nullable(),
    state: z.string().optional().nullable(),
    zipCode: z.string().optional().nullable(),
});

export default RequestFormBaseSchema;