import { z } from 'zod';
import RequestFormBaseSchema from './base.schema';

// BECOME EXPERT
export const BecomeExpertFormSchema = RequestFormBaseSchema.extend({
	//optional fields
	takePlaintiff: z.string().optional().nullable()
});

export const BecomeExpertModelSchema = BecomeExpertFormSchema.refine(
	(data) => data.email === data.confirmEmail,
	{
		message: 'Your emails do not match.',
		path: [ 'email']
	}
);
