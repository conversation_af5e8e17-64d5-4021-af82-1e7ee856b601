import { z } from 'zod';
import RequestFormBaseSchema from './base.schema';

// SUBMIT ARTICLE
const SubmitArticleFormSchema = RequestFormBaseSchema.omit({
	phone: true,
	description: true,
	address: true,
	city: true,
	state: true,
	zipCode: true
}).extend({
	title: z.string().optional().nullable(),
	excerpt: z.string().optional().nullable(),
	content: z.string().optional().nullable(),
	body1: z.string().optional().nullable(),
	body2: z.string().optional().nullable(),
	body3: z.string().optional().nullable(),
	conclusion: z.string().optional().nullable(),
	canonicalTag: z.string().optional().nullable(),
	copyright: z
		.string({
			required_error: 'This field is required.'
		})
		.nonempty('This field is required.')
});

SubmitArticleFormSchema.refine(
	(data) => data.email === data.confirmEmail,
	'Your emails do not match.'
);

export default SubmitArticleFormSchema;
