import { z } from "zod";

const validCoercedStringSchema = z.coerce.string();

const validNoNullStringSchema = validCoercedStringSchema
    .refine((value) => value !== "null", {
        message: "The value 'null' is not allowed.",
});

const SearchExpertsModelSchema = z.object({
    criteria: z.nullable(validCoercedStringSchema),
    categories: z.array(validNoNullStringSchema).default([]),
    country: z.nullable(validCoercedStringSchema),
    regions: z.array(validNoNullStringSchema).default([]),
    states: z.array(validNoNullStringSchema).default([]),
    exactMatch: z.boolean(
        {
            invalid_type_error: 'The exact match parameter should be a boolean.'
        }
    ),
    expertNumber: z.optional(z.nullable(validCoercedStringSchema)),
});

export default SearchExpertsModelSchema;