import { writable, type Writable } from 'svelte/store';

export const createWritablePersistStore = <T>(
	key: string,
	value: T
): Writable<T> & { useLocalStorage: () => void } => {
	const { set, subscribe, update } = writable<T>(value);
	const useLocalStorage = () => {
		const value = localStorage.getItem(key);
		if (value) {
			set(JSON.parse(value));
		}

		subscribe((current) => {
			localStorage.setItem(key, JSON.stringify(current));
		});
	};

	return {
		set,
		subscribe,
		update,
		useLocalStorage
	};
};

export const createWritableStore = <T>(value: T) => {
	return writable<T>(value);
};
