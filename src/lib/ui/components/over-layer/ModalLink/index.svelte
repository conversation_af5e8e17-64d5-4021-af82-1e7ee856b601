<script lang="ts">
	import Button from '../../form-elements/Button.svelte';
	import CleanIcon from '../../system-design/icons/CleanIcon.svelte';
	import Tooltip from '../Tooltip/index.svelte';
	import IoIosHelpCircleOutline from 'svelte-icons/io/IoIosHelpCircleOutline.svelte';
  
	export let linkLabel: string = 'See Modal';
    export let linkHelpMessage: string = '';
    export let modalTitle: string = '';
    export let useCloseButton = true;
	let shareBoxVisible = false;

	const toggleBoxVisible = () => {
		shareBoxVisible = !shareBoxVisible;
	};
  </script>


  	<div class={`modal-link flex items-center m-0 mr-4 ${$$props.class}`}>
		<Button
			color="Link"
			class={`modal-link__see-modal-cta mx-0 capitalize max-w-max self-center px-1 lg:w-auto`}
			on:click={toggleBoxVisible}
		>
			{linkLabel}
		</Button>
        {#if linkHelpMessage}
            <Tooltip
                title={linkHelpMessage}
                class={'modal-link__help-message content-center'}
            >
                <div
                    class={`modal-link__help-message__icon h-5 w-5 ml-0 cursor-help text-fr-charcoal dark:text-fr-white`}
                >
                    <IoIosHelpCircleOutline />
                </div>
            </Tooltip>
        {/if}
		

		<!-- Overlay with tooltip -->
		{#if shareBoxVisible}
			<!-- svelte-ignore a11y-click-events-have-key-events -->
			<div class="modal-link__overlay bg-fr-white-rgba dark:bg-fr-charcoal-rgba" on:click={toggleBoxVisible}>
				<div class="modal-link__overlay__tooltip bg-fr-white dark:bg-fr-charcoal flex flex-col" on:click|stopPropagation>
                    <div class="flex border-b-[1px] border-b-gray-500 justify-between items-center">
                        <h3 class="modal-link__overlay__tooltip__title dark:text-fr-white">{modalTitle}</h3>
                        {#if useCloseButton}
                            <div on:click={toggleBoxVisible} class="cursor-pointer">
                                <CleanIcon class={'text-fr-royal-blue'} />
                            </div>
                        {/if}
                    </div>
                    <div class="modal-link__overlay__tooltip__container flex flex-col overflow-y-auto mt-2">
                        {#if $$slots.content}
                            <slot name="content"/>
                        {/if}
                    </div>
					
				</div>
			</div>
		{/if}
	</div>


  <style>
	.modal-link__overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		backdrop-filter: blur(8px);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1999; /* Ensure it's above other elements */
	}
	.modal-link__overlay__tooltip {
		border: 1px solid #ddd;
		padding: 0 20px 20px 20px;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		z-index: 2000;
		border-radius: 8px;
        max-height: 95%;
	}
    .modal-link__overlay__tooltip__close-modal-cta {
        font-weight: 300;
    }
	.tooltip-buttons {
		margin-top: 10px;
		display: flex;
		align-items: center;
	}
	.link {
		margin-top: 10px;
	}
	.cta-separator {
		margin: 0 10px;
	}
  </style>
  