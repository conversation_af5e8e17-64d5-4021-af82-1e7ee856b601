<script lang="ts">
	import SmallText from "../../system-design/texts/SmallText.svelte";

	export let title = '';
	let isHovered = false;
	let x: number | null = null;
	let y: number | null = null;;
	
	function mouseOver(event: MouseEvent) {
		isHovered = true;
		x = event.pageX + 5;
		y = event.pageY + 5;
	}
	function mouseMove(event: MouseEvent) {
		x = event.pageX + 5;
		y = event.pageY + 5;
	}
	function mouseLeave() {
		isHovered = false;
	}
</script>

<!-- svelte-ignore a11y-mouse-events-have-key-events -->
<div class={`Tooltip ${$$props.class} dark:bg-fr-charcoal`}
	on:mouseover={mouseOver}
    on:mouseleave={mouseLeave}
	on:mousemove={mouseMove}
>
	<slot />

    {#if isHovered}
        <div style="top: {y}px; left: {x}px;" class="Tooltip__container border-fr-charcoal bg-fr-white dark:border-fr-white dark:bg-fr-charcoal">
            <SmallText>{title}</SmallText>
        </div>
    {/if}
</div>

<style>
	.Tooltip__container{
        @apply border-[2px] p-2 absolute max-w-[300px];
		box-shadow: 1px 1px 1px #ddd;
		border-radius: 4px;
	}
</style>