<script lang="ts">
	import { createEventDispatcher } from "svelte";
	import H2 from "../../system-design/headings/H2.svelte";
	import CleanIcon from "../../system-design/icons/CleanIcon.svelte";
	import type { TModalEventDispatcher } from "./_types-interfaces";
	import Button from "../../form-elements/Button.svelte";

    export let isOpened = false;
    export let title: string | null = null;
    export let useClose = true;
    const dispatch = createEventDispatcher<TModalEventDispatcher>();

    const _onClose = () => {
        if(useClose) {
            isOpened = false;
            dispatch('onClose', { isOpened }, { cancelable: true });
        }
    }
</script>

<div
    class={`modal--${isOpened ? 'opened visible' : 'closed hidden'} w-full h-full fixed top-0 left-0 z-10 flex bg-fr-white-rgba dark:bg-fr-charcoal-rgba`} 
    hidden={!isOpened}
>
    <!-- Overlay with tooltip -->
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <div class="modal__overlay bg-fr-white-rgba dark:bg-fr-charcoal-rgba" on:click={_onClose}>
        <div class="modal__overlay__tooltip bg-fr-white dark:bg-fr-charcoal flex flex-col" on:click|stopPropagation>
            <div class="flex border-b-[1px] border-b-gray-500 justify-between items-center">
                <h3 class="modal__overlay__tooltip__title dark:text-fr-white">{title}</h3>
                {#if useClose}
                    <div on:click={_onClose} class="cursor-pointer">
                        <CleanIcon class={'text-fr-royal-blue'} />
                    </div>
                {/if}
            </div>
            <div class="modal__overlay__tooltip__container flex flex-col overflow-y-auto mt-2">
                {#if $$slots.content}
                    <slot name="content"/>
                {/if}
            </div>
        </div>
    </div>
</div>

<style>
	.modal__overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		backdrop-filter: blur(8px);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1999; /* Ensure it's above other elements */
	}
	.modal__overlay__tooltip {
		border: 1px solid #ddd;
		padding: 0 20px 20px 20px;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		z-index: 2000;
		border-radius: 8px;
        max-height: 95%;
	}
  </style>