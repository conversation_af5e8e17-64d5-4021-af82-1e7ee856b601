<script lang="ts">
	import { onMount } from 'svelte';
	import Button from '../../form-elements/Button.svelte';
	import Tooltip from '../Tooltip/index.svelte';
	import IoIosHelpCircleOutline from 'svelte-icons/io/IoIosHelpCircleOutline.svelte';
	import CleanIcon from '../../system-design/icons/CleanIcon.svelte';
	
	export let title: string = '';
	export let link: string = '';
	export let label: string = '';
	export let sharingMessage: string = '';
	let shareBoxVisible = false;

	const toggleBoxVisible = () => {
		shareBoxVisible = !shareBoxVisible;
	};
  
	const copyToClipboard = () => {
	  navigator.clipboard.writeText(link);
	  alert('Copied to clipboard!');
	  toggleBoxVisible();
	};
  
	const sendByEmail = () => {
	  window.location.href = `mailto:?subject=Share Search Results&body=Check out these search results: ${encodeURIComponent(link)}`;
	  toggleBoxVisible();
	};
  </script>


  	<div class="Sharelink flex items-center m-0 mr-4">
		<Button
			color="Link"
			class={`ExpertSearchBox__searchSection__state ${$$props.class} mx-0 capitalize max-w-max self-center px-1 lg:w-auto`}
			on:click={toggleBoxVisible}
		>
			{label}
		</Button>
		<Tooltip
			title={`
				By clicking over this link you will generate a shareable URL to view these search results. You 
				can copy it or just send it by email directly.
			`}
			class={'content-center'}
		>
			<div
				class={`Checkbox_information h-5 w-5 ml-0 cursor-help text-fr-charcoal dark:text-fr-white`}
			>
				<IoIosHelpCircleOutline />
			</div>
		</Tooltip>

		<!-- Overlay with tooltip -->
		{#if shareBoxVisible}
			<!-- svelte-ignore a11y-click-events-have-key-events -->
			<div class="overlay bg-fr-white-rgba dark:bg-fr-charcoal-rgba" on:click={toggleBoxVisible}>
				<div class="tooltip bg-fr-white dark:bg-fr-charcoal" on:click|stopPropagation>
					<div class="flex border-b-[1px] border-b-gray-500 justify-between items-center mb-2">
                        <h3 class="share-link__overlay__tooltip__title dark:text-fr-white">{title}</h3>
						<div on:click={toggleBoxVisible} class="cursor-pointer">
							<CleanIcon class={'text-fr-royal-blue'} />
						</div>
                    </div>

					<p class="text-fr-charcoal dark:text-fr-white">{sharingMessage}:</p>
					<p class="link text-fr-charcoal bg-gray-200 dark:text-fr-white dark:bg-gray-500 p-2"><strong>{link}</strong></p>
					<div class="tooltip-buttons">
						<Button
							color="Link"
							class="mx-0 capitalize max-w-max self-center px-1"
							on:click={copyToClipboard}
						>
							Copy to Clipboard
						</Button>

						<div class="cta-separator text-fr-charcoal dark:text-fr-white">|</div>

						<Button
							color="Link"
							class="mx-0 capitalize max-w-max self-center px-1"
							on:click={sendByEmail}
						>
							Send by Email
						</Button>
					</div>
				</div>
			</div>
		{/if}
	</div>


  <style>
	.overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		backdrop-filter: blur(8px);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1999; /* Ensure it's above other elements */
	}
	.tooltip {
		border: 1px solid #ddd;
		padding: 0 20px 20px 20px;
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		z-index: 2000;
		border-radius: 8px;
		max-width: 90%;
	}
	.tooltip-buttons {
		margin-top: 10px;
		display: flex;
		align-items: center;
	}
	.link {
		margin-top: 10px;
		text-wrap: wrap;
    	overflow-x: scroll;
	}
	.cta-separator {
		margin: 0 10px;
	}
  </style>
  