<script lang="ts">
	import type { TReducedPostDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';
	import SectionLayout from '../layouts/SectionLayout.svelte';
	import PageLayout from '../layouts/PageLayout.svelte';
	import BlogPostSummaryCard from '../cards/BlogPostSummaryCard.svelte';

	export let sectionTitle: string = '';
	export let relatedArticles: TReducedPostDto[] = [];
	let isHovered = false;
</script>

<SectionLayout class="TopicClusterRelatedArticles  justify-items-center place-content-center w-full">
    <PageLayout>
            <h2 class="text-fr-charcoal dark:text-fr-white text-3xl mb-2">
                {sectionTitle}
            </h2>
            <hr class="mb-6 border-fr-charcoal-rgba"/>
            <div class="grid justify-items-center place-content-center gap-6 grid-cols-1 md:grid-cols-2">
            {#each relatedArticles as article (article.id)}
                <BlogPostSummaryCard articleData={article} />
            {/each}
            </div>
    </PageLayout>
</SectionLayout>

<style lang="css">
	:global(.TopicClusterRelatedArticles h2) {
		font-family: Oswald;
		font-size: 1.5rem;
		font-weight: 700;
	}

	:global(.TopicClusterRelatedArticles h2::before) {
		@apply text-fr-charcoal-rgba mr-1;
		content: "# ";
	}
</style>

