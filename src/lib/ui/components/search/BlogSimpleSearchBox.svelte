<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { nanoid } from 'nanoid';

	import TextField from '../form-elements/inputs/TextField/index.svelte';
	import SectionLayout from '../layouts/SectionLayout.svelte';
	import { PostServiceClient } from '$lib/services/posts.service';
	import Button from '../form-elements/Button.svelte';
	import {
		CategoryServiceClient,
		type TCategoryServiceClient
	} from '$lib/services/category.service';
	import type { TReducedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
	import type {
		TFilterSelected,
		TAdvancedSearchFormFieldMap,
		TAdvancedSearchFieldData,
		TOnPostSearchBoxEventDispatcher
	} from './_types-interfaces';
	import type { TReducedPostDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';

	interface $$Slots {
		searchResults: { posts: TReducedPostDto[] };
	}

	const dispatch = createEventDispatcher<TOnPostSearchBoxEventDispatcher>();
	const categoryService: TCategoryServiceClient = CategoryServiceClient();
	const postService = PostServiceClient();

	/* Component Inputs */
	export let useCriteriaFilterReference: boolean = false;
	export let category: string | null = null;
	export let useCategoryFilterReference: boolean = false;
	export let useClearAll = false;
	export let useCustomSearchAction = false;
	export let useNoPadding = false;
	export let paginationLimit = 10;
	export let exactMatch = false;
	export let customPlaceholder: string | null = null;

	/* initialization */
	let posts: TReducedPostDto[] = [];
	let totalExperts = 0;
	let totalExpertsListed = 0;
	let start = 0;
	let categories: TReducedCategoryDto[] = [];
	let isSearching: boolean = false;
	let criteria: string | null = null;
	let filtersSelected: {
		criteria: TFilterSelected | null;
		category: TFilterSelected[] | null;
	} = {
		criteria: null,
		category: null
	};
	// Advanced Search Form and Filtering with Multi-Selection
	let advancedSearchFormDataMapped: {
		category: TAdvancedSearchFormFieldMap;
	} = _resetMultiSelectOptions();
	let advancedSearchFormDataFlatted: {
		category: TAdvancedSearchFieldData[] | null;
	} = {
		category: null
	};

	let filtersAsTags: TFilterSelected[] = [];
	$: _isFiltering = filtersSelected.criteria || filtersSelected.category?.[0]?.value;
	$: _isFilteringAdvanced = advancedSearchFormDataFlatted.category?.[0].value;
	$: {
		let _filtersAsTags: TFilterSelected[] = [];
		if (filtersSelected.criteria) _filtersAsTags.push(filtersSelected.criteria);
		if (filtersSelected.category) _filtersAsTags = [..._filtersAsTags, ...filtersSelected.category];
		filtersAsTags = _filtersAsTags;
		const _advancedSearchFormDataFlatted = {
			category: Array.from(advancedSearchFormDataMapped.category.values())
		};
		advancedSearchFormDataFlatted = _advancedSearchFormDataFlatted;
	}

	function _resetMultiSelectOptions() {
		const _defaultCategoryFieldId = `category-${nanoid(4)}`;
		return {
			category: new Map([
				[_defaultCategoryFieldId, { id: _defaultCategoryFieldId, value: category }]
			])
		};
	}

	const _syncAdvancedSearchingValues = () => {
		category = filtersSelected.category?.[0]?.value ?? null;

		//sync multi-select options
		const _advancedSearchFormDataMapped = _resetMultiSelectOptions();
		if (filtersSelected.category?.[0]?.value) {
			_advancedSearchFormDataMapped.category = new Map();
			filtersSelected.category.map((filterSelected) => {
				const { id, value } = filterSelected;
				_advancedSearchFormDataMapped.category.set(id, { id, value });
			});
		}
		advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
	};

	const _syncSearchingValues = async (hasToSyncAdvanced?: boolean) => {
		criteria = filtersSelected.criteria?.value ?? null;
		if (hasToSyncAdvanced) _syncAdvancedSearchingValues();
	};

	const _resetParameters = () => {
		posts = [];
		totalExperts = 0;
		totalExpertsListed = 0;
		start = 0;
		paginationLimit = 10;
	};

	const _checkSelectedFilters = () => {
		const _filtersSelected: {
			criteria: TFilterSelected | null;
			category: TFilterSelected[] | null;
		} = {
			criteria: null,
			category: null
		};

		if (criteria)
			_filtersSelected.criteria = {
				title: `By Criteria: ${criteria}`,
				value: criteria,
				id: 'criteria',
				// ignoreOnSearch: expertNumber !== null,
				hidden: !useCriteriaFilterReference
			};

		// Category Filter supports now Multi-select options
		if (advancedSearchFormDataFlatted.category?.[0].value) {
			const categoryFilters = advancedSearchFormDataFlatted.category.reduce<TFilterSelected[]>(
				(acc, category) => {
					const { id, value } = category;
					if (value)
						acc.push({
							title: `By Category: ${value}`,
							value,
							id,
							// ignoreOnSearch: expertNumber !== null,
							hidden: !useCategoryFilterReference
						});

					return acc;
				},
				[]
			);
			_filtersSelected.category = categoryFilters.length > 0 ? categoryFilters : null;
		}

		//Because Svelte's reactivity is based on assignments, using array methods won't automatically trigger updates.
		//A subsequent assignment is required to trigger the update.
		filtersSelected = _filtersSelected;
	};

	const _search = async (hasToCleanParameters?: boolean) => {
		if (!useCustomSearchAction) {
			isSearching = true;
			if (hasToCleanParameters) _resetParameters();
			else if (totalExpertsListed) start += paginationLimit;
			_checkSelectedFilters();

			setTimeout(async () => {
				const response = await postService.searchPosts({
					start,
					limit: paginationLimit,
					criteria,
					category: filtersSelected.category?.[0]
						? filtersSelected.category?.reduce<string[]>((acc, filterSelected) => {
								acc.push(filterSelected.value!);
								return acc;
						  }, [])
						: null,
					reduced: true,
					exactMatch
				});
				if (response) {
					const { items, total } = response;
					posts = [...posts, ...items];
					totalExpertsListed += items.length;
					totalExperts = total;
				}
				isSearching = false;
			}, 750);
		} else {
			_checkSelectedFilters();
			dispatch('onSearch', { searchValues: filtersSelected });
		}
	};

	const _resetFiltersSelected = async () => {
		filtersSelected = {
			criteria: null,
			category: null
		};

		//multi-select options
		advancedSearchFormDataMapped = _resetMultiSelectOptions();
	};

	const _clearAll = async () => {
		if (_isFiltering) {
			await _resetFiltersSelected();
			await _syncSearchingValues(true);
			await _search(true);
			goto('/search-blogs');
		}
	};

	const _getCategories = async () => {
		const response = await categoryService.getCategories();
		if (response) {
			categories = response;
		}
	};

	const _onCleanField = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;

		await _search(true);
	};

	const _onSubmit = async (event: any) => {
		_search(true);
	};

	onMount(async () => {
		await _search();
		await Promise.all([_getCategories()]);
	});

	const _onKeyUp = async (event: CustomEvent) => {
		criteria = event.detail.value;
	};
</script>

<SectionLayout class={`BlogSearchBox ${useNoPadding ? '' : 'pb-10'}`}>
	<form
		class={`BlogSearchBox__searchSectionForm flex flex-col w-full`}
		on:submit={_onSubmit}
		method="get"
		action="/search-blogs"
	>
		<div
			class="BlogSearchBox__searchSectionForm_fieldGroup flex flex-col w-full lg:flex-row lg:justify-between mx-auto"
		>
			<TextField
				useSearchIcon
				useCleanIcon
				class={`BlogSearchBox__searchSection__criteria border-[2px] w-full lg:w-11/12 ${
					useNoPadding ? '' : 'my-4'
				}`}
				placeholder={customPlaceholder ?? 'Enter your search terms here'}
				value={criteria}
				id="criteria"
				name="criteria"
				on:onCleanField={_onCleanField}
				on:onKeyUp={_onKeyUp}
			/>

			<a
				href={`/search-blogs${criteria ? `?criteria=${encodeURIComponent(criteria)}` : ''}`}
				title="Search Blogs"
				class="flex items-center"
			>
				<Button
					color="Primary"
					class="BlogSearchBox__searchSection__submitButton h-[46px] px-3 capitalize self-center w-full lg:w-auto lg:ml-1"
					type="button"
				>
					Search
				</Button>
			</a>
		</div>

		<div class="BlogSearchBox__searchSectionForm_fieldGroup flex flex-col w-full">
			<div
				class="BlogSearchBox__searchSectionForm_buttonGroup flex flex-col w-full justify-start lg:flex-row"
			>
				{#if useClearAll && _isFiltering}
					<Button
						color="Link"
						class="BlogSearchBox__searchSection__state mx-0 capitalize max-w-max self-center px-1 lg:w-auto lg:ml-4"
						on:click={_clearAll}
					>
						Clear All
					</Button>
				{/if}
			</div>
		</div>
	</form>
</SectionLayout>
