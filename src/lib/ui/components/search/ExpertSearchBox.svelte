<script lang="ts">
	import { page } from '$app/stores';

	import { createEventDispatcher, onDestroy, onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import { nanoid } from 'nanoid';
	import IoIosHelpCircleOutline from 'svelte-icons/io/IoIosHelpCircleOutline.svelte';

	import type { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
	import TextField from '../form-elements/inputs/TextField/index.svelte';
	import Checkbox from '../form-elements/inputs/Checkbox/index.svelte';
	import SectionLayout from '../layouts/SectionLayout.svelte';
	import { ExpertServiceClient, type TExpertServiceClient } from '$lib/services/expert.service';
	import Button from '../form-elements/Button.svelte';
	import SmallText from '../system-design/texts/SmallText.svelte';
	import type { TFieldTextOnKeyUpEventDispatcher } from '../form-elements/inputs/TextField/_types-interfaces';
	import SelectAutocomplete from '../form-elements/inputs/SelectAutocomplete/index.svelte';
	import Spinner from '../spinner/index.svelte';
	import {
		CategoryServiceClient,
		type TCategoryServiceClient
	} from '$lib/services/category.service';
	import type { TReducedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
	import {
		LocationServiceClient,
		type TLocationServiceClient
	} from '$lib/services/location.service';
	import H3 from '../system-design/headings/H3.svelte';
	import Tag from '../badges/tag/index.svelte';
	import type { TTagOnCloseEventDispatcher } from '../badges/tag/_types-interfaces';
	import type {
		TAdvancedSearchFormDataMapped,
		TAdvancedSearchFormDataFlatted,
		TFilterSelected,
		TFiltersSelected,
		TOnExpertSearchBoxEventDispatcher,
		TAdvancedSearchFieldData,
		TAdvancedSearchFormFieldMap,
		TExpertSearchFilterValue,

		TPreloadedData

	} from './_types-interfaces';
	import { ADVANCED_SEARCH_FORM_DATA_FIELDS } from './_constants';
	import Tooltip from '../over-layer/Tooltip/index.svelte';
	import ModalLink from '../over-layer/ModalLink/index.svelte';
	import { PUBLIC_SITE_URL } from '$env/static/public';
	import Link from '../form-elements/Link.svelte';
	import type { TReducedCountryDto, TReducedRegionDto, TReducedStateDto } from '$lib/shared/api-dtos/internal-rest/location-dtos';
	import { goto } from '$app/navigation';
	import {tick } from 'svelte/internal';


	interface $$Slots {
		searchResults: { experts: TReducedExpertDto[] };
	}

	const dispatch = createEventDispatcher<TOnExpertSearchBoxEventDispatcher>();
	const expertService: TExpertServiceClient = ExpertServiceClient();
	const categoryService: TCategoryServiceClient = CategoryServiceClient();
	const locationService: TLocationServiceClient = LocationServiceClient();

	/* Component Inputs */
	export let useCriteria = true;
	export let useCriteriaFixed = false;
	export let criteria: string | null = null;
	export let useCriteriaFilterReference: boolean = false;
	export let useCategory = false;
	export let category: TExpertSearchFilterValue = [null];
	export let useCategoryFilterReference: boolean = false;
	export let useCountry = false;
	export let country: string | null = null;
	export let useCountryFilterReference: boolean = false;
	export let useRegion = false;
	export let region: TExpertSearchFilterValue = [null];
	export let useRegionFilterReference: boolean = false;
	export let useState = false;
	export let state: TExpertSearchFilterValue = [null];
	export let useStateFilterReference: boolean = false;
	export let expertNumber: string | null = null;
	export let useExpertNumber = false;
	export let useExpertNumberFilterReference: boolean = false;
	export let useClearAll = false;
	export let useShareResults = false;
	export let useRequestExpertLink = false;
	export let useCustomSearchAction = false;
	export let useNoPadding = false;
	export let usePaginationIndicators = true;
	export let paginationLimit = 20;
	export let useExactMatch = false;
	export let exactMatch = false;
	export let criteriaCustomPlaceholder: string | null = null;
	export let formSubmitActionUrl: string | null ='/search';
	export let excludeFromUrlSearchParams: string[] = [];
	export let shouldUpdateUrlSearchParams = false;
	export let keyToForceInitialSearch: string | null = null;
	export let stopOnBeforeSubmitEvent = false;
	export let preloadedData: TPreloadedData | null = null;
	/* initialization */
	let experts: TReducedExpertDto[] = [];
	let totalExperts = 0;
	let totalExpertsListed = 0;
	let start = 0;
	let categories: TReducedCategoryDto[] = [];
	let countries: TReducedCountryDto[] = [];
	let regions: TReducedRegionDto[] = [];
	let states: TReducedStateDto[] = [];
	let isSearching: boolean = false;
	let filtersSelected: TFiltersSelected = {
		criteria: null,
		expertNumber: null,
		category: null,
		country: null,
		region: null,
		state: null
	};
	// Advanced Search Form and Filtering with Multi-Selection
	let advancedSearchFormDataMapped: TAdvancedSearchFormDataMapped = _resetMultiSelectOptions();
	let advancedSearchFormDataFlatted: TAdvancedSearchFormDataFlatted = {
		expertNumber: null,
		category: null,
		country: null,
		region: null,
		state: null
	};
	let filtersAsTags: TFilterSelected[] = [];
	let isInitialized = false;
	let previousKeyToForceInitialSearch: string | null = null;
	let fullFormActionUrl = `${formSubmitActionUrl}${_getQueryStringFromFilters()}`;
	$: _isFilteringByFacet =
		filtersSelected.expertNumber ||
		filtersSelected.category?.[0]?.value ||
		filtersSelected.country?.[0]?.value ||
		filtersSelected.region?.[0]?.value ||
		filtersSelected.state?.[0]?.value;
	$: _isFiltering =
		filtersSelected.criteria ||
		_isFilteringByFacet
	$: _useAdvancedSearch = useExpertNumber || useCategory || useCountry || useRegion || useState;
	$: _filteredWithResults = totalExperts > 0 && !isSearching;
	$: _filteredWithoutResults = totalExperts === 0 && filtersAsTags.length > 0 && !isSearching;
	$: _filteredDefaultState = !_filteredWithResults && !_filteredWithoutResults && !isSearching;
	$: {
		let _filtersAsTags: TFilterSelected[] = [];
		if (filtersSelected.criteria) _filtersAsTags.push(filtersSelected.criteria);
		if (filtersSelected.expertNumber) _filtersAsTags.push(filtersSelected.expertNumber);
		if (filtersSelected.category) _filtersAsTags = [..._filtersAsTags, ...filtersSelected.category];
		if (filtersSelected.country) _filtersAsTags = [..._filtersAsTags, ...filtersSelected.country];
		if (filtersSelected.region) _filtersAsTags = [..._filtersAsTags, ...filtersSelected.region];
		if (filtersSelected.state) _filtersAsTags = [..._filtersAsTags, ...filtersSelected.state];
		filtersAsTags = _filtersAsTags;
		if(formSubmitActionUrl && filtersAsTags[0]) {
			fullFormActionUrl = `${formSubmitActionUrl}${_getQueryStringFromFilters()}`;
		}
		
		const _advancedSearchFormDataFlatted = {
			expertNumber: advancedSearchFormDataMapped.expertNumber,
			category: Array.from(advancedSearchFormDataMapped.category.values()),
			country: Array.from(advancedSearchFormDataMapped.country.values()),
			region: Array.from(advancedSearchFormDataMapped.region.values()),
			state: Array.from(advancedSearchFormDataMapped.state.values())
		};
		advancedSearchFormDataFlatted = _advancedSearchFormDataFlatted;
	}
	// Force component to trigger the initial Search when the component is reused(no re-mounted) during client side navigation
	// between pages with the same layout structure.
	$: (async() => {
			await tick();
			if (
				isInitialized && keyToForceInitialSearch && previousKeyToForceInitialSearch &&
				keyToForceInitialSearch !== previousKeyToForceInitialSearch && !isSearching
			)  {
				advancedSearchFormDataMapped = _resetMultiSelectOptions();
				await tick();
				previousKeyToForceInitialSearch = keyToForceInitialSearch;
				await initialSearch();
			}
		}
	)();

	// @TODO: Remove this from here and move it to the Parent Component. It is Parent's responsibility to run this as a side-effect.
	let searchingPath: string = '';
	// Sticky Search input/button
	const isCriteriaInputFixed = writable(false);
	const isFilteringPanelFixed = writable(false);

	function _resetMultiSelectOptions() {
		const categoriesFormFieldsMap: TAdvancedSearchFormFieldMap = new Map();
		const statesFormFieldsMap: TAdvancedSearchFormFieldMap = new Map();
		const regionsFormFieldsMap: TAdvancedSearchFormFieldMap = new Map();
		if(category.length === 0) category.push(null);
		for (const element of category) {
			const id = `category-${nanoid(4)}`;
			categoriesFormFieldsMap.set( id, {id, value: element});
		}
		const _defaultCountryFieldId = `country-${nanoid(4)}`;
		if(region.length === 0) region.push(null);
		for (const element of region) {
			const id = `region-${nanoid(4)}`;
			regionsFormFieldsMap.set( id, {id, value: element});
		}
		if(state.length === 0) state.push(null);
		for (const element of state) {
			const id = `state-${nanoid(4)}`;
			statesFormFieldsMap.set( id, {id, value: element});
		}
		
		return {
			expertNumber: null,
			category: categoriesFormFieldsMap,
			country: new Map([[_defaultCountryFieldId, { id: _defaultCountryFieldId, value: country }]]),
			region: regionsFormFieldsMap,
			state: statesFormFieldsMap
		};
	}

	const _syncAdvancedSearchingValues = () => {
		expertNumber = filtersSelected.expertNumber?.value ?? null;
		category = filtersSelected.category?.[0]?.value ? [filtersSelected.category[0].value] : [null];
		country = filtersSelected.country?.[0]?.value ?? null;
		region = filtersSelected.region?.[0]?.value ? [filtersSelected.region[0].value] : [null];
		state = filtersSelected.state?.[0]?.value ? [filtersSelected.state[0].value] : [null];

		//sync multi-select options
		const _advancedSearchFormDataMapped = _resetMultiSelectOptions();
		if (filtersSelected.category?.[0]?.value) {
			_advancedSearchFormDataMapped.category = new Map();
			filtersSelected.category.map((filterSelected) => {
				const { id, value } = filterSelected;
				_advancedSearchFormDataMapped.category.set(id, { id, value });
			});
		}
		if (filtersSelected.state?.[0]?.value) {
			_advancedSearchFormDataMapped.state = new Map();
			filtersSelected.state.map((filterSelected) => {
				const { id, value } = filterSelected;
				_advancedSearchFormDataMapped.state.set(id, { id, value });
			});
		}
		if (filtersSelected.region?.[0]?.value) {
			_advancedSearchFormDataMapped.region = new Map();
			filtersSelected.region.map((filterSelected) => {
				const { id, value } = filterSelected;
				_advancedSearchFormDataMapped.region.set(id, { id, value });
			});
		}
		if (filtersSelected.country?.[0]?.value) {
			_advancedSearchFormDataMapped.country = new Map();
			filtersSelected.country.map((filterSelected) => {
				const { id, value } = filterSelected;
				_advancedSearchFormDataMapped.country.set(id, { id, value });
			});
		}
		advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
	};

	const _syncSearchingValues = async (hasToSyncAdvanced?: boolean) => {
		criteria = filtersSelected.criteria?.value ?? null;
		if (hasToSyncAdvanced) _syncAdvancedSearchingValues();
	};

	const _resetParameters = () => {
		experts = [];
		totalExperts = 0;
		totalExpertsListed = 0;
		start = 0;
		paginationLimit = 20;
	};

	const _checkSelectedFilters = () => {
		const _filtersSelected: TFiltersSelected = {
			criteria: null,
			expertNumber: null,
			category: null,
			country: null,
			region: null,
			state: null
		};
		
		if (criteria)
			_filtersSelected.criteria = {
				title: `By Criteria: ${criteria}`,
				value: criteria,
				id: 'criteria',
				ignoreOnSearch: expertNumber !== null,
				hidden: !useCriteriaFilterReference
			};
		if (expertNumber)
			_filtersSelected.expertNumber = {
				title: `By Expert Number: ${expertNumber}`,
				value: expertNumber,
				id: 'expertNumber',
				hidden: !useExpertNumberFilterReference
			};
		// Category/State/Region Filters supports now Multi-select options
		if (advancedSearchFormDataFlatted.category?.[0]?.value && !expertNumber) {
			const categoryFilters = advancedSearchFormDataFlatted.category.reduce<TFilterSelected[]>(
				(acc, category) => {
					const { id, value } = category;
					if (value)
						acc.push({
							title: `By Category: ${value}`,
							value,
							id,
							ignoreOnSearch: expertNumber !== null,
							hidden: !useCategoryFilterReference
						});

					return acc;
				},
				[]
			);
			_filtersSelected.category = categoryFilters.length > 0 ? categoryFilters : null;
		}
		if (advancedSearchFormDataFlatted.state?.[0].value && !expertNumber) {
			const stateFilters = advancedSearchFormDataFlatted.state.reduce<TFilterSelected[]>(
				(acc, state) => {
					const { id, value } = state;
					if (value)
						acc.push({
							title: `By State: ${value}`,
							value,
							id,
							ignoreOnSearch: expertNumber !== null,
							hidden: !useStateFilterReference
						});

					return acc;
				},
				[]
			);
			_filtersSelected.state = stateFilters.length > 0 ? stateFilters : null;
		}
		if (advancedSearchFormDataFlatted.region?.[0].value && !expertNumber) {
			const regionFilters = advancedSearchFormDataFlatted.region.reduce<TFilterSelected[]>(
				(acc, region) => {
					const { id, value } = region;
					if (value)
						acc.push({
							title: `By Region: ${value}`,
							value,
							id,
							ignoreOnSearch: expertNumber !== null,
							hidden: !useRegionFilterReference
						});

					return acc;
				},
				[]
			);
			_filtersSelected.region = regionFilters.length > 0 ? regionFilters : null;
		}
		if (advancedSearchFormDataFlatted.country?.[0].value && !expertNumber) {
			const countryFilters = advancedSearchFormDataFlatted.country.reduce<TFilterSelected[]>(
				(acc, country) => {
					const { id, value } = country;
					if (value)
						acc.push({
							title: `By Country: ${value}`,
							value,
							id,
							ignoreOnSearch: expertNumber !== null,
							hidden: !useCountryFilterReference
						});

					return acc;
				},
				[]
			);
			_filtersSelected.country = countryFilters.length > 0 ? countryFilters : null;
		}

		//Because Svelte's reactivity is based on assignments, using array methods won't automatically trigger updates.
		//A subsequent assignment is required to trigger the update.
		filtersSelected = _filtersSelected;
	};

	const _search = async (hasToCleanParameters?: boolean) => {
		if (!useCustomSearchAction) {
			isSearching = true;
			if (hasToCleanParameters) _resetParameters();
			else if (totalExpertsListed) start += paginationLimit;
			_checkSelectedFilters();

			const category = filtersSelected.category?.[0]
				? filtersSelected.category?.reduce<string[]>((acc, filterSelected) => {
						acc.push(filterSelected.value!);
						return acc;
					}, [])
				: null;
			
			const state = filtersSelected.state?.[0]
				? filtersSelected.state?.reduce<string[]>((acc, filterSelected) => {
						acc.push(filterSelected.value!);
						return acc;
					}, [])
				: null;

			const region = filtersSelected.region?.[0]
				? filtersSelected.region?.reduce<string[]>((acc, filterSelected) => {
						acc.push(filterSelected.value!);
						return acc;
					}, [])
				: null;
			
			const country = filtersSelected.country?.[0]
				? filtersSelected.country?.reduce<string[]>((acc, filterSelected) => {
						acc.push(filterSelected.value!);
						return acc;
					}, [])
				: null;
				
			// Call to search experts if at least one filter is applied or if there are experts listed already.
			// Having experts listed without empty filters corresponds to a case where:
			// - preloaded data was passed when the component was mounted
			// This means we want the user be able to trigger a search when clicks over the "Load More" CTA
			if(criteria || category || country || region || state || expertNumber || experts[0]) {
				setTimeout(async () => {
					const response = await expertService.searchExperts({
						start,
						limit: paginationLimit,
						criteria,
						category,
						country,
						region,
						state,
						expertNumber,
						reduced: true,
						exactMatch,
						includeInactive: false
					});
					if (response) {
						const { items, total } = response;
						experts = [...experts, ...items];
						totalExpertsListed += items.length;
						totalExperts = total;
					}
					isSearching = false;
				}, 750);
			} else {
				experts = [];
				totalExpertsListed = 0;
				totalExperts = 0;
				isSearching = false;
			}

			// Dispatch On Search
			await tick();
			dispatch('onSearch', { searchValues: filtersAsTags });
		} else {
			_checkSelectedFilters();
			dispatch('onSearch', { searchValues: filtersAsTags });
		}
	};

	const _resetFiltersSelected = async () => {
		filtersSelected = {
			...filtersSelected,
			expertNumber: null,
			category: null,
			country: null,
			region: null,
			state: null
		};

		//multi-select options
		advancedSearchFormDataMapped = _resetMultiSelectOptions();
	};

	const _clearAll = async () => {
		if (_isFiltering) {
			await _resetFiltersSelected();
			await _syncSearchingValues(true);
			await _search(true);
		}
		_updateUrlSearchParams();
	};

	const _onSearchingFieldPressKey = async (
		event: CustomEvent<TFieldTextOnKeyUpEventDispatcher>,
		callback: (value: string | null) => Promise<void>,
		callToSearchOnEnter?: boolean
	) => {
		const { keyCode, value } = event.detail;
		let _value = value;
		let _shouldSearch = false;
		switch (keyCode.toLowerCase()) {
			case 'escape':
				_value = null;
				_shouldSearch = true;
				break;
			case 'backspace':
				if (_value?.length == 0) {
					_value = null;
					_shouldSearch = true;
				}
				break;
			case 'enter':
				_shouldSearch = true;
				break;
		}
		await callback(_value);
		if(_shouldSearch && callToSearchOnEnter) await _advancedSearch();
	};

	const _getCategories = async () => {
		const response = await categoryService.getCategoriesByLevel('lvl1');
		if (response) {
			categories = response;
		}
	};

	const _getCountries = async () => {
		const response = await locationService.getCountries();
		if (response) {
			countries = response;
		}
	};

	const _getRegions = async () => {
		const response = await locationService.getRegions();
		if (response) {
			regions = response;
		}
	};

	const _getStates = async () => {
		const response = await locationService.getStates();
		if (response) {
			states = response;
		}
	};

	const _onCategoryChange = async (event: CustomEvent) => {
		if (!expertNumber) {
			const { value, fieldId } = event.detail;
			// multiple selection
			const _advancedSearchFormDataMapped = advancedSearchFormDataMapped;
			_advancedSearchFormDataMapped.category.set(fieldId, { id: fieldId, value });
			advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
			setTimeout(async() => {
				await _advancedSearch();
			}, 200);
		}
	};

	const _onCountryChange = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		// multiple selection
		const _advancedSearchFormDataMapped = advancedSearchFormDataMapped;
		_advancedSearchFormDataMapped.country.set(fieldId, { id: fieldId, value });
		advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
		setTimeout(async() => {
			await _advancedSearch();
		}, 200);
	};

	const _onRegionChange = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		// multiple selection
		const _advancedSearchFormDataMapped = advancedSearchFormDataMapped;
		_advancedSearchFormDataMapped.region.set(fieldId, { id: fieldId, value });
		advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
		setTimeout(async() => {
			await _advancedSearch();
		}, 200);
	};

	const _onStateChange = async (event: CustomEvent) => {
		if (!expertNumber) {
			const { value, fieldId } = event.detail;
			// multiple selection
			const _advancedSearchFormDataMapped = advancedSearchFormDataMapped;
			_advancedSearchFormDataMapped.state.set(fieldId, { id: fieldId, value });
			advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
			setTimeout(async() => {
				await _advancedSearch();
			}, 200);
		}
	};

	function _buildParamsAndUrlFromFilters(): [URLSearchParams, string] {
		const params = new URLSearchParams();
		// Check current filters selected
		for (const filterAsTag of filtersAsTags) {
			if(filterAsTag && filterAsTag.value && filterAsTag.id) {
				const {id: queryParameter, value: queryValue} = filterAsTag;
				let shouldAppend = true;
				if(queryParameter.toLowerCase().includes('country') || queryParameter.toLowerCase().includes('criteria') || queryParameter.toLowerCase().includes('expertNumber'))
					shouldAppend = false;

				if(shouldAppend) {
					const idSegments = queryParameter.split('-', 1);
					if(idSegments[0] && !excludeFromUrlSearchParams.includes(idSegments[0])) params.append(idSegments[0], queryValue);
				} else if(!excludeFromUrlSearchParams.includes(queryParameter)) {
					params.set(queryParameter, queryValue);
				}
			}
		}
		// Check Exact Match Filter
		if(criteria && exactMatch) params.set('exactMatch', 'true');
		const paramsToString = params.toString();
		const newPath = `${$page.url.pathname}${paramsToString ? '?' : ''}${paramsToString}`;
		if(useShareResults)
			searchingPath = paramsToString ? `${(PUBLIC_SITE_URL ?? '').replace(/\/$/, '')}${newPath}` : '';
		
		return [params, newPath];
	};

	function _getQueryStringFromFilters() {
		const [_params, newPath] = _buildParamsAndUrlFromFilters();
		const urlSegments = newPath.split('?');
		return urlSegments[1] ? `?${urlSegments[1]}` : '';
	}

	const _updateUrlSearchParams = () => {
		 // Run this function only if it is required to share the results
		 //@TODO: The URL should be passed to the Parent Component. Check TODO at the top of the variables declaration.
		setTimeout(() => {
			const [params, newPath] = _buildParamsAndUrlFromFilters();
			// trigger event before update URL Search Params
			dispatch('beforeUpdateUrlSearchParams', {newUrlSearchParams: params})
			// update the URL and navigate to if it was requested
			if(shouldUpdateUrlSearchParams) goto(newPath, { replaceState: false, keepFocus: true, noScroll: true })
		}, 100);
	};

	const _onCleanField = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;

		switch (fieldId) {
			case 'criteria':
				criteria=value;
				break;
			case 'country':
				country = value;
				break;
			case 'expertNumber':
				expertNumber = value;
				break;
		}
		await _search(true);
		_updateUrlSearchParams();
	};

	const _onExactMatchChange = (event: CustomEvent) => {
		event.stopPropagation();
		const { value, fieldId } = event.detail;
		if(exactMatch !== value) {
			exactMatch = value;
		}
		_updateUrlSearchParams();
	};

	const _onSubmit = async (event: any) => {
		event.preventDefault();
		_checkSelectedFilters();
		await tick();
		dispatch('beforeSubmit', { searchValues: filtersAsTags });
		await tick();
		if(stopOnBeforeSubmitEvent) return;

		await _search(true);
		_updateUrlSearchParams();
	};

	const _removeSelectedFilterOption = async (event: CustomEvent<TTagOnCloseEventDispatcher>) => {
		const { id } = event.detail;
		let _filtersSelected = filtersSelected;
		const field = (id as keyof TFiltersSelected).split('-', 1)[0] as ADVANCED_SEARCH_FORM_DATA_FIELDS;
		const idFieldsWithMultipleSelection = [ADVANCED_SEARCH_FORM_DATA_FIELDS.CATEGORY, ADVANCED_SEARCH_FORM_DATA_FIELDS.STATE, ADVANCED_SEARCH_FORM_DATA_FIELDS.REGION];
		if(idFieldsWithMultipleSelection.includes(field)) {
			const _filters = (_filtersSelected[field] as TFilterSelected[])?.filter((filter) => filter.id !== id);
			(_filtersSelected[field] as TFilterSelected[]) = _filters;
		} else _filtersSelected[field] = null;

		filtersSelected = _filtersSelected;
		await _syncSearchingValues(true);
		if (expertNumber === null) await _search(true);

		_updateUrlSearchParams();
	};

	const _advancedSearch = async () => {
		await _search(true);
		_updateUrlSearchParams();
	};

	const _toggleMultipleOption = (
		formDataField: ADVANCED_SEARCH_FORM_DATA_FIELDS,
		id: string | null,
	) => {
		if (
			formDataField in advancedSearchFormDataMapped &&
			formDataField !== ADVANCED_SEARCH_FORM_DATA_FIELDS.EXPERT_NUMBER
		) {
			const _advancedSearchFormDataMapped = advancedSearchFormDataMapped;
			if (id) _advancedSearchFormDataMapped[formDataField].delete(id);
			else {
				const _id = `${formDataField}-${nanoid(4)}`;
				_advancedSearchFormDataMapped[formDataField].set(_id, {
					id: _id,
					value: null
				});
			}
			advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
		}
	};

	const _handleScroll = () => {
		const scrollY = window.scrollY;
		scrollY >= 298 ? isCriteriaInputFixed.set(true) : isCriteriaInputFixed.set(false);
		scrollY >= 400 ? isFilteringPanelFixed.set(true) : isFilteringPanelFixed.set(false);
  	};

	const _onAddRemoveFields = async(totalElements: number, element: TAdvancedSearchFieldData, elementType: ADVANCED_SEARCH_FORM_DATA_FIELDS, index: number) => {
		const isRemoving = totalElements && index < totalElements - 1;
		_toggleMultipleOption(
			elementType,
			isRemoving ? element.id : null,
		);
		if(isRemoving && element.value) 
			setTimeout(async() => {
				await _advancedSearch()
			}, 100);
	};

	const loadFacetFilters = async() => {
		await Promise.all([_getCategories(), _getCountries(), _getRegions(), _getStates()]);
	}

	const initialSearch = async() => {
		_updateUrlSearchParams();
		await _search(true);
		await loadFacetFilters();
	};

	onMount(async() => {
		if(!isSearching) {
			isInitialized = true;
			previousKeyToForceInitialSearch = keyToForceInitialSearch;
			if(!preloadedData) await initialSearch();
			else { // Set with preloaded data
				const { items, total, start: _start, limit } = preloadedData;
				experts = [...experts, ...items];
				totalExpertsListed += items.length;
				totalExperts = total;
				start = _start;
				paginationLimit = limit;
				preloadedData = null;
				_checkSelectedFilters();
				await tick();
				loadFacetFilters();
			}
		}
	});
	onMount(() => {
		if (useCriteriaFixed || _useAdvancedSearch) window.addEventListener('scroll', _handleScroll);
		
		return () => {
			if(useCriteriaFixed || _useAdvancedSearch) window.removeEventListener('scroll', _handleScroll);
		};
	});
</script>

<SectionLayout class={`ExpertSearchBox ${useNoPadding ? '' : 'pb-10'}`}>
	<form
		class={`ExpertSearchBox__searchSectionForm flex flex-col w-full`}
		on:submit={_onSubmit}
	>
		{#if useCriteria}
			<div
				class={`ExpertSearchBox__searchSectionForm_fieldGroup flex flex-col w-full lg:flex-row lg:justify-between mx-auto 
						${$isCriteriaInputFixed && useCriteriaFixed ? 'fixed top-14 lg:top-16 bg-fr-white dark:bg-fr-charcoal' : ''}`}
			>
				<TextField
					useSearchIcon
					useCleanIcon
					class={`ExpertSearchBox__searchSection__criteria border-[2px] w-full lg:w-11/12 ${
						useNoPadding ? '' : 'my-4'
					}`}
					placeholder={criteriaCustomPlaceholder ?? "Enter your search terms here"}
					value={criteria}
					id="criteria"
					name="criteria"
					on:onCleanField={_onCleanField}
					on:onKeyUp={(event) => {
						_onSearchingFieldPressKey(event, async (value) => {
							criteria = value;
							if (!criteria) exactMatch = false;
						});
					}}
				/>
				<Button
					color="Primary"
					class="ExpertSearchBox__searchSection__submitButton hidden h-[46px] px-3 capitalize self-center w-full lg:w-2/12 lg:w-auto lg:ml-1 lg:block"
					type="button"
					on:click={_onSubmit}
				>
					Search
				</Button>
			</div>
			<div class="flex mb-8 justify-between items-baseline lg:hidden">
				{#if useExactMatch && useCriteria && criteria}
					<div class="items-center flex mr-4">
						<Checkbox
							id="exactMatch"
							label="Exact Match"
							value={(criteria && exactMatch ? true : false)}
							disabled={!criteria}
							on:onChange={_onExactMatchChange}
						/>
						<Tooltip
							title={`
								Exact Match tells our Search Engine to find the terms exactly as typed. An example of this is 'digital forensic'.
								Without exact match, the results produced are 'digital and forensic' in any order as long as both exist.
						`}
						>
							<div
								class={`Checkbox_information h-5 w-5 ml-1 cursor-help text-fr-charcoal dark:text-fr-white`}
							>
								<IoIosHelpCircleOutline />
							</div>
						</Tooltip>
					</div>
				{/if}
				{#if useRequestExpertLink}
					<div class="items-center flex mr-4">
						<Link linkUrl={`/request-expert-witness`} color="primary" uiType="link"
							class="ExpertSearchBox__searchSection__requestExpertCTA flex flex-col m-auto mr-0 capitalize max-w-max self-center content-end items-end no-underline"
						>
							<div class="capitalize underline">Request An Expert</div>
							<div class="normal-case font-thin">We will handle the search for you</div>
						</Link>
					</div>
				{/if}
			</div>
			<Button
				color="Primary"
				class="ExpertSearchBox__searchSection__submitButton h-[46px] px-3 capitalize self-center w-full lg:hidden"
				type="button"
				on:click={_onSubmit}
			>
				Search
			</Button>
		{/if}

		<div class="ExpertSearchBox__searchSectionForm_fieldGroup flex flex-col w-full">
			<div
				class="ExpertSearchBox__searchSectionForm_buttonGroup flex flex-wrap w-full justify-start items-baseline"
			>
				{#if useExactMatch && useCriteria && criteria}
					<div class="items-center mr-4 hidden lg:flex">
						<Checkbox
							id="exactMatch"
							label="Exact Match"
							value={(criteria && exactMatch ? true : false)}
							disabled={!criteria}
							on:onChange={_onExactMatchChange}
						/>
						<Tooltip
							title={`
								Exact Match tells our Search Engine to find the terms exactly as typed. An example of this is 'digital forensic'.
								Without exact match, the results produced are 'digital and forensic' in any order as long as both exist.
						`}
						>
							<div
								class={`Checkbox_information h-5 w-5 ml-1 cursor-help text-fr-charcoal dark:text-fr-white`}
							>
								<IoIosHelpCircleOutline />
							</div>
						</Tooltip>
					</div>
				{/if}
				{#if useRequestExpertLink}
					<Link linkUrl={`/request-expert-witness`} color="primary" uiType="link"
						class="ExpertSearchBox__searchSection__requestExpertCTA hidden lg:flex lg:m-auto lg:mr-0 lg:flex-col h-auto max-w-max self-center content-end items-end no-underline"
					>
						<div class="capitalize underline">Request An Expert</div>
						<div class="normal-case font-thin">We will handle the search for you</div>
					</Link>
				{/if}
			</div>
		</div>

		{#if _isFiltering && filtersAsTags.length > 0}
			<div
				class="ExpertSearchBox__searchSectionForm_selectedFilters flex mt-8 mb-4 flex-wrap max-h-[200px] overflow-auto"
			>
				{#each filtersAsTags as { title, value, id, ignoreOnSearch, hidden }}
					{#if !hidden}
						<Tag
							{id}
							label={title}
							disabled={ignoreOnSearch}
							closable={true}
							on:onClose={_removeSelectedFilterOption}
						/>
					{/if}
				{/each}
			</div>
		{/if}

		<div class="flex">
			{#if _useAdvancedSearch && !_filteredDefaultState}
				<ModalLink 
					linkLabel='Add Filters'
					linkHelpMessage='Show Terms filters such as Expert Number, Expert Category, and Expert Location.'
					modalTitle="Filter By"
					class='ExpertSearchBox__searchSectionForm__addFiltersLink_modal lg:hidden'
				>
					<div 
						class={`ExpertSearchBox__searchSectionForm__addFiltersLink_modal__content flex-col w-60 min-w-60 mr-7`}
						slot="content"
					>
						<!-- FILTERING FORM -->
						<form
							class="ExpertSearchBox__advancedSearchSectionForm flex flex-col w-full pt-7"
						>
							{#if useExpertNumber}
								<div class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full">
									<TextField
										useCleanIcon
										class="ExpertSearchBox__advancedSearchSectionForm__expertNumber my-4 mt-2 border-[2px] w-full"
										value={expertNumber}
										label="Expert Number"
										id="expertNumber"
										name="expertNumber"
										on:onCleanField={_onCleanField}
										on:onKeyUp={(event) => {
											_onSearchingFieldPressKey(event, async (value) => {
												expertNumber = value;
												if (expertNumber) {
													category = [null];
													country = null;
													region = [null];
													state = [null];
												}
											}, true);
										}}
									/>
									<SmallText class="text-fr-charcoal text-[0.7rem] mt-[-10px]">
										*Filtering by the Expert Number aims to get only one result. It will invalidate the other
										filtering options.
									</SmallText>
								</div>
								<H3 class="w-full text-center">- or -</H3>
							{/if}
							<div class="ExpertSearchBox__advancedSearchSectionForm_fieldsContainer flex flex-col w-full">
								{#if useCategory && advancedSearchFormDataFlatted.category}
									<div
										class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
									>
										{#each advancedSearchFormDataFlatted.category as categoryField, index}
											<SelectAutocomplete
												value={!expertNumber ? categoryField.value : null}
												class="ExpertSearchBox__advancedSearchSectionForm__category mt-2 mb-1 mx-0 border-[2px] w-full"
												options={categories}
												id={categoryField.id}
												name={categoryField.id}
												label={index == 0 ? 'Category' : ''}
												disabled={expertNumber !== null}
												placeholder="Type or select an option"
												on:onChange={_onCategoryChange}
											/>
											<Button
												color="Link"
												class="ExpertSearchBox__searchSection__state mx-0 capitalize max-w-max px-1 pb-2 text-xs h-auto lg:w-auto"
												on:click={() => _onAddRemoveFields(
													advancedSearchFormDataFlatted.category?.length ?? 0,
													categoryField,
													ADVANCED_SEARCH_FORM_DATA_FIELDS.CATEGORY,
													index
												)}
											>
												{advancedSearchFormDataFlatted.category?.length && index == advancedSearchFormDataFlatted.category.length - 1 ? '+Add' : '-Remove'}
											</Button>
										{/each}
									</div>
								{/if}

								{#if useState && advancedSearchFormDataFlatted.state}
									<div
										class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
									>
										{#each advancedSearchFormDataFlatted.state as stateField, index}
											<SelectAutocomplete
												value={!expertNumber ? stateField?.value : null}
												class="ExpertSearchBox__advancedSearchSectionForm__state mt-2 mb-1 mx-0 border-[2px] w-full"
												options={states}
												id={stateField.id}
												name={stateField.id}
												label={index == 0 ? 'State' : ''}
												disabled={expertNumber !== null}
												placeholder="Type or select an option"
												on:onChange={_onStateChange}
											/>
											<Button
												color="Link"
												class="ExpertSearchBox__searchSection__state mx-0 capitalize max-w-max px-1 pb-2 text-xs h-auto lg:w-auto"
												on:click={() => _onAddRemoveFields(
													advancedSearchFormDataFlatted.state?.length ?? 0,
													stateField,
													ADVANCED_SEARCH_FORM_DATA_FIELDS.STATE,
													index
												)}
											>
												{index == advancedSearchFormDataFlatted.state.length - 1 ? '+Add' : '-Remove'}
											</Button>
										{/each}
									</div>
								{/if}
								{#if useRegion && advancedSearchFormDataFlatted.region}
									<div
										class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
									>
										{#each advancedSearchFormDataFlatted.region as regionField, index}
											<SelectAutocomplete
												value={!expertNumber ? regionField?.value : null}
												class="ExpertSearchBox__advancedSearchSectionForm__region mt-2 mb-1 mx-0 border-[2px] w-full"
												options={regions}
												id={regionField.id}
												name={regionField.id}
												label={index == 0 ? 'Region' : ''}
												disabled={expertNumber !== null}
												placeholder="Type or select an option"
												on:onChange={_onRegionChange}
											/>
											<Button
												color="Link"
												class="ExpertSearchBox__searchSection__region mx-0 capitalize max-w-max px-1 pb-2 text-xs h-auto lg:w-auto"
												on:click={() => _onAddRemoveFields(
													advancedSearchFormDataFlatted.region?.length ?? 0,
													regionField,
													ADVANCED_SEARCH_FORM_DATA_FIELDS.REGION,
													index
												)}
											>
												{index == advancedSearchFormDataFlatted.region.length - 1 ? '+Add' : '-Remove'}
											</Button>
										{/each}
									</div>
								{/if}
								{#if useCountry && advancedSearchFormDataFlatted.country}
									<div
										class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
									>
										{#each advancedSearchFormDataFlatted.country as countryField, index}
											<SelectAutocomplete
												value={!expertNumber ? countryField?.value : null}
												class="ExpertSearchBox__advancedSearchSectionForm__country mt-2 mb-1 mx-0 border-[2px] w-full"
												options={countries}
												id={countryField.id}
												name={countryField.id}
												label={index == 0 ? 'Country' : ''}
												disabled={expertNumber !== null}
												placeholder="Type or select an option"
												on:onChange={_onCountryChange}
											/>
										{/each}
									</div>
								{/if}
							</div>
						</form>
					</div>
				</ModalLink>
			{/if}
			{#if useClearAll && _isFilteringByFacet}
				<Button
					color="Link"
					class="ExpertSearchBox__searchSection__clarAllFiltersCTA mx-0 capitalize max-w-max px-1 mr-4"
					on:click={_clearAll}
				>
					Clear All Filters
				</Button>
			{/if}
		</div>
		
	</form>

	{#if $$slots.searchResults}
		<!-- FILTERING/RESULTS BLOCKS -->
		<div class="ExpertSearchBox__slot-container flex mt-8">
			<!-- ASIDE - FILTERING PANEL -->
			{#if _useAdvancedSearch && !_filteredDefaultState}
				<aside class={`ExpertSearchBox__slot-container__searchPanel hidden w-60 min-w-60 mr-7 lg:flex lg:flex-col 
							${$isFilteringPanelFixed ? 'aside-fixed top-14 lg:top-32 bg-fr-white dark:bg-fr-charcoal' : ''}`}>
					<!-- FILTERING PANEL HEADER -->
					<div class="flex pr-7 pb-3 pt-3 border-b-[1px] border-b-gray-500">
						<SmallText class="w-60 min-w-60">
							Filter By
						</SmallText>
					</div>
					<!-- FILTERING FORM -->
					<form
						class="ExpertSearchBox__advancedSearchSectionForm flex flex-col w-full pt-7"
					>
						{#if useExpertNumber}
							<div class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full">
								<TextField
									useCleanIcon
									class="ExpertSearchBox__advancedSearchSectionForm__expertNumber my-4 mt-2 border-[2px] w-full"
									value={expertNumber}
									label="Expert Number"
									id="expertNumber"
									name="expertNumber"
									on:onCleanField={_onCleanField}
									on:onKeyUp={(event) => {
										_onSearchingFieldPressKey(event, async (value) => {
											expertNumber = value;
											if (expertNumber) {
												category = [null];
												country = null;
												region = [null];
												state = [null];
											}
										}, true);
									}}
								/>
								<SmallText class="text-fr-charcoal text-[0.7rem] mt-[-10px]">
									*Filtering by the Expert Number aims to get only one result. It will invalidate the other
									filtering options.
								</SmallText>
							</div>
							<H3 class="w-full text-center">- or -</H3>
						{/if}
						<div class="ExpertSearchBox__advancedSearchSectionForm_fieldsContainer flex flex-col w-full">
							{#if useCategory && advancedSearchFormDataFlatted.category}
								<div
									class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
								>
									{#each advancedSearchFormDataFlatted.category as categoryField, index}
										<SelectAutocomplete
											value={!expertNumber ? categoryField.value : null}
											class="ExpertSearchBox__advancedSearchSectionForm__category mt-2 mb-1 mx-0 border-[2px] w-full"
											options={categories}
											id={categoryField.id}
											name={categoryField.id}
											label={index == 0 ? 'Category' : ''}
											disabled={expertNumber !== null}
											placeholder="Type or select an option"
											on:onChange={_onCategoryChange}
										/>
										<Button
											color="Link"
											class="ExpertSearchBox__searchSection__state mx-0 capitalize max-w-max px-1 pb-2 text-xs h-auto lg:w-auto"
											on:click={() => _onAddRemoveFields(
												advancedSearchFormDataFlatted.category?.length ?? 0,
												categoryField,
												ADVANCED_SEARCH_FORM_DATA_FIELDS.CATEGORY,
												index
											)}
										>
											{advancedSearchFormDataFlatted.category?.length && index == advancedSearchFormDataFlatted.category.length - 1 ? '+Add' : '-Remove'}
										</Button>
									{/each}
								</div>
							{/if}
							{#if useState && advancedSearchFormDataFlatted.state}
								<div
									class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
								>
									{#each advancedSearchFormDataFlatted.state as stateField, index}
										<SelectAutocomplete
											value={!expertNumber ? stateField?.value : null}
											class="ExpertSearchBox__advancedSearchSectionForm__state mt-2 mb-1 mx-0 border-[2px] w-full"
											options={states}
											id={stateField.id}
											name={stateField.id}
											label={index == 0 ? 'State' : ''}
											disabled={expertNumber !== null}
											placeholder="Type or select an option"
											on:onChange={_onStateChange}
										/>
										<Button
											color="Link"
											class="ExpertSearchBox__searchSection__state mx-0 capitalize max-w-max px-1 pb-2 text-xs h-auto lg:w-auto"
											on:click={() => _onAddRemoveFields(
												advancedSearchFormDataFlatted.state?.length ?? 0,
												stateField,
												ADVANCED_SEARCH_FORM_DATA_FIELDS.STATE,
												index
											)}
										>
											{index == advancedSearchFormDataFlatted.state.length - 1 ? '+Add' : '-Remove'}
										</Button>
									{/each}
								</div>
							{/if}
							{#if useRegion && advancedSearchFormDataFlatted.region}
								<div
									class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
								>
									{#each advancedSearchFormDataFlatted.region as regionField, index}
										<SelectAutocomplete
											value={!expertNumber ? regionField?.value : null}
											class="ExpertSearchBox__advancedSearchSectionForm__region mt-2 mb-1 mx-0 border-[2px] w-full"
											options={regions}
											id={regionField.id}
											name={regionField.id}
											label={index == 0 ? 'Region' : ''}
											disabled={expertNumber !== null}
											placeholder="Type or select an option"
											on:onChange={_onRegionChange}
										/>
										<Button
											color="Link"
											class="ExpertSearchBox__searchSection__region mx-0 capitalize max-w-max px-1 pb-2 text-xs h-auto lg:w-auto"
											on:click={() => _onAddRemoveFields(
												advancedSearchFormDataFlatted.region?.length ?? 0,
												regionField,
												ADVANCED_SEARCH_FORM_DATA_FIELDS.REGION,
												index
											)}
										>
											{index == advancedSearchFormDataFlatted.region.length - 1 ? '+Add' : '-Remove'}
										</Button>
									{/each}
								</div>
							{/if}
							{#if useCountry && advancedSearchFormDataFlatted.country}
								<div
									class="ExpertSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6"
								>
									{#each advancedSearchFormDataFlatted.country as countryField, index}
										<SelectAutocomplete
											value={!expertNumber ? countryField?.value : null}
											class="ExpertSearchBox__advancedSearchSectionForm__country mt-2 mb-1 mx-0 border-[2px] w-full"
											options={countries}
											id={countryField.id}
											name={countryField.id}
											label={index == 0 ? 'Country' : ''}
											disabled={expertNumber !== null}
											placeholder="Type or select an option"
											on:onChange={_onCountryChange}
										/>
									{/each}
								</div>
							{/if}
						</div>
					</form>
				</aside>
			{/if}
			<!-- RESULTS PANEL -->
			<SectionLayout class="flex flex-col w-full">
				<!-- TOP PAGINATION REF -->
				{#if usePaginationIndicators}
					<div
						class={`ExpertSearchBox__slotSearchResults__paginationRef w-full flex pb-3 pt-3 justify-center 
								${$$slots.searchResults ? 'border-b-[1px] border-b-gray-500' : ''}`}
					>
						{#if _filteredWithResults}
							<SmallText>
								Showing <b>{totalExpertsListed}</b> from <b>{totalExperts} results</b>
							</SmallText>
						{:else if _filteredWithoutResults}
							<SmallText class="text-fr-navy">No results to show</SmallText>
						{:else if _filteredDefaultState}
							<SmallText class="text-fr-navy">Enter a Search Terms to display results</SmallText>
						{:else if isSearching}
							<SmallText class="text-fr-navy">Loading...</SmallText>
						{/if}
					</div>
				{/if}
				<!-- RESULTS SLOT -->
				<slot name="searchResults" {experts} />
				<!-- BOTTOM PAGINATION REF -->
				{#if usePaginationIndicators && totalExperts > 0 && !isSearching}
					<div
						class="ExpertSearchBox__slotSearchResults__paginationRef flex w-full mt-10 mb-10 justify-center"
					>
						<SmallText
							>Showing <b>{totalExpertsListed}</b> from <b>{totalExperts} results</b></SmallText
						>
					</div>
				{/if}
				<!-- LOADING INDICATOR -->
				{#if isSearching}
					<div
						class="ExpertSearchBox__slotSearchResults__loadingIndicator flex w-full h-screen mt-10 mb-10 justify-center"
					>
						<Spinner />
					</div>
				{/if}
				<!-- LOAD MORE CTA -->
				{#if usePaginationIndicators && totalExperts > totalExpertsListed}
					<div class="ExpertSearchBox__slotSearchResults__loadMore flex w-full mt-10 justify-center">
						<Button
							color="Outlined"
							class="ExpertSummaryCard__footer__viewCvCta capitalize mb-3 lg:mr-3 lg:w-60"
							on:click={() => {
								_search();
							}}
						>
							Load More
						</Button>
					</div>
				{/if}
			</SectionLayout>
		</div>
	{/if}
</SectionLayout>

<style>
	.fixed {
		position: fixed;
		width: 100%;
		left: 0;
		right: 0;
		max-width: 992px;
		padding: 0 10px;
		z-index: 1000;
	}

	.aside-fixed {
		position: sticky;
		align-self: flex-start;
		z-index: 900;
	}
</style>
