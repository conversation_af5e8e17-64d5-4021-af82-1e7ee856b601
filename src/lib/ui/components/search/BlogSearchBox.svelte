<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { nanoid } from 'nanoid';

	import TextField from '../form-elements/inputs/TextField/index.svelte';
	import Checkbox from '../form-elements/inputs/Checkbox/index.svelte';
	import SectionLayout from '../layouts/SectionLayout.svelte';
	import { PostServiceClient } from '$lib/services/posts.service';
	import Button from '../form-elements/Button.svelte';
	import SmallText from '../system-design/texts/SmallText.svelte';
	import type { TFieldTextOnKeyUpEventDispatcher } from '../form-elements/inputs/TextField/_types-interfaces';
	import SelectAutocomplete from '../form-elements/inputs/SelectAutocomplete/index.svelte';
	import Spinner from '../spinner/index.svelte';
	import {
		CategoryServiceClient,
		type TCategoryServiceClient
	} from '$lib/services/category.service';
	import type { TReducedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
	import Modal from '../over-layer/Modal/index.svelte';
	import type {
		TFilterSelected,
		TAdvancedSearchFormFieldMap,
		TAdvancedSearchFieldData,
		TOnPostSearchBoxEventDispatcher
	} from './_types-interfaces';
	import IoIosHelpCircleOutline from 'svelte-icons/io/IoIosHelpCircleOutline.svelte';
	import Tooltip from '../over-layer/Tooltip/index.svelte';
	import type { TReducedPostDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';

	interface $$Slots {
		searchResults: { posts: TReducedPostDto[] };
	}

	const dispatch = createEventDispatcher<TOnPostSearchBoxEventDispatcher>();
	const categoryService: TCategoryServiceClient = CategoryServiceClient();
	const postService = PostServiceClient();

	/* Component Inputs */
	export let useCriteria = true;
	export let criteria: string | null = null;
	export let useCriteriaFilterReference: boolean = false;
	export let useCategory = false;
	export let category: string | null = null;
	export let useCategoryFilterReference: boolean = false;
	export let useClearAll = false;
	export let useCustomSearchAction = false;
	export let useNoPadding = false;
	export let usePaginationIndicators = true;
	export let paginationLimit = 10;
	export let useExactMatchFilter = false;
	export let exactMatch = false;
	export let customPlaceholder: string | null = null;

	/* initialization */
	let posts: TReducedPostDto[] = [];
	let totalExperts = 0;
	let totalExpertsListed = 0;
	let start = 0;
	let categories: TReducedCategoryDto[] = [];
	let isSearching: boolean = false;
	let advancedSearchModalOpened: boolean = false;
	let filtersSelected: {
		criteria: TFilterSelected | null;
		category: TFilterSelected[] | null;
	} = {
		criteria: null,
		category: null
	};
	// Advanced Search Form and Filtering with Multi-Selection
	let advancedSearchFormDataMapped: {
		category: TAdvancedSearchFormFieldMap;
	} = _resetMultiSelectOptions();
	let advancedSearchFormDataFlatted: {
		category: TAdvancedSearchFieldData[] | null;
	} = {
		category: null
	};

	let filtersAsTags: TFilterSelected[] = [];
	$: _isFiltering = filtersSelected.criteria || filtersSelected.category?.[0]?.value;
	$: _isFilteringAdvanced = advancedSearchFormDataFlatted.category?.[0].value;
	$: {
		let _filtersAsTags: TFilterSelected[] = [];
		if (filtersSelected.criteria) _filtersAsTags.push(filtersSelected.criteria);
		if (filtersSelected.category) _filtersAsTags = [..._filtersAsTags, ...filtersSelected.category];
		filtersAsTags = _filtersAsTags;
		const _advancedSearchFormDataFlatted = {
			category: Array.from(advancedSearchFormDataMapped.category.values())
		};
		advancedSearchFormDataFlatted = _advancedSearchFormDataFlatted;
	}
	$: _useAdvancedSearch = useCategory;

	function _resetMultiSelectOptions() {
		const _defaultCategoryFieldId = `category-${nanoid(4)}`;
		return {
			category: new Map([
				[_defaultCategoryFieldId, { id: _defaultCategoryFieldId, value: category }]
			])
		};
	}

	const _syncAdvancedSearchingValues = () => {
		category = filtersSelected.category?.[0]?.value ?? null;

		//sync multi-select options
		const _advancedSearchFormDataMapped = _resetMultiSelectOptions();
		if (filtersSelected.category?.[0]?.value) {
			_advancedSearchFormDataMapped.category = new Map();
			filtersSelected.category.map((filterSelected) => {
				const { id, value } = filterSelected;
				_advancedSearchFormDataMapped.category.set(id, { id, value });
			});
		}
		advancedSearchFormDataMapped = _advancedSearchFormDataMapped;
	};

	const _syncSearchingValues = async (hasToSyncAdvanced?: boolean) => {
		criteria = filtersSelected.criteria?.value ?? null;
		if (hasToSyncAdvanced) _syncAdvancedSearchingValues();
	};

	const _resetParameters = () => {
		posts = [];
		totalExperts = 0;
		totalExpertsListed = 0;
		start = 0;
		paginationLimit = 10;
	};

	const _checkSelectedFilters = () => {
		const _filtersSelected: {
			criteria: TFilterSelected | null;
			category: TFilterSelected[] | null;
		} = {
			criteria: null,
			category: null
		};

		if (criteria)
			_filtersSelected.criteria = {
				title: `By Criteria: ${criteria}`,
				value: criteria,
				id: 'criteria',
				// ignoreOnSearch: expertNumber !== null,
				hidden: !useCriteriaFilterReference
			};

		// Category Filter supports now Multi-select options
		if (advancedSearchFormDataFlatted.category?.[0].value) {
			const categoryFilters = advancedSearchFormDataFlatted.category.reduce<TFilterSelected[]>(
				(acc, category) => {
					const { id, value } = category;
					if (value)
						acc.push({
							title: `By Category: ${value}`,
							value,
							id,
							// ignoreOnSearch: expertNumber !== null,
							hidden: !useCategoryFilterReference
						});

					return acc;
				},
				[]
			);
			_filtersSelected.category = categoryFilters.length > 0 ? categoryFilters : null;
		}

		//Because Svelte's reactivity is based on assignments, using array methods won't automatically trigger updates.
		//A subsequent assignment is required to trigger the update.
		filtersSelected = _filtersSelected;
	};

	const _search = async (hasToCleanParameters?: boolean) => {
		if (!useCustomSearchAction) {
			isSearching = true;
			if (hasToCleanParameters) _resetParameters();
			else if (totalExpertsListed) start += paginationLimit;
			_checkSelectedFilters();

			setTimeout(async () => {
				const response = await postService.searchPosts({
					start,
					limit: paginationLimit,
					criteria,
					category: filtersSelected.category?.[0]
						? filtersSelected.category?.reduce<string[]>((acc, filterSelected) => {
								acc.push(filterSelected.value!);
								return acc;
						  }, [])
						: null,
					reduced: true,
					exactMatch
				});
				if (response) {
					const { items, total } = response;
					posts = [...posts, ...items];
					totalExpertsListed += items.length;
					totalExperts = total;
				}
				isSearching = false;
			}, 750);
		} else {
			_checkSelectedFilters();
			dispatch('onSearch', { searchValues: filtersSelected });
		}
	};

	const _resetFiltersSelected = async () => {
		filtersSelected = {
			criteria: null,
			category: null
		};

		//multi-select options
		advancedSearchFormDataMapped = _resetMultiSelectOptions();
	};

	const _clearAll = async () => {
		if (_isFiltering) {
			await _resetFiltersSelected();
			await _syncSearchingValues(true);
			await _search(true);
			goto('/search-blogs');
		}
	};

	const _clearAdvancedSearchForm = async () => {
		category = null;

		_syncAdvancedSearchingValues();
	};

	const _onSearchingFieldPressKey = async (
		event: CustomEvent<TFieldTextOnKeyUpEventDispatcher>,
		callback: (value: string | null) => Promise<void>
	) => {
		const { keyCode, value } = event.detail;
		let _value = value;
		switch (keyCode) {
			case 'Escape':
				_value = null;
				break;
			case 'Backspace':
				if (_value?.length == 0) {
					_value = null;
				}
				break;
		}

		await callback(_value);
	};

	const _getCategories = async () => {
		const response = await categoryService.getCategories();
		if (response) {
			categories = response;
		}
	};

	const _onCategoryChange = async (event: CustomEvent) => {};

	const _onCleanField = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;

		await _search(true);
	};

	const _onExactMatchChange = (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		exactMatch = value;
	};

	const _onSubmit = async (event: any) => {
		_search(true);
	};

	const _toggleAdvancedSearchModal = () => {
		advancedSearchModalOpened = !advancedSearchModalOpened;
		if (advancedSearchModalOpened) {
			document.body.style.overflow = 'hidden';
			_syncAdvancedSearchingValues();
		} else {
			document.body.style.overflow = 'auto';
		}
	};

	const _advancedSearch = async () => {
		_toggleAdvancedSearchModal();
		await _search(true);
	};

	onMount(async () => {
		await _search();
		await Promise.all([_getCategories()]);
	});
</script>

<SectionLayout class={`BlogSearchBox ${useNoPadding ? '' : 'pb-10'}`}>
	<form
		class={`BlogSearchBox__searchSectionForm flex flex-col w-full ${
			$$slots.searchResults ? 'border-b-[1px] border-b-gray-500' : ''
		}`}
		on:submit={_onSubmit}
		method="get"
		action="/search-blogs"
	>
		{#if useCriteria}
			<div
				class="BlogSearchBox__searchSectionForm_fieldGroup flex flex-col w-full lg:flex-row lg:justify-between mx-auto"
			>
				<TextField
					useSearchIcon
					useCleanIcon
					class={`BlogSearchBox__searchSection__criteria border-[2px] w-full lg:w-11/12 ${
						useNoPadding ? '' : 'my-4'
					}`}
					placeholder={customPlaceholder ?? 'Enter your search terms here'}
					value={criteria}
					id="criteria"
					name="criteria"
					on:onCleanField={_onCleanField}
					on:onKeyUp={(event) => {
						_onSearchingFieldPressKey(event, async (value) => {
							criteria = value;
							if (!criteria) await _search(true);
						});
					}}
				/>
				{#if useExactMatchFilter}
					<div class="items-center mb-5 flex lg:hidden">
						<Checkbox
							label="Exact Match"
							value={exactMatch}
							disabled={!criteria}
							on:onChange={_onExactMatchChange}
						/>
						<Tooltip
							title={`
							Exact Match is a signal to our search engine that you're looking for an exact match. 
							Searching with Exact Match only finds results that include all of those words, in that 
							specific order. Searching without Exact Match populates results that include the words
							you typed, but necessarily not in the order you searched.
						`}
						>
							<div
								class={`Checkbox_information h-5 w-5 ml-1 cursor-help text-fr-charcoal dark:text-fr-white`}
							>
								<IoIosHelpCircleOutline />
							</div>
						</Tooltip>
					</div>
				{/if}
				<Button
					color="Primary"
					class="BlogSearchBox__searchSection__submitButton h-[46px] px-3 capitalize self-center w-full lg:w-2/12 lg:w-auto lg:ml-1"
					type="button"
					on:click={_onSubmit}
				>
					Search
				</Button>
			</div>

			{#if useExactMatchFilter}
				<div class="items-center mb-5 hidden lg:flex">
					<Checkbox
						label="Exact Match"
						value={exactMatch}
						disabled={!criteria}
						on:onChange={_onExactMatchChange}
					/>
					<Tooltip
						title={`
						Exact Match is a signal to our search engine that you're looking for an exact match. 
						Searching with Exact Match only finds results that include all of those words, in that 
						specific order. Searching without Exact Match populates results that include the words
						you typed, but necessarily not in the order you searched.
					`}
					>
						<div
							class={`Checkbox_information h-5 w-5 ml-1 cursor-help text-fr-charcoal dark:text-fr-white`}
						>
							<IoIosHelpCircleOutline />
						</div>
					</Tooltip>
				</div>
			{/if}
		{/if}

		<div class="BlogSearchBox__searchSectionForm_fieldGroup flex flex-col w-full">
			<div
				class="BlogSearchBox__searchSectionForm_buttonGroup flex flex-col w-full justify-start lg:flex-row"
			>
				{#if useClearAll && _isFiltering}
					<Button
						color="Link"
						class="BlogSearchBox__searchSection__state mx-0 capitalize max-w-max self-center px-1 lg:w-auto lg:ml-4"
						on:click={_clearAll}
					>
						Clear All
					</Button>
				{/if}
			</div>
		</div>
	</form>

	{#if $$slots.searchResults}
		<!-- TOP PAGINATION REF -->
		{#if usePaginationIndicators}
			{#if totalExperts > 0 && !isSearching}
				<div
					class="BlogSearchBox__slotSearchResults__paginationRef flex w-full mt-10 mb-10 justify-center"
				>
					<SmallText
						>Showing <b>{totalExpertsListed}</b> from <b>{totalExperts} results</b></SmallText
					>
				</div>
			{:else if !isSearching}
				<div
					class="BlogSearchBox__slotSearchResults__noResults flex w-full mt-10 mb-10 justify-center"
				>
					<SmallText class="text-fr-navy">No results to show</SmallText>
				</div>
			{/if}
		{/if}

		<slot name="searchResults" {posts} />

		{#if usePaginationIndicators}
			<!-- BOTTOM PAGINATION REF -->
			{#if totalExperts > 0 && !isSearching}
				<div
					class="BlogSearchBox__slotSearchResults__paginationRef flex w-full mt-10 mb-10 justify-center"
				>
					<SmallText
						>Showing <b>{totalExpertsListed}</b> from <b>{totalExperts} results</b></SmallText
					>
				</div>
			{/if}
		{/if}

		<!-- LOADING INDICATOR -->
		{#if isSearching}
			<div
				class="BlogSearchBox__slotSearchResults__loadingIndicator flex w-full h-screen mt-10 mb-10 justify-center"
			>
				<Spinner />
			</div>
		{/if}

		{#if usePaginationIndicators}
			<!-- LOAD MORE CTA -->
			{#if totalExperts > totalExpertsListed}
				<div class="BlogSearchBox__slotSearchResults__loadMore flex w-full mt-20 justify-center">
					<Button
						color="Outlined"
						class="ExpertSummaryCard__footer__viewCvCta capitalize mb-3 lg:mr-3 lg:w-60"
						on:click={() => {
							_search();
						}}
					>
						Load More
					</Button>
				</div>
			{/if}
		{/if}
	{/if}
</SectionLayout>

<Modal
	isOpened={advancedSearchModalOpened}
	class="h-[80%]"
	title="Advanced Search"
	on:onClose={_toggleAdvancedSearchModal}
>
	<!-- TODO: Move this into a separated component -->
	<form
		class="BlogSearchBox__advancedSearchSectionForm flex flex-col w-full max-w-[]"
		slot="bodyContent"
		on:submit={async () => {
			await _advancedSearch();
		}}
	>
		<div class="BlogSearchBox__advancedSearchSectionForm_fieldsContainer flex flex-col w-full">
			{#if useCategory && advancedSearchFormDataFlatted.category}
				<div class="BlogSearchBox__advancedSearchSectionForm_fieldGroup flex flex-col w-full mb-6">
					{#each advancedSearchFormDataFlatted.category as categoryField, index}
						<SelectAutocomplete
							class="BlogSearchBox__advancedSearchSectionForm__category mt-2 mb-1 mx-0 border-[2px] w-full"
							options={categories}
							id={categoryField.id}
							name={categoryField.id}
							label={index == 0 ? 'By Category' : ''}
							on:onChange={_onCategoryChange}
						/>
					{/each}
				</div>
			{/if}

			<div class="BlogSearchBox__advancedSearchSectionForm_buttonGroup flex w-full justify-center">
				<Button
					color="Link"
					class="BlogSearchBox__advancedSearchSectionForm__state mx-0 capitalize max-w-max self-center px-1 lg:w-auto"
					disabled={!_isFilteringAdvanced}
					on:click={_clearAdvancedSearchForm}
				>
					Clear All
				</Button>
			</div>
		</div>
	</form>
	<div class="BlogSearchBox__advancedSearchSectionForm__ctas flex mt-16" slot="footerCTAs">
		<Button
			color="Outlined"
			class="BlogSearchBox__advancedSearchSectionForm_cancelButton h-[46px] px-1 capitalize self-center max-w-full lg:w-36 lg:mx-1"
			type="button"
			on:click={_toggleAdvancedSearchModal}
		>
			Cancel
		</Button>
		<Button
			color="Primary"
			class="BlogSearchBox__advancedSearchSectionForm_searchButton h-[46px] px-1 capitalize self-center max-w-full lg:w-36 lg:mx-1"
			type="button"
			on:click={async () => {
				await _advancedSearch();
			}}
		>
			Search
		</Button>
	</div>
</Modal>
