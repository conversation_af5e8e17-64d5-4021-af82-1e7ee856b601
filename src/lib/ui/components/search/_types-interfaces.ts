import { TReducedExpertDto } from "$lib/shared/api-dtos/internal-rest/expert-dtos";

export type TAdvancedSearchFieldData = {
    value: string | null;
    id: string;
}

export type TExpertSearchFilterValue = Array<string | null>;

export type TAdvancedSearchFormFieldMap = Map<TAdvancedSearchFieldData['id'], TAdvancedSearchFieldData>;

export type TAdvancedSearchFormDataMapped = {
    expertNumber: TAdvancedSearchFieldData | null;
    category: TAdvancedSearchFormFieldMap;
    country: TAdvancedSearchFormFieldMap;
    region: TAdvancedSearchFormFieldMap;
    state: TAdvancedSearchFormFieldMap;
};

export type TAdvancedSearchFormDataFlatted = {
    expertNumber: TAdvancedSearchFieldData | null;
    category: TAdvancedSearchFieldData[] | null;
    country: TAdvancedSearchFieldData[] | null;
    region: TAdvancedSearchFieldData[] | null;
    state: TAdvancedSearchFieldData[] | null;
};

export type TFilterSelected = Pick<TAdvancedSearchFieldData, 'id' | 'value'> & {
    title: string;
    ignoreOnSearch?: boolean;
    hidden?: boolean;
};

export type TFiltersSelected = {
    criteria: TFilterSelected | null;
    expertNumber: TFilterSelected | null;
    category: TFilterSelected[] | null;
    country: TFilterSelected[] | null;
    region: TFilterSelected[] | null;
    state: TFilterSelected[] | null;
};

export type TOnExpertSearchBoxEventDispatcher = {
    onSearch: { searchValues: TFilterSelected[] };
    beforeUpdateUrlSearchParams: {newUrlSearchParams: URLSearchParams};
    beforeSubmit: {searchValues: TFilterSelected[]}
};

export type TOnPostSearchBoxEventDispatcher = {
    onSearch: {
        searchValues: {
            criteria: TFilterSelected | null;
            category: TFilterSelected[] | null;
        };
    };
}

export type TPreloadedData = {
    total: number;
    items: TReducedExpertDto[];
    start: number;
    limit: number;
};