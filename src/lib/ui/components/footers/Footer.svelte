<script lang="ts">
	import { globalInfo } from '$lib/ui/state-management/store/global';

	import SectionLayout from '../layouts/SectionLayout.svelte';
	import H3 from '../system-design/headings/H3.svelte';
	import HTMLRenderer from '../cms-content-renderer/HtmlRenderer/index.svelte';
	import Paragraph from '../system-design/texts/Paragraph.svelte';
	import ThemeIcon from '../system-design/icons/ThemeIcon.svelte';
	import SmallText from '../system-design/texts/SmallText.svelte';
	import FacebookIcon from '$lib/ui/components/icons/icon-facebook-footer.svelte';
	import LinkedinIcon from '$lib/ui/components/icons/icon-linkedin-footer.svelte';
	import TwitterIcon from '$lib/ui/components/icons/icon-twitter-footer.svelte';
	import ExpertSimpleSearchBox from '../search/ExpertSimpleSearchBox.svelte';
	import SubsectionHeader from '../system-design/headings/SubsectionHeader.svelte';
	import { derived } from 'svelte/store';
	import { page } from '$app/stores';

	const currentPath = derived(page, ($page: { url: { pathname: string; }; }) => $page.url.pathname)
	let _showFooterSearchingBar = true;
	$: {
		_showFooterSearchingBar = !$currentPath.includes('search')
	}

	const viewBrochure = () => {
		let winInstance = window?.open('/docs/brochure.pdf');
		if (winInstance) {
			winInstance.onload = function () {
				setTimeout(function () {
					if (winInstance) winInstance.document.getElementsByTagName('title')[0].append('Brochure');
				}, 500);
			};
		}
	};
</script>

<footer class="Footer bg-fr-navy">
	<div class="flex flex-col lg:flex-row justify-center py-12">
		<SectionLayout
			class="Footer__itemsContainer flex flex-col gap-4 lg:items-baseline lg:flex-row w-full max-w-screen-2xl px-10 lg:px-16 2xl:px-24 lg:justify-between py-2"
		>
			<div class="Footer__block Footer__itemsContainer__logo flex flex-col gap-2 mb-10">
				<SubsectionHeader class="text-fr-white">{$globalInfo.footerLinks?.[0]?.heading}</SubsectionHeader>
				<a href="/" class="mb-6" title="Light Free Referral Logo">
					<img
						src="/images/light-logo.svg"
						alt="Light Free Referral Logo"
						loading="eager"
						title="Light Free Referral Logo"
					/>
				</a>
				<div class="flex gap-6">
					{#if $globalInfo?.socials?.length}
						{#each $globalInfo?.socials as social}
							{#if social?.profileUrl && social?.icon}
								<a
									href={social?.profileUrl}
									title={social?.icon === 'x'
										? 'This is our X account'
										: social?.icon === 'facebook'
										? 'See Our Facebook Page'
										: 'See Our LinkedIn Page'}
								>
									{#if social?.icon?.toLocaleLowerCase() === 'x'}
										<TwitterIcon />
									{:else if social?.icon?.toLocaleLowerCase() === 'facebook'}
										<FacebookIcon />
									{:else}
										<LinkedinIcon />
									{/if}
								</a>
							{/if}
						{/each}
					{/if}

					<div class="hidden lg:block !text-fr-white ml-6">
						<ThemeIcon isWhite />
					</div>
				</div>

				<div class="grid grid-cols-1 gap-4 pl-4 lg:pl-20 mt-8">
					<HTMLRenderer
						class="text-fr-white leading-normal"
						content={$globalInfo.companyInfo?.address}
					/>

					{#if $globalInfo?.companyInfo?.email}
						<a href={`mailto:${$globalInfo.companyInfo.email}`} title="E-mail us">
							<Paragraph class="text-fr-white">Email: {$globalInfo.companyInfo.email}</Paragraph>
						</a>
					{/if}

					<div>
						{#if $globalInfo?.companyInfo?.tollFree}
							<a
								href={`tel:${$globalInfo?.companyInfo?.tollFree}`}
								title="This is our toll free number"
							>
								<Paragraph class="text-fr-white">
									Toll Free: {$globalInfo?.companyInfo?.tollFree}
								</Paragraph>
							</a>
						{/if}
						<a href={`tel:${$globalInfo.companyInfo?.intl}`} title="Call us anytime">
							<SmallText class="text-fr-white mt-1">
								Intl: {$globalInfo.companyInfo?.intl}
							</SmallText>
						</a>
					</div>
					{#if $globalInfo.companyInfo?.phone}
						<a href={`tel:${$globalInfo.companyInfo?.phone}`} title="Call us anytime">
							<Paragraph class="text-fr-white">Phone: {$globalInfo.companyInfo?.phone}</Paragraph>
						</a>
					{/if}
					{#if $globalInfo.companyInfo?.fax}
						<a href={`tel:${$globalInfo.companyInfo?.fax}`} title="This is our fax number">
							<Paragraph class="text-fr-white">Fax: {$globalInfo.companyInfo?.fax}</Paragraph>
						</a>
					{/if}
				</div>
			</div>

			{#if $globalInfo?.footerLinks && $globalInfo?.footerLinks?.length > 1}
				{#each $globalInfo?.footerLinks?.slice(1) as item}
					<div
						class="Footer__block Footer__itemsContainer__links flex flex-col gap-3 mb-20 pl-4 lg:pl-0"
					>
						<SubsectionHeader class="text-fr-white">{item.heading}</SubsectionHeader>
						{#each item.sublinks as sublink}
							<a
								href={`${sublink.sublinkUrl}`}
								class="text-fr-white hover:underline font-oswald"
								title={sublink.sublinkLabel}
								target={sublink.sublinkUrl.endsWith('.pdf') ? '_blank' : '_self'}
							>
								{sublink.sublinkLabel}
							</a>
						{/each}

						{#if item.heading.toLowerCase().includes('company') && _showFooterSearchingBar}
							<div class="w-80 hidden lg:block mt-5">
								<ExpertSimpleSearchBox
									useCustomSearchAction
									useNoPadding
									customPlaceholder="What type of Expert Witness are you looking for?"
								/>
							</div>
						{/if}
					</div>
				{/each}
			{/if}
		</SectionLayout>
	</div>

	<div class="py-6 flex flex-col items-center px-8 lg:px-12 border-t border-gray-800">
		{#if _showFooterSearchingBar}
			<div class="lg:hidden w-full">
				<ExpertSimpleSearchBox
					useCustomSearchAction
					useNoPadding
					customPlaceholder="What type of Expert Witness are you looking for?"
				/>
			</div>
		{/if}

		<div class="grid grid-cols-1 gap-4 mb-2 text-fr-white">
			<!-- <a href="terms-of-services">Terms of Service</a> -->
			<a href="/privacy" title="Privacy Policy">Privacy Policy</a>
		</div>

		{#if $globalInfo.companyInfo?.copyRight}
			<Paragraph class="text-fr-white text-center leading-normal">
				{$globalInfo.companyInfo.copyRight}
			</Paragraph>
		{/if}
	</div>
</footer>

<style>
	:global(.footer__social-item) {
		width: 2rem;
		height: 2rem;
	}
</style>
