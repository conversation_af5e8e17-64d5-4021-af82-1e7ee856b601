<script lang="ts">
  import { browser } from '$app/environment';
  import { fly } from 'svelte/transition';
  import IoMdClose from 'svelte-icons/io/IoMdClose.svelte';
  import { theme } from '$lib/ui/state-management/store/theme';

  let el;
  let originalBodyOverflow = '';
  export let show = false;

  export let onClose: () => void;

  $: if (browser) {
    if (show) {
      originalBodyOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = originalBodyOverflow;
    }
  }
</script>

{#if show}
  <div
    bind:this={el}
    transition:fly={{ x: el.scrollWidth, opacity: 1 }}
    class={`${$theme ? 'bg-fr-black' : 'bg-fr-white'} fixed top-0 right-0 px-12 py-0 h-full w-full md:w-1/4 lg:w-1/4 overflow-y-auto z-10 md:shadow-[-10px_4px_17px_3px_rgba(0,0,0,0.25)]`}
  >
    <button
      aria-label="Close sidebar"
      class="absolute top-4 right-4 p-0 h-8 w-8"
      on:click={() => {
        onClose();
        show = false;
      }}
    >
      <div class={`${$theme ? 'text-fr-white' : 'text-fr-black'}`}>
        <IoMdClose />
      </div>
    </button>
    <slot />
  </div>
{/if}
