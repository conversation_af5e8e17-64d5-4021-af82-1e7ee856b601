<script lang="ts">
	import { theme } from '$lib/ui/state-management/store/theme';
	import IoIosContrast from 'svelte-icons/io/IoIosContrast.svelte';

	export let isWhite = false;

	const toggleTheme = () => {
		theme.update((v) => !v);
	};
</script>

<button
	class="w-8 h-8 ease-in-out duration-200 hover:scale-125"
	on:click={toggleTheme}
	title="Click to view website in light/dark mode"
>
	<div class={`${isWhite ? 'text-fr-white' : $theme ? 'text-fr-white' : 'text-fr-black'}`}>
		<IoIosContrast />
	</div>
</button>
