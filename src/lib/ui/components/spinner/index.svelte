<div id="spinner_dots">
	<span />
    <span />
    <span />
    <span />
</div>

<style>
    #spinner_dots {
    display: flex;
    height: 50px;
    width: 50px;
  }
  
  #spinner_dots span {
    width: 10px;
    height: 10px;
    background: rgb(53,83,239);
    border-radius: 50%;
    -webkit-animation: spinner_dots 1s infinite ease-in-out;
            animation: spinner_dots 1s infinite ease-in-out;
  }
  
  #spinner_dots span:nth-child(1) {
    left: 0px;
    -webkit-animation-delay: 0.2s;
            animation-delay: 0.2s;
  }
  
  #spinner_dots span:nth-child(2) {
    left: 15px;
    -webkit-animation-delay: 0.3s;
            animation-delay: 0.3s;
  }
  
  #spinner_dots span:nth-child(3) {
    left: 30px;
    -webkit-animation-delay: 0.4s;
            animation-delay: 0.4s;
  }
  
  #spinner_dots span:nth-child(4) {
    left: 45px;
    -webkit-animation-delay: 0.5s;
            animation-delay: 0.5s;
  }
  
  @keyframes spinner_dots {
    0% {
      -webkit-transform: translateY(0px);
              transform: translateY(0px);
      -webkit-transform: translateY(0px);
              transform: translateY(0px);
      background: rgba(53,83,239, 0.7);
    }
    50% {
      -webkit-transform: translateY(10px);
              transform: translateY(10px);
      -webkit-transform: translateY(10px);
              transform: translateY(10px);
      background: rgb(12 30 51);
    }
    100% {
      -webkit-transform: translateY(0px);
              transform: translateY(0px);
      -webkit-transform: translateY(0px);
              transform: translateY(0px);
      background: rgba(53,83,239, 0.7);
    }
  }
  @-webkit-keyframes spinner_dots {
    0% {
      -webkit-transform: translateY(0px);
              transform: translateY(0px);
      background: rgba(53,83,239, 0.7);
    }
    50% {
      -webkit-transform: translateY(10px);
              transform: translateY(10px);
      background: rgb(12 30 51);
    }
    100% {
      -webkit-transform: translateY(0px);
              transform: translateY(0px);
      background: rgba(53,83,239, 0.7);
    }
  }
</style>