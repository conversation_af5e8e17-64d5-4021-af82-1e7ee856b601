<script lang="ts">
    import { onMount } from 'svelte';
  
    export let targetNumber: number = 10000; // Default target number
    export let duration: number = 1500; // Default duration in milliseconds
    export let step: number = 1;
  
    let counter: number = 0;
    let displayCounter: string = "0";
    let interval: NodeJS.Timeout;
    let counterElement: HTMLDivElement;
  
    function formatNumber(number: number): string {
      return number.toLocaleString() + "+";
    }
  
    function startCounting(): void {
      const totalSteps: number = Math.abs(targetNumber - counter);
      const intervalDuration: number = duration / totalSteps;
  
      interval = setInterval(() => {
        if (counter < targetNumber) {
          counter += step;
          displayCounter = formatNumber(counter);
        } else {
          clearInterval(interval);
          displayCounter = formatNumber(targetNumber);
        }
      }, intervalDuration);
    }
  
    onMount((): void => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            startCounting();
            observer.disconnect();
          }
        });
      });
  
      if (counterElement) {
        observer.observe(counterElement);
      }
    });
  </script>
  
  <style>
    .counter {
      font-size: 3rem;
      font-weight: bold;
    }
  </style>
  
  <div bind:this={counterElement} class="counter">
    {displayCounter}
  </div>
  