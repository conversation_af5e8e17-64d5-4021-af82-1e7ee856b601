<script lang="ts">
	import { onMount } from 'svelte';
	import { pageInfo } from '$lib/ui/state-management/store/global';

	interface Post {
		id: number;
		link: string;
		title: { rendered: string };
		excerpt: { rendered: string };
		categories: number[];
		author: number;
		featured_media: number;
		categoriesDetails?: Category[];
		authorDetails?: Author;
		featuredImageUrl?: string;
	}

	interface Category {
		id: number;
		name: string;
	}

	interface Author {
		id: number;
		name: string;
	}

	export let postData: {
		totalPages: number;
		posts: Post[];
	} = {
		totalPages: 1,
		posts: []
	};

	let currentPage = 1;

	const scrollToTop = () => {
		window.scrollTo({ top: 0, behavior: 'smooth' });
	};

	function updateQueryParameter(newPage: number) {
		pageInfo.set(newPage);

		const params = new URLSearchParams(window.location.search);
		if (newPage === 1) {
			window.history.replaceState({}, '', `${window.location.pathname}`);
		} else {
			params.set('page', newPage.toString());
			window.history.replaceState({}, '', `${window.location.pathname}?${params}`);
		}

		// Reload the page to reflect the changes
		window.location.reload();
	}

	const nextPage = (): void => {
		if (currentPage < postData.totalPages) {
			currentPage += 1;
			updateQueryParameter(currentPage);

			scrollToTop();
		}
	};

	const prevPage = (): void => {
		if (currentPage > 1) {
			currentPage -= 1;
			updateQueryParameter(currentPage);

			scrollToTop();
		}
	};

	const goToPage = (page: number): void => {
		currentPage = page;
		updateQueryParameter(currentPage);

		scrollToTop();
	};

	onMount(async () => {
		const { searchParams } = new URL(window.location.href);
		currentPage = parseInt(searchParams.get('page') || '1');
		pageInfo.set(currentPage);
	});

	$: pageNumbers = Array.from({ length: postData.totalPages }, (_, i) => i + 1);

	const getSlug = (link: string) => {
		if (!link) {
			return '';
		}

		const newLink = link.endsWith('/') ? link.slice(0, -1) : link;

		const pathParts = newLink.split('/');
		return pathParts[pathParts.length - 1];
	};
</script>

{#if postData?.posts.length > 0}
	<div class="my-12">
		<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-x-8 gap-y-12 justify-between">
			{#each postData.posts as post}
				{#if post?.title?.rendered}
					<div
						class="post__content basis-full md:basis-[47%] lg:basis-[45%] aspect-square flex flex-col gap-2"
					>
						<a href={`/blog/${getSlug(post.link)}`} class="post-link">
							<div class="flex flex-col gap-1">
								{#if post.featuredImageUrl}
									<img
										class="object-cover aspect-video mb-3"
										src={post.featuredImageUrl}
										alt={post.title.rendered}
									/>
								{/if}

								{#if post?.categoriesDetails?.length}
									<p class="text-fr-royal-blue uppercase font-medium mt-2">
										{@html post.categoriesDetails?.map((category) => category.name).join(' | ')}
									</p>
								{/if}

								<div class="text-fr-charcoal dark:text-fr-white">
									<h4 class="text-2xl my-0 mb-2 line-clamp-3">{@html post.title.rendered}</h4>
								</div>

								{#if post?.authorDetails?.name}
									<div class="Blog__byline flex gap-2 text-fr-charcoal dark:text-fr-white">
										<p>By {post.authorDetails.name}</p>
									</div>
								{/if}
							</div>
						</a>
					</div>
				{/if}
			{/each}
		</div>

		{#if postData?.totalPages > 1}
			<div class="pagination flex items-center justify-center mt-7 lg:mt-12">
				<button
					class="page-number p-2 lg:px-6 border-gray-300 hover:border-gray-800 disabled:!text-gray-300 disabled:pointer-events-none dark:hover:border-gray-100"
					on:click={prevPage}
					disabled={currentPage === 1}>«</button
				>
				{#each pageNumbers as page}
					<button
						class="page-number p-2 lg:px-6 border-gray-300 hover:border-gray-800 dark:hover:border-gray-100 {page ===
						currentPage
							? 'active'
							: ''}"
						on:click={() => goToPage(page)}
					>
						{page}
					</button>
				{/each}
				<button
					class="page-number p-2 lg:px-6 border-gray-300 hover:border-gray-800 disabled:!text-gray-300 disabled:pointer-events-none dark:hover:border-gray-100"
					on:click={nextPage}
					disabled={currentPage === postData.totalPages}>»</button
				>
			</div>
		{/if}
	</div>
{/if}

<style>
	.Blog__byline {
		font-family: Oswald;
		font-size: 1rem;
		font-style: normal;
		font-weight: 400;
		line-height: normal;
		letter-spacing: 0.8px;
	}

	.pagination .page-number {
		@apply text-sky-700 border border-solid transition-colors duration-200 font-thin text-sm cursor-pointer;
	}
	.pagination .active {
		@apply bg-sky-600 text-white;
	}
</style>
