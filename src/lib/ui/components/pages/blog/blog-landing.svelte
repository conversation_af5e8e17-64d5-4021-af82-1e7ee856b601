<script lang="ts">
	import { pageInfo } from '$lib/ui/state-management/store/global';

	import BlogLayout from '$lib/ui/components/layouts/BlogLayout.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import type { PageServerData } from '../../../../../routes/blog/$types';

	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import LatestPosts from './blog-latest-display.svelte';
	import BlogCategoryDisplay from './blog-category-display.svelte';
	import BlogFeaturedTriple from './blog-featured-triple.svelte';
	import BlogHeader from './blog-header.svelte';
	import BlogSimpleSearchBox from '../../search/BlogSimpleSearchBox.svelte';
	import RequestExpertForm from '../generic/RequestExpertForm.svelte';
	import SubscriptionForm from '../generic/SubscriptionForm.svelte';
	import BlogArticlesPagination from './blog-articles-pagination.svelte';

	export let data: Omit<PageServerData, 'seoData'>;

	$: ({ pageData, categoriesData, featuredPosts, latestPosts, postData } = data);

	function isFeaturedBlogs(section: any) {
		return section.__typename.includes('Components_FeaturedBlogs');
	}
</script>

<BlogHeader
	heading={pageData?.title ?? undefined}
	subheading={pageData?.blogPageCustomFields?.subheading ?? undefined}
	description={pageData?.blogPageCustomFields?.description ?? undefined}
/>

<BlogLayout>
	<MainLayout class="flex flex-col gap-4">
		<SectionLayout class="min-w-[320px] max-w-screen-2xl w-full mx-auto px-6 lg:px-16">
			<BlogCategoryDisplay categories={categoriesData.categories} />
		</SectionLayout>
	</MainLayout>

	{#if pageData?.blogPageCustomFields?.components?.length && pageData?.blogPageCustomFields?.components?.length > 0}
		{#each pageData?.blogPageCustomFields?.components as section}
			<div class="min-w-[320px] max-w-screen-2xl mx-auto px-6 lg:px-16">
				{#if isFeaturedBlogs(section) && $pageInfo === 1}
					<BlogFeaturedTriple blogs={section?.blogs} />
				{/if}
			</div>
		{/each}
	{/if}

	<MainLayout class="flex flex-col gap-4 my-6 lg:mb-8">
		<div class="min-w-[320px] max-w-screen-2xl mx-auto px-6 lg:px-16">
			<!-- <BlogFeaturedTriple blogs={featuredPosts.posts} /> -->
			<BlogSimpleSearchBox customPlaceholder="Please Enter Search Term For Blog Articles" />
			<LatestPosts blogs={latestPosts.posts} />
		</div>
	</MainLayout>

	<SubscriptionForm />

	<SectionLayout class="min-w-[320px] max-w-screen-2xl w-full mx-auto px-6 lg:px-16">
		<BlogArticlesPagination {postData} />
	</SectionLayout>
</BlogLayout>

<RequestExpertForm />
