<script lang="ts">
	import BlogArticleItem from './blog-article-item.svelte';

	export let blogs: any[] = [];
</script>

{#if blogs.length}
	<div class="mt-10">
		<h3 class="text-3xl mt-6 mb-8 dark:text-fr-white">Latest Articles</h3>
		<div class="Blogs__Latest grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-x-8 gap-y-12">
			{#each blogs as post}
				<BlogArticleItem {post} />
			{/each}
		</div>
	</div>
{/if}
