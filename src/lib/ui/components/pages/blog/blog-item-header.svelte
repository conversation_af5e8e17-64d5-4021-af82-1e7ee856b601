<script lang="ts">
	import type { PageServerData } from '../../../../../routes/blog/[slug]/$types';
	import { getDateString } from '$lib/utils/date';
	import SocialShare from '$lib/ui/components/pages/blog/blog-item-social-share.svelte';
	import Link from '../../form-elements/Link.svelte';
	export let data: Omit<PageServerData, 'seoData'>;

	function calculateReadingTime(totalWords: number) {
		const wordsPerMinute = 225; // Average reading speed
		const readingTime = totalWords / wordsPerMinute;

		// Round up so that even short texts are considered to take at least 1 minute to read
		return Math.ceil(readingTime);
	}

	function replacePTagsWithH2Tags(htmlString?: string) {
		if (!htmlString) {
			return '';
		}

		return htmlString.replace(/<p>/g, '<h2>').replace(/<\/p>/g, '</h2>');
	}
</script>

<div class="flex flex-col gap-4 max-w-3xl md:mx-auto w-full">
	<h1 class="post-item__title text-center md:text-start text-fr-charcoal dark:text-fr-white">
		{data.post?.title}
	</h1>

	<div
		class="post-item__meta text-start text-fr-charcoal dark:text-fr-white flex flex-wrap gap-2 lg:gap-3"
	>
		{#if data.post?.author?.node.slug}
			<span
				>Written by: 
				<Link linkUrl={`/author/${data.post?.author?.node.slug}`} color="primary">
					{data.post?.author?.node.name}
				</Link>
			</span
			>
			•
		{/if}
		<span>{getDateString(new Date(data.post?.date ?? ''))}</span>
		•
		<span>
			{calculateReadingTime(data.post?.content?.split(' ')?.length ?? 0)} minute(s) read
		</span>
	</div>

	<SocialShare {data} />

	<img
		class="block w-full object-cover aspect-video"
		src={data.post?.featuredImage?.node.sourceUrl}
		alt={data.post?.featuredImage?.node.description ?? data.post?.title}
	/>
</div>

<style>
	.post-item__title {
		font-family: Oswald;
		font-size: 3rem;
		font-weight: 700;
		line-height: 1.15;
	}

	:global(.post-item__excerpt h2) {
		font-weight: 400;
	}
</style>
