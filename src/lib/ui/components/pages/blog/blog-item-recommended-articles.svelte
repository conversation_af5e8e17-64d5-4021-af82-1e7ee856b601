<script lang="ts">
	import type { PageServerData } from '../../../../../routes/blog/[slug]/$types';
	export let data: Omit<PageServerData, 'seoData'>;
</script>

{#if data.featuredPosts.length}
	<div class="my-10 md:mx-6 lg:max-w-6xl lg:mx-auto">
		<h2 class="text-[2rem] text-fr-charcoal dark:text-fr-white">Recommended Articles</h2>
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-x-8 gap-y-12 justify-between">
			{#each data.featuredPosts as post}
				{#if post?.title}
					<a class="Post__content" href={`/blog/${post.slug}`}>
						{#if post?.title}
							<div class="flex flex-col gap-1">
								<img
									class="object-cover aspect-video mb-3"
									src={post.featuredImage?.node.sourceUrl}
									alt={post.featuredImage?.node.description ?? post.title}
								/>
								{#if post?.categories?.nodes?.[0]?.name}
									<p class="text-fr-royal-blue uppercase font-medium mt-2">
										{post?.categories.nodes?.[0]?.name ?? ''}
									</p>
								{/if}

								<div class="text-fr-charcoal dark:text-fr-white">
									<h3 class="text-2xl my-0 mb-2 line-clamp-3">{post.title}</h3>
								</div>
								{#if post.author?.node.name}
									<div class="Blog__byline flex gap-2 dark:text-fr-white">
										<p class="">By {post.author?.node.name}</p>
									</div>
								{/if}
							</div>
						{/if}
					</a>
				{/if}
			{/each}
		</div>
	</div>
{/if}

<style>
	.Blog__byline {
		font-family: Oswald;
		font-size: 1rem;
		font-weight: 400;
		letter-spacing: 0.8px;
	}
</style>
