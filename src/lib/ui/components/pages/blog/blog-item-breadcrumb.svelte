<script lang="ts">
	import type { PageServerData } from '../../../../../routes/blog/[slug]/$types';
	import Link from '../../form-elements/Link.svelte';
	export let data: Omit<PageServerData, 'seoData'>;
</script>

<div class="flex flex-col gap-4 max-w-3xl md:mx-auto w-full">
	<ul class="flex items-center gap-2 flex-wrap dark:text-fr-white">
		<li>
			<Link linkUrl="/blog" color="primary" class="text-sm">Blog</Link>
		</li>
		/
		<li>
			<Link linkUrl={`/blog/category/${data?.post?.categories?.nodes?.[0]?.slug}`} color="primary" class="text-sm">
				{data?.post?.categories?.nodes?.[0]?.name}
			</Link>
		</li>
		/
		<li>
			<Link disabled color="primary" class="text-sm no-underline h-auto">
				{data.post?.title}
			</Link>
		</li>
	</ul>
</div>

<style>
	:global(.post-item__excerpt h2) {
		font-weight: 400;
	}
</style>
