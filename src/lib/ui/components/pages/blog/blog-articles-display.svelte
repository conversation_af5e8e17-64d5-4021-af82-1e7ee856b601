<script lang="ts">
	import type { CorePostFragment } from '$lib/shared/api-dtos/external-graphql/generated';
	export let posts: CorePostFragment[];
</script>

<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-x-8 gap-y-12 justify-between">
	{#each posts as post}
		{#if post?.title}
			<div
				class="post__content basis-full md:basis-[47%] lg:basis-[45%] aspect-square flex flex-col gap-2"
			>
				<a href={`/blog/${post.slug}`} class="post-link">
					<div class="flex flex-col gap-1">
						<img
							class="object-cover aspect-video mb-3"
							src={post.featuredImage?.node.sourceUrl}
							alt={post.featuredImage?.node.description ?? post.title}
						/>
						{#if post?.categories?.nodes?.[0]?.name}
							<p class="text-blue-800 uppercase font-medium mt-2">
								{post.categories.nodes?.[0]?.name ?? ''}
							</p>
						{/if}

						<div class="text-fr-charcoal dark:text-fr-white">
							<h4 class="text-2xl my-0 mb-2 line-clamp-3">{post.title}</h4>
						</div>

						<div class="Blog__byline flex gap-2">
							<p class="">{post.author?.node.name}</p>
						</div>
					</div>
				</a>
			</div>
		{/if}
	{/each}
</div>

<style>
	/* .Blog__readmore {
		font-family: Oswald;
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: normal;
	} */

	.Blog__byline {
		font-family: Oswald;
		font-size: 1rem;
		font-style: normal;
		font-weight: 400;
		line-height: normal;
		letter-spacing: 0.8px;
	}

	/* .Blog__tag {
		color: #99908e;
		text-transform: uppercase;
		font-family: Source Serif Pro;
		font-size: 0.875rem;
		font-style: normal;
		font-weight: 400;
		line-height: normal;
	}

	.Blog__tag:not(:last-child)::after {
		content: ',';
	} */
</style>
