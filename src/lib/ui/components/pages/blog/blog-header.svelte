<script lang="ts">
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import { pageInfo } from '$lib/ui/state-management/store/global';
	export let heading = 'Expert Witness News';
	export let subheading: string | undefined;
	export let description: string | undefined;
</script>

<SectionLayout class="bg-gray-200 py-8 lg:py-16">
	<div class="Blog__headingContainer">
		{#if subheading && $pageInfo === 1}
			<h3 class="Blog__subheading text-center text-fr-charcoal uppercase !text-sm m-0">
				{subheading}
			</h3>
		{/if}
		<h1 class="Blog__heading text-fr-charcoal my-3">
			{#if $pageInfo > 1}
				More from
			{/if}

			{heading}

			{#if $pageInfo > 1}
				{`: Page ${$pageInfo}`}
			{/if}
		</h1>
		{#if description && $pageInfo === 1}
			<div class="Blog__subheading text-center text-fr-charcoal !text-base leading-normal">
				{@html description}
			</div>
		{/if}
	</div>
</SectionLayout>

<style>
	.Blog__heading {
		font-family: Oswald;
		font-size: 2rem;
		font-style: normal;
		font-weight: 700;
		line-height: 42px; /* 131.25% */
	}

	.Blog__subheading {
		font-family: Source Serif Pro;
		font-size: 1.25rem;
		font-weight: 400;
	}

	.Blog__headingContainer {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		max-width: 700px;
		margin: 0 auto;
	}
</style>
