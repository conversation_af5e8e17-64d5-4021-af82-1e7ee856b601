<script lang="ts">
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import BlogArticlesDisplay from './blog-articles-display.svelte';
	import BlogCategoryDisplay from './blog-category-display.svelte';
	import type { PageServerData } from '../../../../../routes/blog/category/[slug]/$types';
	export let data: PageServerData;
</script>

<SectionLayout class="flex flex-col gap-8">
	<div>
		<BlogArticlesDisplay posts={data.postData.posts} />
		<div class="flex w-full py-6">
			{#if data.postData.pageInfo && data.postData.pageInfo.hasPreviousPage}
				<a
					href={`/blog/category/${data.categoryData.category?.slug}?before=${data.postData?.pageInfo?.startCursor}`}
					class="text-center text-fr-charcoal dark:text-fr-white dark:bg-fr-charcoal uppercase text-2xl dark:hover:bg-fr-white bg-fr-white hover:text-fr-white hover:bg-fr-charcoal dark:hover:text-fr-charcoal rounded px-3 py-1 mr-auto"
				>
					Previous
				</a>
			{/if}
			{#if data.postData.pageInfo && data.postData.pageInfo.hasNextPage}
				<a
					href={`/blog/category/${data.categoryData.category?.slug}?after=${data.postData?.pageInfo?.endCursor}`}
					class="text-center text-fr-charcoal dark:text-fr-white dark:bg-fr-charcoal uppercase text-2xl dark:hover:bg-fr-white bg-fr-white hover:text-fr-white hover:bg-fr-charcoal dark:hover:text-fr-charcoal rounded px-3 py-1 ml-auto"
				>
					Next
				</a>
			{/if}
		</div>
	</div>
</SectionLayout>

<style>
	.BlogCategory__heading {
		font-family: Oswald;
		font-size: 1.5rem;
		font-weight: 700;
	}

	.BlogCategory__subheading {
		font-family: Source Serif Pro;
		font-size: 1.25rem;
		font-weight: 400;
	}
</style>
