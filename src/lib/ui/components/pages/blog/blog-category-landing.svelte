<script lang="ts">
	import { onMount } from 'svelte';
	import BlogLayout from '$lib/ui/components/layouts/BlogLayout.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import type { PageServerData } from '../../../../../routes/blog/category/[slug]/$types';

	import LatestPosts from './blog-latest-display.svelte';
	import BlogFeaturedTriple from './blog-featured-triple.svelte';
	import BlogHeader from './blog-header.svelte';
	import RequestExpertForm from '../generic/RequestExpertForm.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import BlogCategoryDisplay from './blog-category-display.svelte';
	import SubscriptionForm from '../generic/SubscriptionForm.svelte';
	import BlogArticlesPagination from './blog-articles-pagination.svelte';
	import BlogSimpleSearchBox from '../../search/BlogSimpleSearchBox.svelte';

	export let data: Omit<PageServerData, 'seoData'>;
	$: ({ categoryData, categoriesData, latestPosts, featuredPosts, postData } = data);

	let scriptLoaded = false;

	onMount(() => {
		const script = document.createElement('script');
		script.type = 'text/javascript';
		script.async = true;
		script.src = 'https://static.klaviyo.com/onsite/js/klaviyo.js?company_id=RX9iKq';
		script.onload = () => {
			scriptLoaded = true;
		};
		document.head.appendChild(script);
	});
</script>

<BlogHeader
	heading={categoryData.category?.name ? `${categoryData.category?.name} News` : ''}
	subheading={categoryData?.category?.blogPageCustomFields?.subheading ?? ''}
	description={categoryData?.category?.blogPageCustomFields?.description ?? ''}
/>

<BlogLayout>
	<SectionLayout class="min-w-[320px] max-w-screen-2xl w-full mx-auto px-6 lg:px-16">
		<BlogCategoryDisplay categories={categoriesData.categories} />
	</SectionLayout>

	<MainLayout class="flex flex-col gap-4 mb-6 lg:mb-8">
		<div class="min-w-[320px] max-w-screen-2xl mx-auto px-6 lg:px-16">
			<BlogFeaturedTriple blogs={featuredPosts.posts} />
			<BlogSimpleSearchBox customPlaceholder="Please Enter Search Term For Blog Articles" />
			<LatestPosts blogs={latestPosts.posts} />
		</div>
	</MainLayout>

	{#if scriptLoaded}
		<SubscriptionForm />
	{/if}

	<SectionLayout class="min-w-[320px] max-w-screen-2xl w-full mx-auto px-6 lg:px-16">
		<BlogArticlesPagination {postData} />
	</SectionLayout>

	<RequestExpertForm />
</BlogLayout>
