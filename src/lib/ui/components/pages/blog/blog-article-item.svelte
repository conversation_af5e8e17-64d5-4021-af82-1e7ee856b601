<script lang="ts">
	export let post;

	$: postCategory = post.categories?.nodes?.map((node: any) => node.name)?.join(' | ') ?? '';
</script>

<a class="Post__content" href={`/blog/${post.slug}`}>
	{#if post?.title}
		<div class="flex flex-col gap-1">
			<img
				class="object-cover aspect-video mb-3"
				src={post.featuredImage?.node.sourceUrl}
				alt={post.featuredImage?.node.description ?? post.title}
			/>

			{#if postCategory}
				<p class="text-fr-royal-blue uppercase font-medium mt-2">
					{postCategory}
				</p>
			{/if}

			<div class="text-fr-charcoal dark:text-fr-white">
				<h4 class="text-2xl my-0 mb-2 line-clamp-3">{@html post.title}</h4>
			</div>
			{#if post.author?.node.name}
				<div class="Blog__byline flex gap-2 text-fr-charcoal dark:text-fr-white">
					<p>By {post.author?.node.name}</p>
				</div>
			{/if}
		</div>
	{/if}
</a>

<style>
	.Blog__byline {
		font-family: Oswald;
		font-size: 1rem;
		font-weight: 400;
		letter-spacing: 0.8px;
	}
</style>
