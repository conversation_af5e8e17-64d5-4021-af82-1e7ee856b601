<script lang="ts">
	import type { PageServerData } from '../../../../../routes/blog/[slug]/$types';
	import ArrowIcon from '$lib/ui/components/icons/icon-arrow.svelte';
	import H4 from '../../system-design/headings/H4.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import SocialShare from '$lib/ui/components/pages/blog/blog-item-social-share.svelte';
	import Button from '../../form-elements/Button.svelte';
	import H2 from '../../system-design/headings/H2.svelte';
	import Link from '../../form-elements/Link.svelte';
	import HtmlRenderer from "../../cms-content-renderer/HtmlRenderer/index.svelte";

	export let data: Omit<PageServerData, 'seoData'>;
</script>

<div class="max-w-3xl md:mx-auto">
	<a href="/blog" class="lg:hidden">
		<div class="flex flex-row my-8 items-center">
			<ArrowIcon />
			<span class="post-item__meta ml-4 text-fr-charcoal dark:text-fr-white">Back to News</span>
		</div>
	</a>
	<div class="post-item__content text-lg text-fr-charcoal dark:text-fr-white">
		<HtmlRenderer
			class="post-item__content__htmlContainer text-lg text-fr-black dark:text-fr-white"
			content={data.post?.content ?? ''}
		/>
	</div>

	<div class="border-t border-solid border-gray-800 pt-6 mt-8">
		<H2>About the Author</H2>
		<Link color="primary" linkUrl={`/author/${data.post?.author?.node.slug}`} class="font-normal text-2xl">
			{data?.post.author?.node.name}
		</Link>
		{#if data.post.author?.node.authorCustomFields?.bio}
			<p class="dark:text-fr-white">{@html data.post.author?.node.authorCustomFields?.bio}</p>
		{/if}
	</div>

	<SocialShare {data} />

	<div class="my-4 flex justify-center">
		<a href="/how-to-submit-articles">
			<Button color="Secondary" class="px-10">Become an Article Contributor</Button>
		</a>
	</div>
</div>

<style>
	.post-item__meta {
		font-family: Oswald;
		font-size: 1.25rem;
		font-weight: 400;
	}

	:global(.post-item__content p) {
		font-size: 1.125rem;
	}

	:global(
			.post-item__content p + p,
			.post-item__content p + blockquote,
			.post-item__content blockquote + p,
			.post-item__content p
		) {
		margin-top: 1rem;
	}

	:global(.post-item__content h2) {
		font-family: Oswald;
		font-size: 1.5rem;
		font-weight: 700;
	}
	:global(.post-item__content h2::before) {
		@apply text-fr-charcoal-rgba mr-1;
		content: "# ";
	}

	:global(.post-item__content h3) {
		font-size: 1.25rem;
	}

	:global(.post-item__content img) {
		margin-bottom: 1rem;
		margin-top: 1rem;
		object-fit: cover;
		width: 100%;
	}

	:global(.post-item__content blockquote) {
		font-family: Oswald;
		font-weight: 400;
		margin: 1.5em 10px;
		padding: 0.5em 10px;
		quotes: '\201C''\201D''\2018''\2019';
	}

	:global(.post-item__content blockquote:before) {
		content: open-quote;
		font-size: 4em;
		line-height: 0.1em;
		margin-right: 0.25em;
		vertical-align: -0.5em;
	}

	:global(.post-item__content blockquote p) {
		display: inline;
	}

	:global(.post-item__content ul) {
		list-style-position: inside;
		list-style-type: disc;
		margin-bottom: 1rem;
		margin-top: 1rem;
		padding-left: 1.5rem;
	}

	:global(.post-item__content ol) {
		list-style-position: inside;
		list-style-type: decimal;
		margin-bottom: 1rem;
		margin-top: 1rem;
		padding-left: 1.5rem;
	}

	:global(.frequently-asked-questions details p) {
		@apply ml-[18px] mb-[18px];
	}
	:global(.frequently-asked-questions details) {
		margin: 10px 10px 10px 20px;
	}

	:global(.post-item__content .disclaimer) {
		@apply border-fr-orange-rgba border-l-4 pl-2;
	}

	:global(.post-item__content table) {
		@apply mt-10 mb-10;
	}

	:global(.post-item__content tr) {
		@apply border-b-2 border-fr-light-gray;
	}

	:global(.post-item__content td) {
		@apply p-2;
	}
</style>
