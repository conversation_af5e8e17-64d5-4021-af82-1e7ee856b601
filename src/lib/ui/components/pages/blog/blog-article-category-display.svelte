<script lang="ts">
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import BlogArticlesDisplay from './blog-articles-display.svelte';
	import type { PageServerData } from '../../../../../routes/author/[slug]/$types';
	export let data: PageServerData;
</script>

<SectionLayout>
	<BlogArticlesDisplay posts={data.postData.posts} />

	<div class="flex w-full py-6">
		{#if data.postData.pageInfo && data.postData.pageInfo.hasPreviousPage}
			<a
				href={`/blog?before=${data.postData?.pageInfo?.startCursor}`}
				class="text-center text-fr-charcoal dark:text-fr-white dark:bg-fr-charcoal uppercase text-2xl dark:hover:bg-fr-white bg-fr-white hover:text-fr-white hover:bg-fr-charcoal dark:hover:text-fr-charcoal rounded px-3 py-1 mr-auto"
			>
				Previous
			</a>
		{/if}
		{#if data.postData.pageInfo && data.postData.pageInfo.hasNextPage}
			<a
				href={`/blog?after=${data.postData?.pageInfo?.endCursor}`}
				class="text-center text-fr-charcoal dark:text-fr-white dark:bg-fr-charcoal uppercase text-2xl dark:hover:bg-fr-white bg-fr-white hover:text-fr-white hover:bg-fr-charcoal dark:hover:text-fr-charcoal rounded px-3 py-1 ml-auto"
			>
				Next
			</a>
		{/if}
	</div>
</SectionLayout>
