<script lang="ts">
	import type { CoreCategoryFragment } from '$lib/shared/api-dtos/external-graphql/generated';
	import { page } from '$app/stores';
	export let categories: CoreCategoryFragment[];
	let currentSlug: string;
	$: currentSlug = $page.params.slug;

	const onChangeNavigate = (e: Event) => {
		const target = e?.target as HTMLSelectElement;
		const slug = target?.value;
		if (slug === 'all-topics') {
			window.location.href = '/blog';
		} else {
			window.location.href = `/blog/category/${slug}`;
		}
	};
</script>

<div
	class="order-first lg:order-last flex flex-col gap-2 lg:px-4 border-solid border-b border-[#a9a9a9] pb-4 mb-4"
>
	<ul
		class="Blog__topicsList flex gap-4 lg:gap-6 overflow-auto text-fr-charcoal dark:text-fr-white"
	>
		<li data-state={!currentSlug ? 'active' : 'inactive'} class="Blog__topic whitespace-nowrap">
			<a href={`/blog`} data-sveltekit-reload>All</a>
		</li>
		{#each categories as category}
			<li data-state={currentSlug === category.slug ? 'active' : 'inactive'} class="Blog__topic">
				<a class="whitespace-nowrap" href={`/blog/category/${category.slug}`} data-sveltekit-reload>{category.name}</a>
			</li>
		{/each}
	</ul>
	<select on:change={onChangeNavigate} class="Blog__topicsSelect block lg:hidden">
		<option value="all-topics">All Topics</option>
		{#each categories as category}
			<option value={category.slug} class="Blog__topic">
				{category.name}
			</option>
		{/each}
	</select>
</div>

<style>
	.Blog__topicsList {
		list-style: none;
		font-weight: 300;
		margin: 0;
		padding: 0;
	}

	.Blog__topic {
		margin-left: 0.25rem;
		font-family: Oswald;
		font-size: 1.25rem;
	}

	.Blog__topic[data-state='active'] {
		font-weight: 700;
	}
</style>
