<script lang="ts">
	import BlogLayout from '$lib/ui/components/layouts/BlogLayout.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import type { PageServerData } from '../../../../../routes/blog/tag/[slug]/$types';
	import BlogByTagArticleCategoryDisplay from './blog-by-tag-article-category-display.svelte';
	import BlogHeader from './blog-header.svelte';
	export let data: PageServerData;
</script>

<BlogLayout>
	<MainLayout class="flex flex-col gap-4">
		<BlogHeader />
		<BlogByTagArticleCategoryDisplay {data} />
	</MainLayout>
</BlogLayout>
