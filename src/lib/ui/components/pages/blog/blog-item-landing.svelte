<script lang="ts">
	import BlogLayout from '$lib/ui/components/layouts/BlogLayout.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import type { PageServerData } from '../../../../../routes/blog/[slug]/$types';
	import BlogArticleDisplay from './blog-item-display.svelte';
	import BlogArticleHeader from './blog-item-header.svelte';
	import RecommendedArticles from './blog-item-recommended-articles.svelte';
	import RequestExpertForm from '../generic/RequestExpertForm.svelte';
	import BlogItemBreadcrumb from './blog-item-breadcrumb.svelte';

	export let data: Omit<PageServerData, 'seoData'>;
</script>

<BlogLayout>
	<MainLayout class="flex flex-col px-4">
		<BlogItemBreadcrumb {data} />
		<BlogArticleHeader {data} />
		<BlogArticleDisplay {data} />
		<RecommendedArticles {data} />
		<RequestExpertForm />
	</MainLayout>
</BlogLayout>
