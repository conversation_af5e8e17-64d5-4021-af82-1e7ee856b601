<script lang="ts">
	import BlogArticleItem from './blog-article-item.svelte';

	export let blogs: any[] = [];
</script>

{#if blogs.length}
	<div class="mt-10 hidden lg:block">
		<div class="Blogs__Featured gap-x-8 gap-y-12">
			{#each blogs as post}
				<BlogArticleItem {post} />
			{/each}
		</div>
	</div>
{/if}

<style>
	.Blogs__Featured {
		display: grid;
		grid-template-areas: 'first second' 'first third';
		grid-template-rows: 1fr 1fr;
		grid-template-columns: 1fr 1fr;
	}

	:global(.Blogs__Featured .Post__content:first-of-type) {
		grid-area: first;
	}

	:global(.Blogs__Featured .Post__content:first-of-type img) {
		@apply aspect-square object-center;
	}

	:global(.Blogs__Featured .Post__content:nth-of-type(2)) {
		grid-area: second;
	}

	:global(.Blogs__Featured .Post__content:nth-of-type(2) img),
	:global(.Blogs__Featured .Post__content:nth-of-type(3) img) {
		@apply aspect-[2/1];
	}

	:global(.Blogs__Featured .Post__content:nth-of-type(3)) {
		grid-area: third;
	}
</style>
