<script lang="ts">
	import type { PageServerData } from '../../../../../routes/blog/[slug]/$types';
	import { getSocialShareLink } from '$lib/utils/getSocialShareLink';
	import FacebookIcon from '$lib/ui/components/icons/icon-facebook.svelte';
	import LinkedinIcon from '$lib/ui/components/icons/icon-linkedin.svelte';
	import TwitterIcon from '$lib/ui/components/icons/icon-twitter.svelte';
	import EmailIcon from '$lib/ui/components/icons/icon-email.svelte';
	export let data: Omit<PageServerData, 'seoData'>;
</script>

<div class="mt-2 flex items-center gap-1">
	<div class="text-fr-charcoal dark:text-fr-white font-oswald text-base font-bold">Share Via:</div>
	<div class="ml-4 flex gap-4">
		<a href={getSocialShareLink('twitter', data.post)}>
			<TwitterIcon />
		</a>
		<a href={getSocialShareLink('facebook', data.post)}>
			<FacebookIcon />
		</a>
		<a href={getSocialShareLink('linkedin', data.post)}>
			<LinkedinIcon />
		</a>
		<a href={getSocialShareLink('email', data.post)} title="Share by Email">
			<EmailIcon />
		</a>
	</div>
</div>

<style>
	:global(.post-item__social-item) {
		width: 2rem;
		height: 2rem;
	}
</style>
