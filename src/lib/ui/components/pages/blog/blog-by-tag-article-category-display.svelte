<script lang="ts">
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import BlogArticlesDisplay from './blog-articles-display.svelte';
	import BlogCategoryDisplay from './blog-category-display.svelte';
	import type { PageServerData } from '../../../../../routes/blog/tag/[slug]/$types';
	import { page } from '$app/stores';
	let currentSlug: string;
	$: currentSlug = $page.params.slug;
	export let data: PageServerData;
</script>

<SectionLayout class="flex flex-col gap-8 lg:grid grid-cols-[1fr,_max-content]">
	<BlogCategoryDisplay categories={data.categoriesData.categories} />
	<div>
		<div class="flex flex-col pr-12 pb-4 gap-4">
			<h4 class="BlogTag__heading uppercase m-0 text-fr-charcoal dark:text-fr-white">#{currentSlug}</h4>
		</div>
		<BlogArticlesDisplay posts={data.postData.posts} />
		<div class="flex w-full py-6">
			{#if data.postData.pageInfo && data.postData.pageInfo.hasPreviousPage}
				<a
					href={`/blog/tag/${currentSlug}?before=${data.postData?.pageInfo?.startCursor}`}
					class="text-center text-fr-charcoal dark:text-fr-white dark:bg-fr-charcoal uppercase text-2xl dark:hover:bg-fr-white bg-fr-white hover:text-fr-white hover:bg-fr-charcoal dark:hover:text-fr-charcoal rounded px-3 py-1 mr-auto"
				>
					Previous
				</a>
			{/if}
			{#if data.postData.pageInfo && data.postData.pageInfo.hasNextPage}
				<a
					href={`/blog/tag/${currentSlug}?after=${data.postData?.pageInfo?.endCursor}`}
					class="text-center text-fr-charcoal dark:text-fr-white dark:bg-fr-charcoal uppercase text-2xl dark:hover:bg-fr-white bg-fr-white hover:text-fr-white hover:bg-fr-charcoal dark:hover:text-fr-charcoal rounded px-3 py-1 ml-auto"
				>
					Next
				</a>
			{/if}
		</div>
	</div>
</SectionLayout>

<style>
	.BlogTag__heading {
		font-family: Oswald;
		font-size: 1.5rem;
		font-weight: 700;
	}
</style>
