import type { TBecomeExpertFormFieldsDto } from "$lib/shared/api-dtos/internal-rest/contact-dto";

export type TTakePlaintiff = {
    title: string;
    slug: string;
}

export type TBecomeExpertFormValues = Omit<
    TBecomeExpertFormFieldsDto,
    'firstName' | 'lastName' | 'email' | 'confirmEmail' | 'phone' | 'description'
> & {
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    confirmEmail: string | null;
    phone: string | null;
    description: string | null;
};

export type TBecomeExpertFormErrors = {
    [key in keyof (TBecomeExpertFormFieldsDto)]?: string | null;
} & {form?: string | null;}

export type TBecomeExpertFormStatus = {
    success: boolean | null;
    message?: string | undefined;
}

export type TBecomeExpertForm = {
    values: TBecomeExpertFormValues,
    errors: TBecomeExpertFormErrors
};