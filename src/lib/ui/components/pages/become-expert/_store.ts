import { createWritablePersistStore } from "$lib/ui/state-management/store/store";
import { derived, writable } from "svelte/store";
import { becomeExpertFormInitErrors, becomeExpertFormInitStatus, becomeExpertFormInitValues } from "./_constants";
import type { TBecomeExpertFormErrors, TBecomeExpertFormStatus, TBecomeExpertFormValues } from "./_types-interfaces";

//TODO: Transform this into a generic Form Store
const becomeExpertFormValuesStore = createWritablePersistStore<TBecomeExpertFormValues>('become-expert-form-values', becomeExpertFormInitValues);
const becomeExpertFormErrorsStore = writable<TBecomeExpertFormErrors>(becomeExpertFormInitErrors);
const becomeExpertFormStatusStore = writable<TBecomeExpertFormStatus>(becomeExpertFormInitStatus);

export const becomeExpertFormStore = derived(
    [becomeExpertFormValuesStore, becomeExpertFormErrorsStore, becomeExpertFormStatusStore], 
    ([$becomeExpertFormValuesStore, $becomeExpertFormErrorsStore]) => {
      return {
        formValues: becomeExpertFormValuesStore,
        formHasValues: () => $becomeExpertFormValuesStore.firstName ||
            $becomeExpertFormValuesStore.lastName ||
            $becomeExpertFormValuesStore.email ||
            $becomeExpertFormValuesStore.confirmEmail ||
            $becomeExpertFormValuesStore.phone ||
            $becomeExpertFormValuesStore.address ||
            $becomeExpertFormValuesStore.city ||
            $becomeExpertFormValuesStore.state ||
            $becomeExpertFormValuesStore.zipCode ||
            $becomeExpertFormValuesStore.takePlaintiff,
        formErrors: becomeExpertFormErrorsStore,
        formHasErrors: () => {
          let formKey: keyof typeof $becomeExpertFormErrorsStore;
          for (formKey in $becomeExpertFormErrorsStore) {
              if (formKey !== 'form' && $becomeExpertFormErrorsStore[formKey]) return true;
          }
          
          return false;
        },
        formStatus: becomeExpertFormStatusStore,
      };
    }
  );
