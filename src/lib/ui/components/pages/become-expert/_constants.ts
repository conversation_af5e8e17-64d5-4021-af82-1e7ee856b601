import type { TBecomeExpertFormErrors, TBecomeExpertFormStatus, TBecomeExpertFormValues, TTakePlaintiff } from "./_types-interfaces";

export const takePlaintiff: TTakePlaintiff[] = [
    { title: 'Yes', slug: 'Yes' },
    { title: 'No', slug: 'No' },
];

export const becomeExpertFormInitValues: TBecomeExpertFormValues = {
    firstName: null,
    lastName: null,
    email: null,
    confirmEmail: null,
    phone: null,
    takePlaintiff: takePlaintiff[0].slug, //Attorney
    description: null,
    address: null,
    city: null,
    state: null,
    zipCode: null,
}

export const becomeExpertFormInitErrors: TBecomeExpertFormErrors = {
    form: null,
    firstName: null,
    lastName: null,
    email: null,
    confirmEmail: null,
    phone: null,
    description: null,
    address: null,
    city: null,
    state: null,
    zipCode: null,
    takePlaintiff: null,
}

export const becomeExpertFormInitStatus: TBecomeExpertFormStatus = {
    success: null,
}; 