<script lang="ts">
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import type { SubmitFunction } from '@sveltejs/kit';
	import { fade } from 'svelte/transition';
	import { onMount } from 'svelte';

	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import Select from '$lib/ui/components/form-elements/inputs/Select/index.svelte';
	import TextField from '$lib/ui/components/form-elements/inputs/TextField/index.svelte';
	import Button from '../../form-elements/Button.svelte';
	import SmallText from '../../system-design/texts/SmallText.svelte';
	import { becomeExpertFormStore } from './_store';
	import Modal from './../../over-layer/Modal/index.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import { takePlaintiff } from './_constants';
	import Paragraph from '../../system-design/texts/Paragraph.svelte';
	import IoMdCloseCircleOutline from 'svelte-icons/io/IoMdCloseCircleOutline.svelte';
	import IoMdCheckmarkCircleOutline from 'svelte-icons/io/IoMdCheckmarkCircleOutline.svelte';
	import type { TBecomeExpertFormErrors } from './_types-interfaces';
	import { BecomeExpertFormSchema, BecomeExpertModelSchema } from '$lib/shared/validation/schemas/contact-forms/become-expert.schema';
	import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import H2 from '../../system-design/headings/H2.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';

	export let page: TExtendedPageDto | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	$: ({ formValues, formErrors, formHasValues, formHasErrors, formStatus } =
		$becomeExpertFormStore);
	$: _title = page?.title;
	$: _description = page?.description
	$: _subtitle = page?.subtitle;
	let scrollY: any;
	let isSubmitting: boolean | null = null;
	let toggleModalOpened: boolean = false;

	const clearForm = async () => {
		if (formHasValues()) {
			const emptyFormValues = $formValues;
			let formField: keyof typeof emptyFormValues;
			for (formField in emptyFormValues) {
				$formValues = { ...$formValues, [formField]: null };
			}

			// This will remove the Query Parameters from the URL in case they exists.
			await goto('become-expert-witness/');
		}
	};

	const _validateFormField = (value: string, field: string) => {
		$formValues = { ...$formValues, [field]: value };
		const schemaShape = BecomeExpertFormSchema.shape;
		if(field in schemaShape) {
			const parsedFieldRes =
				BecomeExpertFormSchema.shape[field as keyof typeof schemaShape].safeParse(value);
			if (!parsedFieldRes.success) {
				const errorMessage = parsedFieldRes.error.issues[0].message;
				$formErrors = { ...$formErrors, [field]: errorMessage };
			} else $formErrors = { ...$formErrors, [field]: null };
		}
	};

	const onTextFieldPressKey = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		_validateFormField(value, fieldId);
	};

	const onCleanField = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		_validateFormField(value, fieldId);
	};

	const onSubmit: SubmitFunction = (formRequest: any) => {
		if (!isSubmitting) {
			isSubmitting = true;
			$formErrors = {};
			scrollY = 0;
			const parsedFormRes = BecomeExpertModelSchema.safeParse($formValues);
			if (!parsedFormRes.success) {
				parsedFormRes.error.issues.forEach((issue) => {
					$formErrors = { ...$formErrors, [issue.path[0]]: issue.message };
				});

				formRequest.cancel();
				$formStatus = {
					success: false,
					message: 'The form has invalid or empty values. Please check.'
				};
				isSubmitting = false;
				toggleModalOpened = true;
			}

			return async ({ result: { data } }: any) => {
				const {success, ...rest} = data;
				if (!success) {
					const {isAValidationError, errors} = rest;
					if (isAValidationError) {
						$formStatus = {
							success: false,
							message: 'The form has invalid or empty values. Please check.',
						};
					} else {
						$formStatus = {
							success: false,
							message: 'Your request could not be processed, please try again later'
						};
					}
					if(errors) $formErrors = errors as TBecomeExpertFormErrors;

					isSubmitting = false;
					toggleModalOpened = true;
				} else {
					const {message} = rest;
					$formStatus = {
						success: true,
						message,
					};
					isSubmitting = false;
					toggleModalOpened = true;
					setTimeout(async() => {
						await goto('/thank-you-becoming-an-expert');
					}, 1500);
				}
			};
		}
	};

	const closeModal = () => {
		toggleModalOpened = false;
		$formStatus = { success: null };
	};

	onMount(() => {
		$becomeExpertFormStore.formValues.useLocalStorage();
	});
</script>

<!-- svelte-ignore missing-declaration -->
<PageLayout class="BecomeExpertPage flex flex-col h-full">
	<PageHeader
		title={_title}
		class="BecomeExpertPage__header text-center"
		breadcrumbs={breadcrumbs}
	>
		<H2 slot="richContent" class="normal-case mb-0">{_subtitle}</H2>
	</PageHeader>

	<MainLayout>
		<SectionLayout class="BecomeExpertPage__MainSection__container flex flex-col">
			<HTMLRenderer content={_description} class="mb-5 text-fr-charcoal dark:text-fr-white" />
		</SectionLayout>
		<form
			method="POST"
			use:enhance={onSubmit}
			class={`BecomeExpertPage__form flex flex-col gap-10 ${isSubmitting ? 'animate-pulse' : ''}`}
		>
			<!-- NAME  fields -->
			<div class="BecomeExpertPage__form__row-container flex flex-col gap-5 lg:flex-row">
				<div class="BecomeExpertPage__form__field-container text-left w-full lg:w-1/2">
					<TextField
						id="firstName"
						name="firstName"
						label="First Name"
						value={$formValues.firstName}
						disabled={isSubmitting}
						maxlength=100
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.firstName}
							<div class="text-fr-orange">{$formErrors.firstName}</div>
						{/if}
					</SmallText>
				</div>
				<div class="BecomeExpertPage__form__field-container text-left w-full lg:w-1/2">
					<TextField
						id="lastName"
						name="lastName"
						label="Last Name"
						value={$formValues.lastName}
						disabled={isSubmitting}
						maxlength=100
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.lastName}
							<div class="text-fr-orange">{$formErrors.lastName}</div>
						{/if}
					</SmallText>
				</div>
			</div>

			<!-- EMAIL  fields -->
			<div class="BecomeExpertPage__form__row-container flex flex-col gap-5 lg:flex-row">
				<div class="BecomeExpertPage__form__field-container text-left w-full lg:w-1/2">
					<TextField
						id="email"
						name="email"
						label="Email"
						value={$formValues.email}
						disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.email}
							<div class="text-fr-orange">{$formErrors.email}</div>
						{/if}
					</SmallText>
				</div>
				<div class="BecomeExpertPage__form__field-container text-left w-full lg:w-1/2">
					<TextField
						id="confirmEmail"
						name="confirmEmail"
						label="Confirm Email"
						value={$formValues.confirmEmail}
						disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.confirmEmail}
							<div class="text-fr-orange">{$formErrors.confirmEmail}</div>
						{/if}
					</SmallText>
				</div>
			</div>

			<!-- PHONE -->
			<div class="BecomeExpertPage__form__field-container text-left">
				<TextField
					id="phone"
					name="phone"
					label="Phone"
					value={$formValues.phone}
					disabled={isSubmitting}
					maxlength=20
					useCleanIcon
					required
					on:onKeyUp={onTextFieldPressKey}
					on:onCleanField={onCleanField}
				/>
				<SmallText class="mt-1 h-4 font-oswald">
					{#if $formErrors.phone}
						<div class="text-fr-orange">{$formErrors.phone}</div>
					{/if}
				</SmallText>
			</div>

			<!-- ADDRESS -->
			<div class="BecomeExpertPage__form__field-container text-left">
				<label for="address" class="dark:text-fr-white">Address</label>
				<TextField
					id="address"
					name="address"
					value={$formValues.address}
					disabled={isSubmitting}
					maxlength=150
					useCleanIcon
					on:onKeyUp={onTextFieldPressKey}
					on:onCleanField={onCleanField}
				/>
				<SmallText class="mt-1 h-4 font-oswald">
					{#if $formErrors.address}
						<div class="text-fr-orange">{$formErrors.address}</div>
					{/if}
				</SmallText>
			</div>

			<!-- ADDRESS complementary  fields -->
			<div class="BecomeExpertPage__form__row-container flex flex-col gap-5 lg:flex-row">
				<div class="BecomeExpertPage__form__field-container text-left w-full lg:w-1/3">
					<label for="city" class="dark:text-fr-white">City</label>
					<TextField
						id="city"
						name="city"
						value={$formValues.city}
						disabled={isSubmitting}
						maxlength=100
						useCleanIcon
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.city}
							<div class="text-fr-orange">{$formErrors.city}</div>
						{/if}
					</SmallText>
				</div>
				<div class="BecomeExpertPage__form__field-container text-left w-full lg:w-1/3">
					<label for="state" class="dark:text-fr-white">State</label>
					<TextField
						id="state"
						name="state"
						value={$formValues.state}
						disabled={isSubmitting}
						maxlength=100
						useCleanIcon
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.state}
							<div class="text-fr-orange">{$formErrors.state}</div>
						{/if}
					</SmallText>
				</div>
				<div class="BecomeExpertPage__form__field-container text-left w-full lg:w-1/3">
					<label for="zipCode" class="dark:text-fr-white">Zip Code</label>
					<TextField
						id="zipCode"
						name="zipCode"
						value={$formValues.zipCode}
						disabled={isSubmitting}
						maxlength=20
						useCleanIcon
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.zipCode}
							<div class="text-fr-orange">{$formErrors.zipCode}</div>
						{/if}
					</SmallText>
				</div>
			</div>

			<!-- DESCRIPTION -->
			<div class="BecomeExpertPage__form__field-container text-left">
				<label for="description" class=" flex items-baseline dark:text-fr-white">
					Short Description of Your Expertise
					<SmallText class="ml-1">(*)</SmallText>
				</label>
				<div
					class="TextArea__container flex items-center relative rounded-[0.3125rem] border-2 border-fr-charcoal dark:border-fr-white dark:bg-inherit dark:text-fr-white"
				>
					<!-- TODO: Create a form-element component for text-are and replace this -->
					<textarea
						id="description"
						name="description"
						disabled={isSubmitting}
						class="TextArea outline-none p-4 w-full dark:bg-fr-charcoal dark:text-fr-white resize-none"
						rows="5"
						maxlength="2000"
						bind:value={$formValues.description}
					/>
				</div>
				<SmallText class="mt-1 h-4 font-oswald block">
					{#if $formErrors.description}
						<div class="text-fr-orange float-left">{$formErrors.description}</div>
					{/if}
					<div class="text-fr-charcoal dark:text-fr-white font-thin text-end float-right">
						{$formValues.description?.length ?? 0}/2000.
					</div>
				</SmallText>
			</div>

			<!-- TAKE PLAINTIFF -->
			<div class="BecomeExpertPage__form__field-container text-left">
				<Select
					id="takePlaintiff"
					name="takePlaintiff"
					label="Will you take Plaintiff & Defense Cases?"
					disabled={isSubmitting}
					value={$formValues.takePlaintiff}
					options={takePlaintiff}
					on:onChange={(event) => onTextFieldPressKey(event)}
					on:onCleanField={onCleanField}
				/>
				<SmallText class="mt-1 h-4 font-oswald">
					{#if $formErrors.takePlaintiff}
						<div class="dark:text-fr-orange">{$formErrors.takePlaintiff}</div>
					{/if}
				</SmallText>
			</div>
			<Button
				class="BecomeExpertPage__form__submit-cta"
				type="submit"
				disabled={isSubmitting === true}>Submit Form</Button
			>
			<Button
				color="Link"
				class="BecomeExpertPage__form__clear-cta mx-0 capitalize max-w-max self-center px-1 lg:w-auto"
				disabled={!formHasValues() || isSubmitting === true}
				on:click={() => {
					clearForm();
				}}
			>
				Clear Form
			</Button>
		</form>
	</MainLayout>
</PageLayout>

<Modal
	isOpened={toggleModalOpened}
	title={`Form Submission ${$formStatus.success ? 'Successful!' : 'Failed'}`}
	on:onClose={closeModal}
	useClose={$formStatus.success ? false : true}
>
	<div class="BecomeExpertPage__notificationModal__content flex flex-col w-full justify-center" slot="content">
			<div class="flex min-h-[76px] items-center" transition:fade={{ duration: 500 }}
			>
				{#if $formStatus.success === true}
					<div class="h-[50px] mr-3 text-fr-royal-blue">
						<IoMdCheckmarkCircleOutline />
					</div>
					<HTMLRenderer
						content={'<div class="flex flex-col"><p>Your request was sent correctly.</p><p class="animate-pulse"> Redirecting...</p></div>'}
						class="max-w-[400px] mt-2 dark:text-fr-white mb-4"
					/>
				{:else if $formStatus.success === false}
					<div class="h-[50px] mr-3 text-fr-orange">
						<IoMdCloseCircleOutline />
					</div>
					<HTMLRenderer
						content={$formStatus.message}
						class="max-w-[400px] mt-2 dark:text-fr-white mb-4"
					/>
				{/if}
			</div>
	</div>
</Modal>

<svelte:window bind:scrollY />

<style>
	:global(.BecomeExpertPage__MainSection__container ul) {
		list-style-type: disc;
		padding: 0 0 23px 1em;
		line-height: 26px;
		padding-left: 40px;
	}
</style>
