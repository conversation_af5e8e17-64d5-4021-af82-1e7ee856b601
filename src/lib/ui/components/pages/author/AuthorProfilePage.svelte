<script lang="ts">
	import BlogLayout from '$lib/ui/components/layouts/BlogLayout.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import type { PageServerData } from '../../../../../routes/author/[slug]/$types';

	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import RequestExpertForm from '../generic/RequestExpertForm.svelte';
	import H1 from '../../system-design/headings/H1.svelte';
	import H2 from '../../system-design/headings/H2.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import BlogArticlesDisplay from '../blog/blog-articles-display.svelte';
	import FacebookIcon from '$lib/ui/components/icons/icon-facebook.svelte';
	import LinkedinIcon from '$lib/ui/components/icons/icon-linkedin.svelte';
	import TwitterIcon from '$lib/ui/components/icons/icon-twitter.svelte';

	export let data: PageServerData;

	$: ({
		userData: { user }
	} = data);
</script>

<div class="py-8 lg:py-16 bg-gray-50 mt-4">
	<H1 class="text-center">{`${user?.firstName} ${user?.lastName}`}</H1>
	<H3 class="text-center">{user?.name}</H3>
</div>

<BlogLayout>
	<MainLayout class="flex flex-col gap-4">
		<SectionLayout class="min-w-[320px] max-w-screen-2xl w-full mx-auto px-6 lg:px-16">
			<div>
				<div class="flex flex-col lg:grid lg:grid-cols-[3fr__1fr] lg:gap-16 items-start gap-4">
					<div>
						<H3>{`${user?.firstName} ${user?.lastName}`}</H3>
						{@html user?.authorCustomFields?.bio}

						{#if user?.authorCustomFields?.expertise}
							<H3>Expertise</H3>
							<ul class="list-disc list-inside">
								{#each user?.authorCustomFields?.expertise as item}
									<li>
										{item?.detail}
									</li>
								{/each}
							</ul>
						{/if}

						{#if user?.authorCustomFields?.articleTopics}
							<H3>Write Articles Topics</H3>
							<ul class="list-disc list-inside">
								{#each user?.authorCustomFields?.articleTopics as item}
									<li>
										{#if item?.topic?.[0]?.slug && item?.topic?.[0]?.name}
											<a href={`/blog/category/${item?.topic?.[0]?.slug}`} class="underline">
												{item?.topic?.[0]?.name}
											</a>
										{/if}
									</li>
								{/each}
							</ul>
						{/if}

						{#if user?.authorCustomFields?.education}
							<H3>Education</H3>
							<ul class="list-disc list-inside">
								{#each user?.authorCustomFields?.education as item}
									<li>
										{item?.detail}
									</li>
								{/each}
							</ul>
						{/if}

						{#if user?.authorCustomFields?.featuredArticles}
							<H3>{`${user.firstName}'s Picks (featured articles)`}</H3>
							<ul class="list-disc list-inside">
								{#each user?.authorCustomFields?.featuredArticles as article}
									<li>
										<a href={`/blog/${article?.slug}`} class="underline">
											{article?.title}
										</a>
									</li>
								{/each}
							</ul>
						{/if}

						{#if user?.authorCustomFields?.authorFeatured}
							<H3>{`${user.firstName} is Featured In`}</H3>
							<ul class="list-disc list-inside">
								{#each user?.authorCustomFields?.authorFeatured as article}
									<li>
										<a href={article?.link} class="underline">
											{article?.name}
										</a>
									</li>
								{/each}
							</ul>
						{/if}

						{#if user?.authorCustomFields?.authorLinks}
							<H3>{`${user.firstName} Links`}</H3>
							<ul class="flex items-center gap-3">
								{#each user?.authorCustomFields?.authorLinks as article, index}
									<li>
										<a href={article?.link} class="underline">
											{article?.name}
										</a>
										{#if index < user?.authorCustomFields?.authorLinks.length - 1}
											<span class="pl-3">|</span>
										{/if}
									</li>
								{/each}
							</ul>
						{/if}

						{#if user?.authorCustomFields?.socials}
							<H3>{`Connect with ${user.firstName}`}</H3>

							<div class="flex items-center gap-3 text-black">
								{#each user?.authorCustomFields?.socials as social}
									{#if social?.profileUrl && social?.icon}
										<a href={social?.profileUrl}>
											{#if social?.icon === 'x'}
												<TwitterIcon />
											{:else if social.icon === 'facebook'}
												<FacebookIcon />
											{:else}
												<LinkedinIcon />
											{/if}
										</a>
									{/if}
								{/each}
							</div>
						{/if}
					</div>

					<div>
						<img src={user?.avatar?.url} alt={user?.name} class="w-full h-auto" />
					</div>
				</div>
			</div>

			<div class="border-t broder-gray-600 pt-10 mt-10">
				<H2>The Latest From {user?.firstName}</H2>

				<BlogArticlesDisplay posts={data.postData.posts} />
			</div>
		</SectionLayout>
	</MainLayout>
</BlogLayout>

<RequestExpertForm />
