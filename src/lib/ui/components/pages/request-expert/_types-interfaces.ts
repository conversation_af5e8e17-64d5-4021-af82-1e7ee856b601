import type { TRequestExpertFormFieldsDto } from "$lib/shared/api-dtos/internal-rest/contact-dto";

export type TRequesterPosition = {
    title: string;
    slug: string;
}

export type TTypesOfCase = {
    title: string;
    slug: string;
}

export type TRequestExpertFormValues = Omit<
    TRequestExpertFormFieldsDto,
    'firstName' | 'lastName' | 'email' | 'confirmEmail' | 'phone' | 'position' | 'typeOfCase' | 'description'
> & {
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    confirmEmail: string | null;
    phone: string | null;
    position: string | null;
    typeOfCase: string | null;
    description: string | null;
};

export type TRequestExpertFormErrors = {
    [key in keyof (TRequestExpertFormFieldsDto)]?: string | null;
} & {form?: string | null;}

export type TRequestExpertFormStatus = {
    success: boolean | null;
    message?: string | undefined;
}

export type TRequestExpertForm = {
    values: TRequestExpertFormValues,
    errors: TRequestExpertFormErrors
};