import { createWritablePersistStore } from "$lib/ui/state-management/store/store";
import { derived, writable } from "svelte/store";
import { requestExpertFormInitErrors, requestExpertFormInitStatus, requestExpertFormInitValues } from "./_consts";
import type { TRequestExpertFormErrors, TRequestExpertFormStatus, TRequestExpertFormValues } from "./_types-interfaces";

//TODO: Transform this into a generic Form Store
const requestExpertFormValuesStore = createWritablePersistStore<TRequestExpertFormValues>('request-expert-form-values', requestExpertFormInitValues);
const requestExpertFormErrorsStore = writable<TRequestExpertFormErrors>(requestExpertFormInitErrors);
const requestExpertFormStatusStore = writable<TRequestExpertFormStatus>(requestExpertFormInitStatus);

export const requestExpertFormStore = derived(
    [requestExpertFormValuesStore, requestExpertFormErrorsStore, requestExpertFormStatusStore], 
    ([$requestExpertFormValuesStore, $requestExpertFormErrorsStore]) => {
      return {
        formValues: requestExpertFormValuesStore,
        formHasValues: () => $requestExpertFormValuesStore.firstName ||
            $requestExpertFormValuesStore.lastName ||
            $requestExpertFormValuesStore.email ||
            $requestExpertFormValuesStore.confirmEmail ||
            $requestExpertFormValuesStore.expertLink ||
            $requestExpertFormValuesStore.description ||
            $requestExpertFormValuesStore.caseCaption ||
            $requestExpertFormValuesStore.phone ||
            $requestExpertFormValuesStore.company ||
            $requestExpertFormValuesStore.position ||
            $requestExpertFormValuesStore.address ||
            $requestExpertFormValuesStore.city ||
            $requestExpertFormValuesStore.state ||
            $requestExpertFormValuesStore.zipCode ||
            $requestExpertFormValuesStore.timeFrame ||
            $requestExpertFormValuesStore.typeOfCase,
        formErrors: requestExpertFormErrorsStore,
        formHasErrors: () => {
          let formKey: keyof typeof $requestExpertFormErrorsStore;
          for (formKey in $requestExpertFormErrorsStore) {
              if (formKey !== 'form' && $requestExpertFormErrorsStore[formKey]) return true;
          }
          
          return false;
        },
        formStatus: requestExpertFormStatusStore,
      };
    }
  );
