<script lang="ts">
    import { goto } from "$app/navigation";
	import { enhance } from "$app/forms";
	import type { SubmitFunction } from "@sveltejs/kit";
    import IoMdCheckmarkCircleOutline from 'svelte-icons/io/IoMdCheckmarkCircleOutline.svelte'
    import IoMdCloseCircleOutline from 'svelte-icons/io/IoMdCloseCircleOutline.svelte'
	import { fade } from "svelte/transition";
    import { onMount } from "svelte";

	import type { TRequestExpertPageDataDefaultValues } from "../../../../../routes/request-expert-witness/+page.server";
	import PageHeader from "../../headers/PageHeader.svelte";
	import MainLayout from "../../layouts/MainLayout.svelte";
	import PageLayout from "../../layouts/PageLayout.svelte";
    import Select from '$lib/ui/components/form-elements/inputs/Select/index.svelte';
    import TextField from '$lib/ui/components/form-elements/inputs/TextField/index.svelte';
	import Button from "../../form-elements/Button.svelte";
	import { requesterPositions, typesOfCase } from "./_consts";
	import SmallText from "../../system-design/texts/SmallText.svelte";
	import { requestExpertFormStore } from "./_store";
    import Modal from './../../over-layer/Modal/index.svelte';
	import { theme } from "$lib/ui/state-management/store/theme";
	import H3 from "../../system-design/headings/H3.svelte";
    import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import {RequestExpertFormSchema, RequestExpertModelSchema} from "$lib/shared/validation/schemas/contact-forms/request-expert.schema";
	import Link from "../../form-elements/Link.svelte";
	import type { TRequestExpertFormErrors } from "./_types-interfaces";

    export let pageDefaultValues: TRequestExpertPageDataDefaultValues | null = null;
    $: ({formValues, formErrors, formHasValues, formHasErrors, formStatus} = $requestExpertFormStore);
    $: _isReferringSpecificExpert = pageDefaultValues?.requestedExpertLink;
    let scrollY: any;
    let isSubmitting: boolean | null = null;
	let toggleModalOpened: boolean = false;
  
    const clearForm = async () => {
		if (formHasValues()) {
            const emptyFormValues = $formValues;
			let formField: keyof typeof emptyFormValues;
            for (formField in emptyFormValues) {
                if(_isReferringSpecificExpert && formField === 'expertLink')
                    continue;

                $formValues = {...$formValues, [formField]:  null}
            }
            
            // This will remove the Query Parameters from the URL in case they exists when Request An Expert only
            if(!_isReferringSpecificExpert) await goto('request-expert-witness/');
		}
	};

    const _validateFormField = (value: string, field: string) => {
        $formValues = {...$formValues, [field]: value};
        const schemaShape = RequestExpertFormSchema.shape;
        if(field in schemaShape) {
            const parsedFieldRes= RequestExpertFormSchema.shape[field as keyof typeof schemaShape].safeParse(value);
            if (!parsedFieldRes.success) {
                const errorMessage = parsedFieldRes.error.issues[0].message;
                $formErrors = {...$formErrors, [field]: errorMessage};
            } 
            else $formErrors = {...$formErrors, [field]: null};
        }
    }

    const onTextFieldPressKey = async (
		event: CustomEvent
	) => {
		const { value, fieldId } = event.detail;
        _validateFormField(value, fieldId);
	};

    const onCleanField = async (
		event: CustomEvent
	) => {
		const { value, fieldId } = event.detail;
        _validateFormField(value, fieldId);
	};

    const onSubmit: SubmitFunction = (formRequest: any) => {
        if (!isSubmitting) {
            isSubmitting = true;
            $formErrors = {};
            scrollY = 0;
            const parsedFormRes = RequestExpertModelSchema.safeParse($formValues);
            if (!parsedFormRes.success) {
                parsedFormRes.error.issues.forEach(issue => {
                    $formErrors = {...$formErrors, [issue.path[0]]: issue.message}
                });

                formRequest.cancel();
                $formStatus = {
                    success: false,
                    message: 'The form has invalid or empty values. Please check.'
                }
                isSubmitting = false;
                toggleModalOpened = true;
            }

            return async ({result:{data}}: any) => {
                const {success, ...rest} = data;
				if (!success) {
					const {isAValidationError, errors} = rest;
					if (isAValidationError) {
						$formStatus = {
							success: false,
							message: 'The form has invalid or empty values. Please check.',
						};
					} else {
						$formStatus = {
							success: false,
							message: 'Your request could not be processed, please try again later'
						};
					}
					if(errors) $formErrors = errors as TRequestExpertFormErrors;

					isSubmitting = false;
					toggleModalOpened = true;
				} else {
					const {message} = rest;
					$formStatus = {
						success: true,
						message,
					};
					isSubmitting = false;
					toggleModalOpened = true;
					setTimeout(async() => {
						await goto('/thank-you-requesting-an-expert');
					}, 1500);
				}
            }
        }
    }

    const closeModal = () => {
		toggleModalOpened = false;
		$formStatus = { success: null };
	};

    onMount(() => {
        $requestExpertFormStore.formValues.useLocalStorage();
        $formValues = {
            ...$formValues,
            firstName: pageDefaultValues?.requesterFirstName ?? $formValues.firstName,
            lastName: pageDefaultValues?.requesterLastName ?? $formValues.lastName,
            email: pageDefaultValues?.requesterEmail ?? $formValues.email,
            expertLink: pageDefaultValues?.requestedExpertLink
        };
    })
</script>

<!-- svelte-ignore missing-declaration -->
<PageLayout class="RequestExpertPage flex flex-col h-full">
    <PageHeader 
        title={`${_isReferringSpecificExpert ? 'Request this Expert' : 'Request an Expert'}`}
        description="We’ll save you time and search or recruit for you. We respond in as little as 1 hour*"
        class="RequestExpertPage__header text-center"
    />

    <MainLayout>
        <form
            method="POST"
            use:enhance={onSubmit}           
            class={`RequestExpertPage__form flex flex-col gap-10 ${isSubmitting ? 'animate-pulse' : ''}`}
        >
            <!-- EXPERT CV LINK -->
            {#if $formValues.expertLink}
                <div class="RequestExpertPage__form__field-container text-left flex flex-col">
                    <label for="expertLink" class="dark:text-fr-white">Expert CV's Link:</label>
                
                        <Link
                            linkUrl={`${$formValues.expertLink}`}
                            color="primary"
                            id="expertLink"
                            class="lowercase pointer"
                            linkTarget="_blank"
                            style="text-transform: lowercase"
                        >
                            {$formValues.expertLink}
                        </Link>
                    
                    <!-- Required to be a hidden field as well to be send as part fo the form -->
                    <TextField 
                        id="expertLink" 
                        name="expertLink" 
                        value={$formValues.expertLink}
                        hidden
                    />
                </div>
            {/if}
            

            <!-- NAME  fields -->
            <div class="RequestExpertPage__form__row-container flex flex-col gap-5 lg:flex-row">
                <div class="RequestExpertPage__form__field-container text-left w-full lg:w-1/2">
                    <TextField 
                        id="firstName" 
                        name="firstName"
                        label="First Name"
                        value={$formValues.firstName}
                        maxlength=100
                        disabled={isSubmitting}
                        useCleanIcon
                        required
                        on:onKeyUp={onTextFieldPressKey}
                        on:onCleanField={onCleanField}
                    />
                    <SmallText class="mt-1 h-4 font-oswald">
                        {#if $formErrors.firstName}
                            <div class="text-fr-orange">{$formErrors.firstName}</div>
                        {/if}
                    </SmallText>
                </div>
                <div class="RequestExpertPage__form__field-container text-left w-full lg:w-1/2">
                    <TextField 
                        id="lastName" 
                        name="lastName"
                        label="Last Name"
                        value={$formValues.lastName}
                        maxlength=100
                        disabled={isSubmitting}
                        useCleanIcon
                        required
                        on:onKeyUp={onTextFieldPressKey}
                        on:onCleanField={onCleanField}
                    />
                    <SmallText class="mt-1 h-4 font-oswald ">
                        {#if $formErrors.lastName}
                            <div class="text-fr-orange">{$formErrors.lastName}</div>
                        {/if}
                    </SmallText>
                </div>
            </div>
         
            <!-- EMAIL  fields -->
            <div class="RequestExpertPage__form__row-container flex flex-col gap-5 lg:flex-row">
                <div class="RequestExpertPage__form__field-container text-left w-full lg:w-1/2">
                    <TextField 
                        id="email" 
                        name="email"
                        label="Email"
                        value={$formValues.email}
                        disabled={isSubmitting}
                        useCleanIcon
                        required
                        on:onKeyUp={onTextFieldPressKey}
                        on:onCleanField={onCleanField}
                    />
                    <SmallText class="mt-1 h-4 font-oswald">
                        {#if $formErrors.email}
                            <div class="text-fr-orange">{$formErrors.email}</div>
                        {/if}
                    </SmallText>
                    
                </div>
                <div class="RequestExpertPage__form__field-container text-left w-full lg:w-1/2">
                    <TextField 
                        id="confirmEmail" 
                        name="confirmEmail"
                        label="Confirm Email"
                        value={$formValues.confirmEmail}
                        disabled={isSubmitting}
                        useCleanIcon
                        required
                        on:onKeyUp={onTextFieldPressKey}
                        on:onCleanField={onCleanField}
                    />
                    <SmallText class="mt-1 h-4font-oswald">
                        {#if $formErrors.confirmEmail}
                            <div class="text-fr-orange">{$formErrors.confirmEmail}</div>
                        {/if}
                    </SmallText>
                </div>
            </div>

            <!-- PHONE -->
            <div class="RequestExpertPage__form__field-container text-left">
                <TextField 
                    id="phone" 
                    name="phone"
                    label="Phone"
                    value={$formValues.phone}
                    maxlength=20
                    disabled={isSubmitting}
                    useCleanIcon
                    required
                    on:onKeyUp={onTextFieldPressKey}
                    on:onCleanField={onCleanField}
                />
                <SmallText class="mt-1 h-4 font-oswald">
                    {#if $formErrors.phone}
                        <div class="text-fr-orange">{$formErrors.phone}</div>
                    {/if}
                </SmallText>
            </div>

            <!-- POSITION -->
            <div class="RequestExpertPage__form__field-container text-left">
                <Select
                    id="position"
                    name="position"
                    label="Position"
                    value={$formValues.position}
                    disabled={isSubmitting}
                    options={requesterPositions}
                    required
                    on:onChange={(event => onTextFieldPressKey(event))}
                    on:onCleanField={onCleanField}
                />
                <SmallText class="mt-1 h-4 font-oswald">
                    {#if $formErrors.position}
                        <div class="text-fr-orange">{$formErrors.position}</div>
                    {/if}
                </SmallText>
            </div>

            <!-- ADDRESS -->
            <div class="RequestExpertPage__form__field-container text-left">
                <label for="address" class="dark:text-fr-white">Address</label>
                <TextField 
                    id="address" 
                    name="address" 
                    value={$formValues.address}
                    maxlength=150
                    disabled={isSubmitting}
                    useCleanIcon
                    on:onKeyUp={onTextFieldPressKey}
                    on:onCleanField={onCleanField}
                />
                <SmallText class="mt-1 h-4 font-oswald">
                    {#if $formErrors.address}
                        <div class="text-fr-orange">{$formErrors.address}</div>
                    {/if}
                </SmallText>
            </div>

            <!-- ADDRESS complementary  fields -->
            <div class="RequestExpertPage__form__row-container flex flex-col gap-5 lg:flex-row">
                <div class="RequestExpertPage__form__field-container text-left w-full lg:w-1/3">
                    <label for="city" class="dark:text-fr-white">City</label>
                    <TextField 
                        id="city" 
                        name="city" 
                        value={$formValues.city}
                        maxlength=100
                        disabled={isSubmitting} 
                        useCleanIcon
                        on:onKeyUp={onTextFieldPressKey}
                        on:onCleanField={onCleanField}
                    />
                    <SmallText class="mt-1 h-4 font-oswald">
                        {#if $formErrors.city}
                            <div class="text-fr-orange">{$formErrors.city}</div>
                        {/if}
                    </SmallText>
                </div>
                <div class="RequestExpertPage__form__field-container text-left w-full lg:w-1/3">
                    <label for="state" class="dark:text-fr-white">State</label>
                    <TextField 
                        id="state" 
                        name="state" 
                        value={$formValues.state}
                        maxlength=100
                        disabled={isSubmitting} 
                        useCleanIcon
                        on:onKeyUp={onTextFieldPressKey}
                        on:onCleanField={onCleanField}
                    />
                    <SmallText class="mt-1 h-4 font-oswald">
                        {#if $formErrors.state}
                            <div class="text-fr-orange">{$formErrors.state}</div>
                        {/if}
                    </SmallText>
                </div>
                <div class="RequestExpertPage__form__field-container text-left w-full lg:w-1/3">
                    <label for="zipCode" class="dark:text-fr-white">Zip Code</label>
                    <TextField 
                        id="zipCode" 
                        name="zipCode" 
                        value={$formValues.zipCode}
                        maxlength=10
                        disabled={isSubmitting} 
                        useCleanIcon
                        on:onKeyUp={onTextFieldPressKey}
                        on:onCleanField={onCleanField}
                    />
                    <SmallText class="mt-1 h-4 font-oswald text-fr-orange">
                        {#if $formErrors.zipCode}
                            <div class="text-fr-orange">{$formErrors.zipCode}</div>
                        {/if}
                    </SmallText>
                </div>
            </div>

            <!-- COMPANY -->
            <div class="RequestExpertPage__form__field-container text-left">
                <label for="company" class="dark:text-fr-white">Company Name</label>
                <TextField 
                    id="company" 
                    name="company" 
                    value={$formValues.company}
                    maxlength=100
                    disabled={isSubmitting} 
                    useCleanIcon
                    on:onKeyUp={onTextFieldPressKey}
                    on:onCleanField={onCleanField}
                />
                <SmallText class="mt-1 h-4 font-oswald text-fr-orange">
                    {#if $formErrors.company}
                        <div class="text-fr-orange">{$formErrors.company}</div>
                    {/if}
                </SmallText>
            </div>

            <!-- TYPE OF CASE -->
            <div class="RequestExpertPage__form__field-container text-left">
                <Select
                    id="typeOfCase"
                    name="typeOfCase"
                    label="Type of Case"
                    value={$formValues.typeOfCase}
                    disabled={isSubmitting}
                    options={typesOfCase}
                    required
                    on:onChange={(event => onTextFieldPressKey(event))}
                    on:onCleanField={onCleanField}
                />
                <SmallText class="mt-1 h-4 font-oswald">
                    {#if $formErrors.typeOfCase}
                        <div class="text-fr-orange">{$formErrors.typeOfCase}</div>
                    {/if}
                </SmallText>
            </div>

            <!-- DESCRIPTION -->
            <div class="RequestExpertPage__form__field-container text-left">
                <label for="description" class=" flex items-baseline dark:text-fr-white">
                    Short Description of the Case
                    <SmallText class="ml-1">(*)</SmallText>
                </label>
                <div
                    class="TextArea__container flex items-center relative rounded-[0.3125rem] border-2 border-fr-charcoal dark:border-fr-white dark:bg-inherit dark:text-fr-white "
                >
                    <!-- TODO: Create a form-element component for text-are and replace this -->
                    <textarea
                        id="description"
                        name="description"
                        class="TextArea outline-none p-4 w-full dark:bg-fr-charcoal dark:text-fr-white resize-none"
                        rows=5
                        maxlength=2000
                        disabled={isSubmitting}
                        bind:value={$formValues.description}
                    ></textarea>
                </div>
                <SmallText class="mt-1 h-4 font-oswald block">
					{#if $formErrors.description}
						<div class="text-fr-orange float-left">{$formErrors.description}</div>
					{/if}
					<div class="text-fr-charcoal dark:text-fr-white font-thin text-end float-right">
						{$formValues.description?.length ?? 0}/2000.
					</div>
				</SmallText>
            </div>

            <!-- CASE CAPTION -->
            <div class="RequestExpertPage__form__field-container text-left">
                <TextField 
                    id="caseCaption" 
                    name="caseCaption"
                    label="Case Caption"
                    value={$formValues.caseCaption}
                    maxlength=100
                    disabled={isSubmitting}
                    useCleanIcon
                    on:onKeyUp={onTextFieldPressKey}
                    on:onCleanField={onCleanField}
                />
                <SmallText class="mt-1 h-4 font-oswald">
                    {#if $formErrors.caseCaption}
                        <div class="text-fr-orange">{$formErrors.caseCaption}</div>
                    {/if}
                </SmallText>
            </div>

            <!-- TIME FRAME -->
            <div class="RequestExpertPage__form__field-container text-left">
                <label for="timeFrame" class="dark:text-fr-white">Time Frame</label>
                <SmallText class="font-oswald">Are there any time constraints?</SmallText>
                <TextField 
                    id="timeFrame" 
                    name="timeFrame" 
                    value={$formValues.timeFrame}
                    maxlength=100
                    disabled={isSubmitting} 
                    useCleanIcon
                    on:onKeyUp={onTextFieldPressKey}
                    on:onCleanField={onCleanField}
                />
                <SmallText class="mt-1 h-4 font-oswald">
                    {#if $formErrors.timeFrame}
                        <div class="text-fr-orange">{$formErrors.timeFrame}</div>
                    {/if}
                </SmallText>
            </div>
            <Button class="RequestExpertPage__form__submit-cta" type="submit" disabled={isSubmitting === true}>Submit Form</Button>
            <Button
                color="Link"
                class="RequestExpertPage__form__clear-cta mx-0 capitalize max-w-max self-center px-1 lg:w-auto"
                disabled={!formHasValues() || isSubmitting === true}
                on:click={() => {
                    clearForm();
                }}
            >
                Clear Form
            </Button>
        </form>
        
    </MainLayout>
</PageLayout>


<Modal
	isOpened={toggleModalOpened}
	title={`Form Submission ${$formStatus.success ? 'Successful!' : 'Failed'}`}
	on:onClose={closeModal}
	useClose={$formStatus.success ? false : true}
>
	<div class="BecomeExpertPage__notificationModal__content flex flex-col w-full justify-center" slot="content">
        <div class="flex min-h-[76px] items-center" transition:fade={{ duration: 500 }}>
            {#if $formStatus.success === true}
                <div class="h-[50px] mr-3 text-fr-royal-blue">
                    <IoMdCheckmarkCircleOutline />
                </div>
                <HTMLRenderer
                    content={'<div class="flex flex-col"><p>Your request was sent correctly.</p><p class="animate-pulse"> Redirecting...</p></div>'}
                    class="max-w-[400px] mt-2 dark:text-fr-white mb-4"
                />
            {:else if $formStatus.success === false}
                <div class="h-[50px] mr-3 text-fr-orange">
                    <IoMdCloseCircleOutline />
                </div>
                <HTMLRenderer
                    content={$formStatus.message}
                    class="max-w-[400px] mt-2 dark:text-fr-white mb-4"
                />
            {/if}
        </div>
	</div>
</Modal>


<svelte:window bind:scrollY={scrollY} />