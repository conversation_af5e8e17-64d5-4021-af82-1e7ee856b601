import type { TRequestExpertFormErrors, TRequestExpertFormStatus, TRequestExpertFormValues, TRequesterPosition, TTypesOfCase } from "./_types-interfaces";

export const requesterPositions: TRequesterPosition[] = [
    { title: 'Attorney', slug: 'Attorney' },
    { title: 'Paralegal', slug: 'Paralegal' },
    { title: 'Legal Clerk', slug: 'Legal Clerk' },
    { title: 'Insurance Co. Rep', slug: 'Insurance Co. Rep' },
    { title: 'Client', slug: 'Client' },
    { title: 'Pro Per/ Pro Se', slug: 'Pro Per/ Pro Se' }
];

export const typesOfCase: TTypesOfCase[] = [
    { title: 'Plaintiff', slug: 'Plaintiff' },
    { title: 'Defense', slug: 'Defense' },
    { title: 'Expert Consulting', slug: 'Expert Consulting' },
];

export const requestExpertFormInitValues: TRequestExpertFormValues = {
    firstName: null,
    lastName: null,
    email: null,
    confirmEmail: null,
    phone: null,
    position: requesterPositions[0].slug, //Attorney
    description: null,
    typeOfCase: null, //Plaintiff
    address: null,
    city: null,
    state: null,
    zipCode: null,
    caseCaption: null,
    company: null,
    timeFrame: null,
    expertLink: null
}

export const requestExpertFormInitErrors: TRequestExpertFormErrors = {
    form: null,
    firstName: null,
    lastName: null,
    email: null,
    confirmEmail: null,
    phone: null,
    position: null,
    description: null,
    typeOfCase: null,
    address: null,
    city: null,
    state: null,
    zipCode: null,
    caseCaption: null,
    company: null,
    timeFrame: null,
    expertLink: null
}

export const requestExpertFormInitStatus: TRequestExpertFormStatus = {
    success: null,
}; 