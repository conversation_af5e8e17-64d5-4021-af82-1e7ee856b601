<script lang="ts">
	import type { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
	import { onMount } from "svelte";
	import ExpertSummaryCard from "../../cards/ExpertSummaryCard.svelte";
	import PageHeader from "../../headers/PageHeader.svelte";
	import MainLayout from "../../layouts/MainLayout.svelte";
	import PageLayout from "../../layouts/PageLayout.svelte";
	import SectionLayout from "../../layouts/SectionLayout.svelte";
	import type { TExpertSearchFilterValue, TOnExpertSearchBoxEventDispatcher, TPreloadedData } from "../../search/_types-interfaces";
	import ExpertSearchBox from "../../search/ExpertSearchBox.svelte";
	import TruncatedParagraph from "../../wrappers/TruncatedParagraph.svelte";
	import { ContentServiceClient } from "$lib/services/content.service";
    import type { TContentServiceClient } from "$lib/services/content.service";
	import { createWritableStore } from "$lib/ui/state-management/store/store";

    const contentService: TContentServiceClient = ContentServiceClient();

    export let criteriaFilterValue: string |  null = null;
    export let categoryFilterValue: TExpertSearchFilterValue = [];
    export let countryFilterValue: string |  null = null;
    export let regionFilterValue: TExpertSearchFilterValue = [];
    export let stateFilterValue: TExpertSearchFilterValue = [];
    export let exactMatchFilterValue: boolean = false;
    export let page: TExtendedPageDto;
    export let keywordContent: string | null = null;
	export let searchPreloadedData:  TPreloadedData | null = null;
    let showKeywordContent: boolean = false;

    $: ({description} = page);
    const _keywordRef = createWritableStore<string | null>(criteriaFilterValue);

    const _onSearch = async(event: CustomEvent<TOnExpertSearchBoxEventDispatcher['onSearch']>) => {
        let criteriaAsKeyword = null;
        let categoryAsKeyword = null;

        const filtersSelected = event.detail.searchValues;
        for (const filter of filtersSelected) {
            const {id, value} = filter;
            if(id === 'criteria') criteriaAsKeyword = value;
            if(id.includes('category') && !categoryAsKeyword) categoryAsKeyword = value; // take only the first category if exist.
        }
        const newKeywordRef = criteriaAsKeyword || categoryAsKeyword;
        if(!newKeywordRef) {
            _keywordRef.set(null);
            keywordContent = null;
            return;
        }
        
        if(newKeywordRef !== $_keywordRef) {
            keywordContent = await contentService.getSearchPageUnifiedContent(criteriaAsKeyword, categoryAsKeyword);
            _keywordRef.set(newKeywordRef);
        }
	};

    onMount(() => {
      setTimeout(() => {
        showKeywordContent = true;
      }, 500);
    });
</script>

<PageLayout class="SearchPage flex flex-col h-full">
    <PageHeader
        class="mb-2"
        titleClass={'text-center font-normal'}
        title="Search Over 30,000 Expert Witness Specialties & View Their CV's"
        {description}
    />

    <MainLayout>
        {#if keywordContent && showKeywordContent}
            <TruncatedParagraph lines={3} class="border-l-4 border-l-fr-orange px-2">
                <strong><u>{$_keywordRef}</u></strong>: {keywordContent}
            </TruncatedParagraph>
        {/if}
        <!-- SEARCHING FEATURES SECTION -->
        <ExpertSearchBox 
            criteria={criteriaFilterValue}
            useCriteriaFixed
            useCriteriaFilterReference
            criteriaCustomPlaceholder="What type of expert are you looking for?"
            useCategory
            category={categoryFilterValue}
            useCategoryFilterReference
            useCountry
            country={countryFilterValue}
            useCountryFilterReference
            useRegion
            region={regionFilterValue}
            useRegionFilterReference
            useState
            state={stateFilterValue}
            useStateFilterReference
            useExpertNumber
            useExpertNumberFilterReference
            useClearAll
            shouldUpdateUrlSearchParams
            useRequestExpertLink
            useExactMatch
            exactMatch={exactMatchFilterValue}
            on:onSearch={_onSearch}
            preloadedData={searchPreloadedData}
        >
            <!-- SEARCHING RESULTS SECTION (components composition)-->
            <SectionLayout class="SearchPage__resultsSection flex flex-col" slot="searchResults" let:experts={experts}>
                {#each experts as {permalink, permalinkSlugSegment, ...rest}}
                    <ExpertSummaryCard  
                        expert={{
                            detailsLink: permalink, 
                            slug: permalinkSlugSegment,
                            ...rest
                        }} 
                    />
                {/each}
            </SectionLayout>
        </ExpertSearchBox>
    </MainLayout>
</PageLayout>