<script lang="ts">
	import type { TCompetitiveEdgeDto } from '$lib/shared/api-dtos/internal-rest/competitive-edge-dtos';
	import { theme } from '$lib/ui/state-management/store/theme';
	import CompetitiveEdgeCard from '../../cards/CompetitiveEdgeCard.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H2 from '../../system-design/headings/H2.svelte';

	export let competitiveEdges: TCompetitiveEdgeDto[];
</script>

<SectionLayout
	class=" relative flex flex-col items-center min-h-[70rem] md:p-8 my-8 dark:border-t-2 dark:border-b-2"
>
	{#if $theme}
		<img
			class="absolute right-0 h-auto hidden md:block"
			src="/images/competitive-edge-dark.svg"
			alt="How Different Banner"
			loading="eager"
		/>
	{:else}
		<img
			class="absolute right-0 h-auto hidden md:block"
			src="/images/competitive-edge-light.svg"
			alt="How Different Banner"
			loading="eager"
		/>
	{/if}
	<H2 class="text-center px-16 pt-6 md:p-0">How We’re Different</H2>
	<div class="flex flex-col w-full py-16 px-8 gap-12 max-w-7xl">
		{#each competitiveEdges as competitiveEdgeInfo, index}
			<CompetitiveEdgeCard
				class={`${index % 2 ? 'md:self-end' : 'md:self-start'} self-center`}
				title={competitiveEdgeInfo.title}
				description={competitiveEdgeInfo.description}
				image={competitiveEdgeInfo.image}
			/>
		{/each}
	</div>
</SectionLayout>
