<script lang="ts">
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import Paragraph from './../../system-design/texts/Paragraph.svelte';
	import Counter from '../../counter/Counter.svelte';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import type { TFindResults } from '../../../../../routes/+page.server';

	export let section: TFindResults;
</script>

<SectionLayout class="FindResultSection py-5 px-4 gap-4 mt-8 text-center">
	<div class="max-w-7xl mx-auto">
		<H3 class="text-[2rem] leading-[2.5rem] dark:text-fr-white capitalize">{section?.title}</H3>
		<HTMLRenderer
			content={section?.description}
			class="dark:text-fr-white max-w-2xl mx-auto mb-8"
		/>

		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-14">
			<div>
				<div class="dark:text-fr-white">
					<Counter targetNumber={section?.caseSupported.caseCount} duration={1} step={100} />
				</div>
				<Paragraph class="max-w-[240px] leading-normal mx-auto"
					>{section?.caseSupported.caseCopy}</Paragraph
				>
			</div>

			<div>
				<div class="flex justify-center">
					<!-- {#if section?.searchOnlineImage?.sourceUrl}
						<img
							class="h-[5rem] lg:h-[5.5rem]"
							src={section?.searchOnlineImage?.sourceUrl}
							alt={section?.searchOnlineImage.title}
						/>
					{/if} -->

					<div class="h-[5rem] lg:h-[5.5rem] dark:text-fr-white">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							xmlns:xlink="http://www.w3.org/1999/xlink"
							version="1.1"
							width="256"
							height="256"
							viewBox="0 0 256 256"
							xml:space="preserve"
							class="w-full h-full"
						>
							<defs />
							<g
								style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;"
								transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)"
							>
								<path
									d="M 82.302 75.38 H 7.698 C 3.454 75.38 0 71.927 0 67.682 v -3.233 c 0 -0.553 0.448 -1 1 -1 h 32.003 c 0.552 0 1 0.447 1 1 v 2.857 h 21.994 v -2.857 c 0 -0.553 0.447 -1 1 -1 H 89 c 0.553 0 1 0.447 1 1 v 3.233 C 90 71.927 86.547 75.38 82.302 75.38 z M 2 65.448 v 2.233 c 0 3.142 2.556 5.698 5.698 5.698 h 74.604 c 3.142 0 5.698 -2.557 5.698 -5.698 v -2.233 H 57.997 v 2.857 c 0 0.553 -0.447 1 -1 1 H 33.003 c -0.552 0 -1 -0.447 -1 -1 v -2.857 H 2 z"
									style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: currentColor; fill-rule: nonzero; opacity: 1;"
									transform=" matrix(1 0 0 1 0 0) "
									stroke-linecap="round"
								/>
								<path
									d="M 83.914 65.448 c -0.553 0 -1 -0.447 -1 -1 V 17.907 c 0 -0.71 -0.577 -1.288 -1.287 -1.288 H 8.374 c -0.71 0 -1.288 0.578 -1.288 1.288 v 46.541 c 0 0.553 -0.448 1 -1 1 s -1 -0.447 -1 -1 V 17.907 c 0 -1.813 1.475 -3.288 3.288 -3.288 h 73.253 c 1.813 0 3.287 1.475 3.287 3.288 v 46.541 C 84.914 65.001 84.467 65.448 83.914 65.448 z"
									style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: currentColor; fill-rule: nonzero; opacity: 1;"
									transform=" matrix(1 0 0 1 0 0) "
									stroke-linecap="round"
								/>
								<path
									d="M 78.5 60.448 h -67 c -0.552 0 -1 -0.447 -1 -1 v -36.31 c 0 -1.712 1.393 -3.104 3.104 -3.104 h 62.791 c 1.712 0 3.104 1.393 3.104 3.104 v 36.31 C 79.5 60.001 79.053 60.448 78.5 60.448 z M 12.5 58.448 h 65 v -35.31 c 0 -0.609 -0.495 -1.104 -1.104 -1.104 H 13.604 c -0.609 0 -1.104 0.496 -1.104 1.104 V 58.448 z"
									style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: currentColor; fill-rule: nonzero; opacity: 1;"
									transform=" matrix(1 0 0 1 0 0) "
									stroke-linecap="round"
								/>
								<path
									d="M 42.111 47.034 c -2.717 0 -5.435 -1.034 -7.503 -3.103 c -2.004 -2.004 -3.108 -4.669 -3.108 -7.503 c 0 -2.834 1.104 -5.499 3.108 -7.503 c 4.137 -4.138 10.871 -4.137 15.006 0 c 4.137 4.137 4.137 10.869 0 15.006 C 47.546 46 44.829 47.034 42.111 47.034 z M 42.111 27.821 c -2.205 0 -4.41 0.839 -6.089 2.518 c -1.626 1.626 -2.522 3.789 -2.522 6.089 c 0 2.3 0.896 4.462 2.522 6.089 c 3.357 3.357 8.821 3.357 12.178 0 c 3.357 -3.357 3.357 -8.82 0 -12.178 C 46.521 28.661 44.316 27.821 42.111 27.821 z"
									style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: currentColor; fill-rule: nonzero; opacity: 1;"
									transform=" matrix(1 0 0 1 0 0) "
									stroke-linecap="round"
								/>
								<path
									d="M 57.5 52.817 c -0.256 0 -0.512 -0.098 -0.707 -0.293 L 48.2 43.931 c -0.391 -0.391 -0.391 -1.023 0 -1.414 s 1.023 -0.391 1.414 0 l 8.593 8.593 c 0.391 0.391 0.391 1.023 0 1.414 C 58.012 52.72 57.756 52.817 57.5 52.817 z"
									style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: currentColor; fill-rule: nonzero; opacity: 1;"
									transform=" matrix(1 0 0 1 0 0) "
									stroke-linecap="round"
								/>
								<path
									d="M 38.428 41.111 c -0.256 0 -0.512 -0.098 -0.707 -0.293 c -1.173 -1.172 -1.818 -2.731 -1.818 -4.39 s 0.646 -3.217 1.818 -4.39 c 0.391 -0.391 1.023 -0.391 1.414 0 s 0.391 1.023 0 1.414 c -0.795 0.795 -1.232 1.852 -1.232 2.976 s 0.438 2.181 1.232 2.976 c 0.391 0.39 0.391 1.023 0 1.414 C 38.94 41.013 38.684 41.111 38.428 41.111 z"
									style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: currentColor; fill-rule: nonzero; opacity: 1;"
									transform=" matrix(1 0 0 1 0 0) "
									stroke-linecap="round"
								/>
							</g>
						</svg>
					</div>
				</div>
				<Paragraph class="max-w-[240px] leading-normal mx-auto"
					>{section?.searchOnlineText}</Paragraph
				>
			</div>

			<div>
				<div class="dark:text-fr-white">
					<Counter targetNumber={section?.qualifiedExperts.expertsCount} duration={1} step={100} />
				</div>
				<Paragraph class="max-w-[240px] leading-normal mx-auto"
					>{section?.qualifiedExperts.expertsCopy}</Paragraph
				>
			</div>
		</div>
	</div>
</SectionLayout>
