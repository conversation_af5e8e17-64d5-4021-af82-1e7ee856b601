<script lang="ts">
	import type { TFeatureStrip } from '../../../../../routes/+page.server';
	import Button from '../../form-elements/Button.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';

	export let section: TFeatureStrip;
</script>

<SectionLayout
	class="FeatureSection grid grid-rows-1 md:grid-cols-[3fr__2fr] w-full justify-items-center gap-4 dark:border-t-2 dark:border-b-2 dark:border-b-zinc-700"
>
	<div class="flex flex-col h-auto justify-center text-center gap-4 w-full lg:w-[25rem] px-4">
		<H3 class="text-[2rem] leading-[2.5rem] capitalize">{section?.title}</H3>
		<HTMLRenderer content={section?.description} class="dark:text-fr-white mb-4" />
		{#if section?.cta?.title}
			<a href={section?.cta.url}>
				<Button class="md:w-[9rem] mx-auto">{section?.cta?.title}</Button>
			</a>
		{/if}
	</div>
	
	{#if section.media.mediaType === 'image' && section?.media?.sourceUrl}
		<img
			class="w-full h-[20rem] lg:h-[25rem] object-cover"
			src={section.media.sourceUrl}
			alt={section.media.title}
		/>
	{/if}
</SectionLayout>
