<script lang="ts">
	import { PUBLIC_SITE_URL } from '$env/static/public';
	import type { TCategoriesSection, THomepageSettingsDto } from '../../../../../routes/+page.server';
	import CategoryCard from '../../cards/CategoryCard.svelte';
	import Button from '../../form-elements/Button.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import type { THomepageCategoryDto } from './_types-interfaces';

	export let section: TCategoriesSection;
	export let categories: THomepageCategoryDto[] = [];
</script>

<SectionLayout class="CategoriesSection grid grid-rows-[repeat(2,auto)] py-5 px-4 gap-4 mt-8">
	<div class="flex flex-row w-full h-fit justify-between gap-4 items-center">
		{#if section?.title}
			<H3 class="font-[1.625rem]">{section?.title}</H3>
		{/if}
		{#if section?.cta?.title}
			<a href={section?.cta.url}>
				<Button class="sm:w-[16rem]">{section?.cta?.title}</Button>
			</a>
		{/if}
	</div>
	<div class="grid md:grid-cols-4 justify-items-center gap-4">
		{#if categories.length}
			{#each categories as category}
				<!--@TODO: Refactor this.-->
				<CategoryCard
					category={{ ...category, link: `${PUBLIC_SITE_URL}categories/${category.slug}` }}
				/>
			{/each}
		{/if}
	</div>
</SectionLayout>
