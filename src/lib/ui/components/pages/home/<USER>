<script lang="ts">
	import type { TCompetitiveEdgeDto } from '$lib/shared/api-dtos/internal-rest/competitive-edge-dtos';

	import CategoriesSections from './CategoriesSections.svelte';
	import FeatureStrip from './FeatureStrip.svelte';
	import HeroBanner from './HeroBanner.svelte';
	// import CompetitiveEdgeSection from './CompetitiveEdgeSection.svelte';
	// import StepSection from './StepSection.svelte';
	import LogoSlider from './LogoSlider.svelte';
	import FindResult from './FindResult.svelte';
	import WinningSecret from './WinningSecret.svelte';
	import FindWitnessFree from './FindWitnessFree.svelte';
	import HowReferralsWork from './HowReferralsWork.svelte';
	import Testimonials from './Testimonials.svelte';
	import RequestExpertForm from '../generic/RequestExpertForm.svelte';
	import type {
		PageSection,
		TBanner,
		TFindResults,
		TCategoriesSection,
		TLogoSlider,
		TWinningSecret,
		TFindWitnessFree,
		TFeatureStrip,
		THowReferralsWork,
		TRequestExpertForm,
		TTestimonialDtos,
	} from '../../../../../routes/+page.server';
	import type { THomepageSettingsDto } from '$lib/shared/api-dtos/internal-rest/settings-dtos';

	type TSection = {
		competitiveEdges: TCompetitiveEdgeDto[];
	};

	export let pageSections: PageSection[] = [];
	export let homepageSettings: THomepageSettingsDto = {testimonials: [], categories: []};
	$: ({ testimonials, categories } = homepageSettings);

	function isTBanner(section: PageSection): section is TBanner {
		return (section as TBanner).__typename.includes('Components_Banner');
	}
	function isLogoSlider(section: PageSection): section is TLogoSlider {
		return (section as TLogoSlider).__typename.includes('Components_LogoSlider');
	}
	function isFindResults(section: PageSection): section is TFindResults {
		return (section as TFindResults).__typename.includes('Components_FindResult');
	}
	function canShowCategorySection(section: PageSection) {
		const categorySection = section as TCategoriesSection;
		// @TODO: Refactor this.
		return categorySection.__typename.includes('Components_Categories') &&
				categorySection.title && categorySection.cta?.title && categorySection.cta?.url &&
				Array.isArray(categories) && categories.length > 0;
	}
	function isTestimonials(section: PageSection): section is TTestimonialDtos {
		return (section as TTestimonialDtos).__typename.includes('Components_Testimonial');
	}
	function isWinningSecret(section: PageSection): section is TWinningSecret {
		return (section as TWinningSecret).__typename.includes('Components_WinningSecret');
	}
	function isFindWitnessFree(section: PageSection): section is TFindWitnessFree {
		return (section as TFindWitnessFree).__typename.includes('Components_FindWitnessFree');
	}
	function isHowReferralsWork(section: PageSection): section is THowReferralsWork {
		return (section as TFindWitnessFree).__typename.includes('Components_HowReferralsWork');
	}
	function isFeatureStrip(section: PageSection): section is TFeatureStrip {
		return (section as TFeatureStrip).__typename.includes('Components_FeatureStrip');
	}
	function isRequestExpertForm(section: PageSection): section is TRequestExpertForm {
		return (section as TRequestExpertForm).__typename.includes('Components_RequestExpertForm');
	}
</script>

{#each pageSections as section}
	{#if isTBanner(section)}
		<HeroBanner {section} />
	{/if}
	{#if isLogoSlider(section)}
		<LogoSlider {section} />
	{/if}
	{#if isFindResults(section)}
		<FindResult {section} />
	{/if}
	{#if canShowCategorySection(section)}
		<CategoriesSections {section} {categories} />
	{/if}
	{#if isTestimonials(section)}
		<Testimonials {section} {testimonials} />
	{/if}
	{#if isWinningSecret(section)}
		<WinningSecret {section} />
	{/if}
	{#if isFindWitnessFree(section)}
		<FindWitnessFree {section} />
	{/if}
	{#if isHowReferralsWork(section)}
		<HowReferralsWork {section} />
	{/if}
	{#if isFeatureStrip(section)}
		<FeatureStrip {section} />
	{/if}
	{#if isRequestExpertForm(section)}
		<RequestExpertForm />
	{/if}
{/each}

<!-- <StepSection /> -->
<!-- <CompetitiveEdgeSection competitiveEdges={sections.competitiveEdges} /> -->
