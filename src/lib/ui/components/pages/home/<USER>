<script lang="ts">
	import { goto } from '$app/navigation';

	import H<PERSON><PERSON>enderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import TextField from '../../form-elements/inputs/TextField/index.svelte';
	import type { TFieldTextOnKeyUpEventDispatcher } from '../../form-elements/inputs/TextField/_types-interfaces';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H1 from '../../system-design/headings/H1.svelte';
	import H2 from '../../system-design/headings/H2.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import Paragraph from '../../system-design/texts/Paragraph.svelte';
	import SmallText from '../../system-design/texts/SmallText.svelte';
	import Link from '../../form-elements/Link.svelte';
	import ExpertSearchBox from '../../search/ExpertSearchBox.svelte';
	import type { TOnExpertSearchBoxEventDispatcher } from '../../search/_types-interfaces';
	import type { TBanner } from '../../../../../routes/+page.server';
	import ExpertSimpleSearchBox from '../../search/ExpertSimpleSearchBox.svelte';
	import { tick } from 'svelte';

	interface IHeroFormData {
		firstName: string | null;
		lastName: string | null;
		email: string | null;
	}

	export let section: TBanner;

	const bgImage: string = '/images/home-page-image.png';
	let searchingCriteria: string | null = null;
	let heroFormData: IHeroFormData = {
		firstName: null,
		lastName: null,
		email: null
	};
	let urlSegments = '';
	$: {
		let segments = '';
		let formField: keyof typeof heroFormData;
		for (formField in heroFormData) {
			if (heroFormData[formField])
				segments += `${segments.length > 0 ? '&' : ''}${formField}=${heroFormData[formField]}`;
		}

		urlSegments = segments.length > 0 ? `?${segments}` : '';
	}

	const _onFormFieldPressKey = async(event: CustomEvent<TFieldTextOnKeyUpEventDispatcher>) => {
		const { value, fieldId, keyCode } = event.detail;
		const key = fieldId as keyof IHeroFormData;
		heroFormData[key] = value;
		if(keyCode && keyCode.toLowerCase() === 'enter') {
			await tick();
			goto(`/request-expert-witness${urlSegments}`, { replaceState: false})
		}
	};
</script>

<SectionLayout
	style="background-image: url('{section?.backgroundImage?.sourceUrl ?? bgImage}');"
	class={`HeroBannerSection grid grid-rows-2 grid-cols-1 lg:grid-rows-1 lg:grid-cols-3 min-h-[33.5rem] bg-cover dark:border-t-2 dark:border-b-2 dark:border-zinc-700`}
>
	<div
		class="bg-[#1E1E1E91] w-full col-span-2 flex flex-col content-center justify-center p-10 lg:p-24 border-b-2 lg:border-r-2 lg:border-b-0
	"
	>
		<H1 class="text-fr-white !text-[1.25rem] !leading-[1.75rem] m-0 capitalize"
			>{section?.subtitle}</H1
		>
		<H3 class="text-fr-white text-[2rem] leading-[2.5rem] capitalize">{section?.title}</H3>

		<HTMLRenderer
			content={section?.description}
			class="HeroBannerSection__contentRenderer__description text-fr-white"
		/>

		<div class="HeroBanner__searchSectionForm flex flex-col w-full max-w-xl self-start">
			<ExpertSimpleSearchBox
				useCustomSearchAction
				customPlaceholder="What type of Expert Witness are you looking for?"
			/>
		</div>
	</div>
	<div class="bg-[rgba(53,83,239,0.16)] flex flex-col w-full content-center justify-center p-10">
		<div class="flex flex-col gap-4 my-4">
			<H2 class="text-fr-white capitalize">Request An Expert</H2>
			<Paragraph class="text-fr-white"
				>Save yourself time and we will search for you. We respond in as fast as within 1 hour.* No
				cost or obligation until retention.</Paragraph
			>
		</div>
		<div class="flex flex-col w-full gap-4">
			<div class="text-left">
				<label for="firstName" class="text-fr-white">First Name</label>
				<TextField
					id="firstName"
					name="firstName"
					class="border-[2px]"
					value={heroFormData.firstName}
					requireValueOnEnter={false}
					on:onKeyUp={_onFormFieldPressKey}
				/>
			</div>
			<div class="text-left">
				<label for="lastName" class="text-fr-white">Last Name</label>
				<TextField
					id="lastName"
					name="lastName"
					class="border-[2px]"
					value={heroFormData.lastName}
					requireValueOnEnter={false}
					on:onKeyUp={_onFormFieldPressKey}
				/>
			</div>
			<div class="text-left">
				<label for="email" class="text-fr-white">Email</label>
				<TextField
					id="email"
					name="email"
					class="border-[2px]"
					value={heroFormData.email}
					requireValueOnEnter={false}
					on:onKeyUp={_onFormFieldPressKey}
				/>
			</div>
			<div class="flex flex-col gap-4 mt-6">
				<Link
					linkUrl={`/request-expert-witness${urlSegments}`}
					color="primary"
					uiType="button"
					class="ExpertSummaryCard__footer__viewCvCta capitalize mb-3 w-full"
				>
					Request An Expert
				</Link>
				<SmallText class="text-fr-white"
					>*We respond as fast as within 1 hour M-F 8-5pm PT or, the next business day outside these
					hours.</SmallText
				>
			</div>
		</div>
	</div>
</SectionLayout>

<style>
	:global(.HeroBannerSection__contentRenderer__description ul) {
		@apply list-disc list-inside text-fr-white pl-6 my-4;
	}
</style>
