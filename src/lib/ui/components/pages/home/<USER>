<script lang="ts">
	import type { TFindWitnessFree } from './../../../../../routes/+page.server.ts';

	import SectionLayout from '$lib/ui/components/layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import H4 from '../../system-design/headings/H4.svelte';
	import Paragraph from '../../system-design/texts/Paragraph.svelte';

	export let section: TFindWitnessFree;
</script>

<SectionLayout
	class="FindFreeSection grid grid-row-2 grid-cols-1 gap-4 justify-center mt-[2.8rem] lg:mb-[2.8rem] border-b-gray-200 border-b-2 dark:border-b-zinc-700"
>
	<div class="bg-gray-200 px-4 py-10 lg:py-14 text-center dark:bg-zinc-800">
		<H3 class="text-[2rem] leading-[2.5rem] dark:text-fr-white capitalize font-normal"
			>{section.title}</H3
		>
	</div>
	<div
		class="grid grid-row-1 grid-cols-1 lg:grid-cols-3 gap-6 justify-center p-4 pb-10 max-w-5xl mx-auto lg:gap-x-10"
	>
		{#each section.findSteps as step}
			<div class="grid gap-4 items-baseline grid-cols-[max-content__1fr] lg:gap-5">
				{#if step.icon?.sourceUrl}
					<div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center p-2.5">
						<img src={step.icon.sourceUrl} alt={step.icon.title} />
					</div>
				{/if}

				<div class="">
					<H4 class="text-xl capitalize mt-0 dark:text-fr-white">{step.title}</H4>
					<Paragraph class="dark:text-fr-white leading-normal">
						{step?.description}
					</Paragraph>
				</div>
			</div>
		{/each}
	</div>
</SectionLayout>
