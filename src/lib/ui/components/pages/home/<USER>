<script lang="ts">
	import { PUBLIC_SITE_URL } from '$env/static/public';
	import StepCard from '$lib/ui/components/cards/StepCard.svelte';

	import SectionLayout from '$lib/ui/components/layouts/SectionLayout.svelte';
	import H2 from '../../system-design/headings/H2.svelte';
</script>

<SectionLayout
	class="StepSection grid grid-row-2 grid-cols-1 gap-4 justify-center text-center my-[2.8rem]"
>
	<H2>How it works</H2>
	<div class="grid grid-row-1 md:grid-cols-3 gap-4 justify-center p-4">
		<StepCard
			title="Step 1: Find an Expert"
			description="Search from our expansive database of thousands of experts across over 30,000 expert categories."
			image="/images/step-image.png"
			url={`${PUBLIC_SITE_URL}search`}
		/>
		<StepCard
			title="Step 2: Contact Us"
			description="Let us know who you’ve found, or contact us and we’ll locate the proper expert for your case."
			image="/images/step-image.png"
			url={`${PUBLIC_SITE_URL}request-expert-witness`}
		/>
		<StepCard
			title="Step 3: Build Your Case"
			description="Work directly with your new expert witness or team of experts to build your case."
			image="/images/step-image.png"
		/>
	</div>
</SectionLayout>
