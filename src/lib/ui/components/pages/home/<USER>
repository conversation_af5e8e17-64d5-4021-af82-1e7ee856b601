<script lang="ts">
	import Swiper from 'swiper';
	import { onMount } from 'svelte';
	import { Navigation, Autoplay } from 'swiper/modules';

	import HtmlRenderer from '$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte';
	import type { TTestimonialDto } from './../../../../shared/api-dtos/internal-rest/settings-dtos.ts';
	import type { TTestimonialDtos } from './../../../../../routes/+page.server.ts';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';

	import 'swiper/css';
	import 'swiper/css/navigation';

	export let section: TTestimonialDtos;
	export let testimonials: Array<TTestimonialDto> = [];

	onMount(() => {
		const swiper = new Swiper('.testimonial-swiper', {
			loop: true,
			autoplay: {
				delay: 5000,
				disableOnInteraction: false
			},
			modules: [Navigation, Autoplay],
			navigation: {
				nextEl: '.swiper-button-next',
				prevEl: '.swiper-button-prev'
			},
			slidesPerView: 1,
			spaceBetween: 20
		});
	});
</script>

<SectionLayout
	class="TestimonialSection w-full pt-10 pb-6 lg:pb-10 gap-4 text-center dark:text-fr-white bg-gray-100 dark:bg-zinc-800"
>
	<H3 class="text-[2rem] leading-[2.5rem] capitalize mb-8 lg:mb-10">
		{section?.title}
	</H3>

	<div class="swiper testimonial-swiper max-w-[860px]">
		<div class="swiper-button-prev !text-fr-black dark:!text-fr-white">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="size-6"
			>
				<path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
			</svg>
		</div>
		<div class="swiper-button-next !text-fr-black dark:!text-fr-white">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="size-6"
			>
				<path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
			</svg>
		</div>

		<div class="swiper-wrapper">
			{#each testimonials as testimonial}
				<div class="swiper-slide px-14 lg:px-20">
					<HtmlRenderer
						class="dark:text-fr-white text-lg leading-normal"
						content={testimonial.review}
					/>

					<div class="flex gap-6 items-center justify-center mt-6 lg:mt-8 px-4">
						<div class="w-10">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="1.5"
								stroke="currentColor"
								class="size-6"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
								/>
							</svg>
						</div>

						<div class="flex flex-col items-start">
							<p><strong>{testimonial.clientName}</strong></p>
							<p><i>{testimonial.clientPosition}</i></p>
						</div>
					</div>
				</div>
			{/each}
		</div>
	</div>
</SectionLayout>

<style>
	.swiper-button-next,
	.swiper-button-prev {
		@apply z-[20] -translate-y-10;
	}

	.swiper-button-next::after,
	.swiper-button-prev::after {
		@apply font-extrabold;
	}
</style>
