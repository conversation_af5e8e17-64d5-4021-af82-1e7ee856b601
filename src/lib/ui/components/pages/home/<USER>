<script lang="ts">
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import SectionLayout from '$lib/ui/components/layouts/SectionLayout.svelte';

	import type { THowReferralsWork } from './../../../../../routes/+page.server.ts';

	export let section: THowReferralsWork;
</script>

<SectionLayout
	class="HowWorkSection grid grid-row-2 grid-cols-1 gap-4 justify-center lg:mt-[2.8rem] border-b-gray-200 border-b dark:border-b-zinc-700"
>
	<HTMLRenderer
		content={section.title}
		class="HowWorkSection__title dark:text-fr-white hidden lg:block"
	/>

	<div class="grid grid-cols-1 lg:grid-cols-2 items-start">
		{#if section?.media?.sourceUrl}
			<div>
				{#if section.media.mediaType === 'image'}
					<img
						class="w-full"
						src={section.media.sourceUrl}
						alt={section.media.title}
						width={section.media.mediaDetails.width}
						height={section.media.mediaDetails.height}
					/>
				{/if}
			</div>
		{/if}

		<div class="grid grid-cols-1 gap-6 justify-center p-6 max-w-5xl mx-auto lg:gap-x-20 lg:pl-10">
			<HTMLRenderer
				content={section.title}
				class="HowWorkSection__title dark:text-fr-white lg:hidden"
			/>

			{#each section.referralSteps as step, index}
				<div class="grid gap-6 grid-cols-[max-content__1fr] items-center lg:gap-10 px-4">
					<div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center p-2.5">
						<p class="text-xl font-semibold text-gray-500 leading-none">{index + 1}</p>
					</div>
					<HTMLRenderer content={step?.description} class="dark:text-fr-white max-w-sm" />
				</div>
			{/each}
		</div>
	</div>
</SectionLayout>

<style>
	:global(.HowWorkSection__title h3) {
		@apply text-[2rem] leading-[2.5rem] capitalize font-normal text-center;
	}
</style>
