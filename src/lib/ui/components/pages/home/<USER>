<script lang="ts">
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import type { TWinningSecret } from './../../../../../routes/+page.server.ts';

	import SectionLayout from '$lib/ui/components/layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import H4 from '../../system-design/headings/H4.svelte';

	export let section: TWinningSecret;
</script>

<SectionLayout
	class="WinningSecretSection grid grid-row-2 grid-cols-1 gap-4 justify-center my-[2.8rem] px-4 lg:px-10"
>
	<H3 class="text-[2rem] leading-[2.5rem] dark:text-fr-white capitalize font-normal text-center"
		>{section.title}</H3
	>
	<div
		class="grid grid-cols-1 md:grid-cols-2 gap-6 justify-center p-4 max-w-5xl mx-auto lg:gap-x-20"
	>
		{#each section.features as feature}
			<div class="grid gap-4 items-baseline grid-cols-[max-content__1fr]">
				{#if feature?.icon?.sourceUrl}
					<div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center p-2">
						<img src={feature.icon.sourceUrl} alt={feature.icon.title} />
					</div>
				{/if}

				<div class="-translate-y-1">
					<H4 class="text-xl capitalize mt-0 dark:text-fr-white">{feature.title}</H4>
					<HTMLRenderer content={feature?.description} class="dark:text-fr-white" />
				</div>
			</div>
		{/each}
	</div>
</SectionLayout>
