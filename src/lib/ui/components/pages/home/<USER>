<script lang="ts">
	import { onMount } from 'svelte';
	import Swiper from 'swiper';
	import { Autoplay } from 'swiper/modules';
	import type { TLogoSlider } from './../../../../../routes/+page.server.ts';

	import 'swiper/css';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';

	export let section: TLogoSlider;

	onMount(() => {
		const swiper = new Swiper('.logo-swiper', {
			loop: false,
			autoplay: {
				delay: 5000,
				disableOnInteraction: false
			},
			modules: [Autoplay],
			slidesPerView: 2,
			spaceBetween: 16,
			breakpoints: {
				1024: {
					slidesPerView: 4,
					spaceBetween: 24
				}
			}
		});
	});
</script>

<SectionLayout
	class="LogoSliderSection pb-6 lg:pb-8 border-b-2 border-b-gray-200 dark:border-b-zinc-700"
>
	<H3 class="pl-10 xl:px-24">{section.title}</H3>

	<div class="px-12 xl:px-36">
		<div class="logo-swiper swiper">
			<div class="swiper-wrapper">
				{#each section?.logoList as item}
					{#if item?.logo?.sourceUrl != null}
						<div class="swiper-slide grayscale">
							<img
								class="h-20 object-contain"
								src={item?.logo?.sourceUrl}
								alt={item.logo?.altText ?? item.logo?.title}
								title={item.logo?.title ?? ''}
							/>
						</div>
					{/if}
				{/each}
			</div>
		</div>
	</div>
</SectionLayout>

<style>
	.swiper-slide {
		width: auto;
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
