<script lang="ts">
	import H1 from '$lib/ui/components/system-design/headings/H1.svelte';
	import SectionLayout from '$lib/ui/components/layouts/SectionLayout.svelte';
	import HtmlRenderer from "$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte";
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import Breadcrumb from '../../breadcrumb/Breadcrumb.svelte';

	export let description: string;
	export let title: string;
	export let imageUrl: string | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
</script>

<SectionLayout class="CategorySlugPage__heroBanner max-w-screen-xl mx-auto py-9">
	<div class="CategorySlugPage__heroBanner-container flex flex-row relative items-center gap-4">
		{#if imageUrl}
			<!-- TODO: Modify this once the Category has a Featured Image -->
			<div
				class="CategorySlugPage__heroBanner-description flex flex-col bg-[#3553EFBF] h-fit min-h-[18rem] w-full p-8 gap-4 overflow-auto"
			>
				<H1 class="text-fr-white text-center">{title}</H1>
				<HtmlRenderer content={description} class="text-fr-white md:text-xl"/>
			</div>
		{:else}
			<div
				class="CategorySlugPage__heroBanner-description flex flex-col h-fit min-h-[18rem] w-full p-8 gap-4 overflow-auto text-fr-charcoal dark:text-fr-white"
			>
				{#if breadcrumbs[0]}
					<Breadcrumb items={breadcrumbs}/>
				{/if}
				<H1 class="text-left mt-0">{title}</H1>
				<HtmlRenderer content={description} class="md:text-xl"/>
			</div>
		{/if}
	</div>
</SectionLayout>
