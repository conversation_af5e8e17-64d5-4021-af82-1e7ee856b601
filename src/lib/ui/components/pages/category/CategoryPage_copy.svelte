<script lang="ts">
	import ExpertSummaryCard from '$lib/ui/components/cards/ExpertSummaryCard.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import PageLayout from '$lib/ui/components/layouts/PageLayout.svelte';
	import SectionLayout from '$lib/ui/components/layouts/SectionLayout.svelte';
	import ExpertSearchBox from '$lib/ui/components/search/ExpertSearchBox.svelte';
	import CategorySlugHeroBanner from '$lib/ui/components/pages/category/CategoryHeroBanner.svelte';
	import type { TExtendedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
	import CategoryCard from '../../cards/CategoryCard.svelte';
	import Link from '../../form-elements/Link.svelte';
	import type { TPagePaginationInfo } from '../../../../../routes/categories/_types-interfaces';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';

	export let category: Omit<TExtendedCategoryDto, 'seo'> | null = null;
	export let pageInfo: TPagePaginationInfo | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	let categoryKeywordForSearch = '';
	let parentCategoryName = '';
	$: _title = category?.title;
	$: _description = category?.description;
	$: _hierarchicalLevel = category?.hierarchicalLevel;
	$: _hasSubCategories = category?.subCategories?.[0] ? true : false;
	$: _subCategories = category?.subCategories ?? [];
	$: _parentCategory = category?.parent;
	$: _nextPageUrl = pageInfo?.nextPageUrl;
	$: _previousPageUrl = pageInfo?.previousPageUrl;
	$: {
		if(_hierarchicalLevel === 2) {
			const _titleSegments = _title?.toLowerCase().split(' expert witnesses');
			if(_titleSegments && _titleSegments.length > 1)
				categoryKeywordForSearch = _titleSegments[0];
			parentCategoryName = _parentCategory?.title ?? '';
		}
	}
	$: {
		if(!breadcrumbs[1] || breadcrumbs[1].text.toLowerCase() !== 'categories') {
			breadcrumbs.splice(1, 0, {text: 'Categories', url: '/categories'})
		}
	}
</script>

<CategorySlugHeroBanner description={_description ?? ''} title={_title ?? ''} {breadcrumbs}/>
<PageLayout>
	<MainLayout>
		{#if _hasSubCategories}
			<SectionLayout class="CategoryPage__categoryCards">
				<div class="flex items-center justify-between gap-4 mt-6 lg:mt-10">
					{#if _previousPageUrl}
						<Link
							color="primary"
							class="text-start"
							disabled={!_previousPageUrl}
							linkUrl={_previousPageUrl}
						>
							{'<< Previous'}
						</Link>
					{:else}
						<div></div>
					{/if}
					{#if _nextPageUrl}
						<Link
							color="primary"
							class="text-end"
							disabled={!_nextPageUrl}
							linkUrl={_nextPageUrl}
						>
							{'Next >>'}
						</Link>
					{:else}
						<div></div>
					{/if}
				</div>

				<div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 justify-items-center gap-4">
					{#each _subCategories as subCategory}
						<CategoryCard category={subCategory} />
					{/each}
				</div>

				<div class="flex items-center justify-between gap-4 mt-6 lg:mt-10">
					{#if _previousPageUrl}
						<Link
							color="primary"
							class="text-start"
							disabled={!_previousPageUrl}
							linkUrl={_previousPageUrl}
						>
							{'<< Previous'}
						</Link>
					{:else}
						<div></div>
					{/if}
					{#if _nextPageUrl}
						<Link
							color="primary"
							class="text-end"
							disabled={!_nextPageUrl}
							linkUrl={_nextPageUrl}
						>
							{'Next >>'}
						</Link>
					{:else}
						<div></div>
					{/if}
				</div>
			</SectionLayout>
		{/if}
		<ExpertSearchBox
			useCriteria={false}
			criteria={categoryKeywordForSearch}
			useCountry
			useCountryFilterReference
			useRegion
			useRegionFilterReference
			useState
			useStateFilterReference
			category={[parentCategoryName]}
		>
			<SectionLayout
				class="CategoryPage__resultsSection flex flex-col"
				slot="searchResults"
				let:experts
			>
				{#each experts as { permalink, permalinkSlugSegment, ...rest }}
					<ExpertSummaryCard
						expert={{
							detailsLink: permalink,
							slug: permalinkSlugSegment,
							...rest
						}}
					/>
				{/each}
			</SectionLayout>
		</ExpertSearchBox>
	</MainLayout>
</PageLayout>
