<script lang="ts">
	import type { TReducedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
	import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import CategoryCard from '../../cards/CategoryCard.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import CategorySlugHeroBanner from '$lib/ui/components/pages/category/CategoryHeroBanner.svelte';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import ExpertSearchBox from '../../search/ExpertSearchBox.svelte';
	import ExpertSummaryCard from '../../cards/ExpertSummaryCard.svelte';
	import type { TPreloadedData } from '../../search/_types-interfaces';

	export let categories: TReducedCategoryDto[];
	export let categoryRoot: TExtendedPageDto;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	export let searchPreloadedData:  TPreloadedData | null = null;
</script>

<CategorySlugHeroBanner description={categoryRoot.description ?? ''} title={categoryRoot.title} {breadcrumbs}/>
{#if categories[0]}
	<SectionLayout class="CategoryPage__categoryCards max-w-screen-xl mx-auto p-8 pt-0">
		<div class="flex items-center justify-between gap-1">
			<div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 justify-items-center gap-4">
				{#each categories as category}
					<CategoryCard {category} />
				{/each}
			</div>
		</div>
	</SectionLayout>
{/if}
<PageLayout>
	<MainLayout>
		<ExpertSearchBox
			useCriteriaFilterReference
			useExactMatch
			useCountry
			useCountryFilterReference
			useRegion
			useRegionFilterReference
			useState
			useStateFilterReference
			category={[categories[0].title, categories[1].title]}
			formSubmitActionUrl={null}
			preloadedData={searchPreloadedData}
		>
			<SectionLayout
				class="CategoryPage__resultsSection flex flex-col"
				slot="searchResults"
				let:experts
			>
				{#each experts as { permalink, permalinkSlugSegment, ...rest }}
					<ExpertSummaryCard
						expert={{
							detailsLink: permalink,
							slug: permalinkSlugSegment,
							...rest
						}}
					/>
				{/each}
			</SectionLayout>
		</ExpertSearchBox>
	</MainLayout>
</PageLayout>
