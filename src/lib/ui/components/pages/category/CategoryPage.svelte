<script lang="ts">
	import { page } from '$app/stores';

	import ExpertSummaryCard from '$lib/ui/components/cards/ExpertSummaryCard.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import PageLayout from '$lib/ui/components/layouts/PageLayout.svelte';
	import SectionLayout from '$lib/ui/components/layouts/SectionLayout.svelte';
	import ExpertSearchBox from '$lib/ui/components/search/ExpertSearchBox.svelte';
	import CategorySlugHeroBanner from '$lib/ui/components/pages/category/CategoryHeroBanner.svelte';
	import type { TExtendedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
	import CategoryCard from '../../cards/CategoryCard.svelte';
	import Link from '../../form-elements/Link.svelte';
	import type { TPagePaginationInfo } from '../../../../../routes/categories/_types-interfaces';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import type { TExpertSearchFilterValue, TPreloadedData } from '../../search/_types-interfaces';
	import { goto } from '$app/navigation';

	export let category: Omit<TExtendedCategoryDto, 'seo'> | null = null;
	export let pageInfo: TPagePaginationInfo | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	export let criteriaFilterValue: string |  null = null;
    export let countryFilterValue: string |  null = null;
    export let regionFilterValue: TExpertSearchFilterValue = [];
    export let stateFilterValue: TExpertSearchFilterValue = [];
    export let exactMatchFilterValue: boolean = false;
	export let searchPreloadedData:  TPreloadedData | null = null;
	let categoryKeywordForSearch = '';
	let parentCategoryName = '';
	let searchParamsToExcludeFromUrl = ['category']; //Excludes these from the URL query string
	$: _title = category?.title;
	$: _slug = category?.slug;
	$: _description = category?.description;
	$: _hierarchicalLevel = category?.hierarchicalLevel;
	$: _hasSubCategories = category?.subCategories?.[0] ? true : false;
	$: _subCategories = category?.subCategories ?? [];
	$: _parentCategory = category?.parent;
	$: _nextPageUrl = pageInfo?.nextPageUrl;
	$: _previousPageUrl = pageInfo?.previousPageUrl;
	$: _currentSearchParams = new URLSearchParams($page.url.search);
	$: {
		parentCategoryName = category?.title ?? '';
		// When hierarchical level = 2 the "category" to filter by is the Parent Name, and by default it adds the "criteria" filter params = the category name.
		if(_hierarchicalLevel === 2) {
			const _titleSegments = _title?.toLowerCase().split(' expert witnesses');
			if(_titleSegments && _titleSegments.length > 1)
				categoryKeywordForSearch = _titleSegments[0];
			parentCategoryName = _parentCategory?.title ?? '';
			// Remove the "criteria" from query string when hierarchicalLevel is 2.
			searchParamsToExcludeFromUrl.push('criteria');
		} else {
			// Allows the "criteria" to appear on the query string.
			searchParamsToExcludeFromUrl  = ['category'];
		}
	}
	$: {
		if(!breadcrumbs[1] || breadcrumbs[1].text.toLowerCase() !== 'categories') {
			breadcrumbs.splice(1, 0, {text: 'Categories', url: '/categories'})
		}
	}

	const recreateNewUrlWithSearchParams = (event: CustomEvent<{newUrlSearchParams: URLSearchParams}>) => {
		const  {newUrlSearchParams} = event.detail;
		const pageSearchParam = _currentSearchParams.get('page');
		if(pageSearchParam && !isNaN(Number(pageSearchParam)) && Number(pageSearchParam) > 1) newUrlSearchParams.set('page', pageSearchParam);
		const paramsToString = newUrlSearchParams.toString();
		const newPath = `${$page.url.pathname}${paramsToString ? '?' : ''}${paramsToString}`;
		goto(newPath, { replaceState: true,  keepFocus: true, noScroll: true })
	}
</script>

<CategorySlugHeroBanner description={_description ?? ''} title={_title ?? ''} {breadcrumbs}/>
{#if _hasSubCategories}
	<SectionLayout class="CategoryPage__categoryCards max-w-screen-xl mx-auto p-8 pt-0">
		<div class="flex items-center justify-between gap-1">
			{#if _previousPageUrl}
				<Link
					color="primary"
					class="text-start w-28"
					disabled={!_previousPageUrl}
					linkUrl={_previousPageUrl}
				>
					{'<< Previous'}
				</Link>
			{:else}
				<div></div>
			{/if}

			<div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 justify-items-center gap-4">
				{#each _subCategories as subCategory}
					<CategoryCard category={subCategory} />
				{/each}
			</div>

			{#if _nextPageUrl}
				<Link
					color="primary"
					class="text-end w-28"
					disabled={!_nextPageUrl}
					linkUrl={_nextPageUrl}
				>
					{'Next >>'}
				</Link>
			{:else}
				<div></div>
			{/if}
		</div>
	</SectionLayout>
{/if}
<PageLayout>
	<MainLayout>
		<ExpertSearchBox
			useCriteria={_hierarchicalLevel !== 2}
			criteria={_hierarchicalLevel === 2 ? categoryKeywordForSearch : criteriaFilterValue}
			useCriteriaFilterReference={_hierarchicalLevel !== 2}
			useExactMatch
			exactMatch={exactMatchFilterValue}
			useCountry
			country={countryFilterValue}
			useCountryFilterReference
			useRegion
			region={regionFilterValue}
			useRegionFilterReference
			useState
			state={stateFilterValue}
			useStateFilterReference
			category={[parentCategoryName]}
			formSubmitActionUrl={null}
			excludeFromUrlSearchParams={searchParamsToExcludeFromUrl}
			keyToForceInitialSearch={_slug}
			on:beforeUpdateUrlSearchParams={recreateNewUrlWithSearchParams}
			preloadedData={searchPreloadedData}
		>
			<SectionLayout
				class="CategoryPage__resultsSection flex flex-col"
				slot="searchResults"
				let:experts
			>
				{#each experts as { permalink, permalinkSlugSegment, ...rest }}
					<ExpertSummaryCard
						expert={{
							detailsLink: permalink,
							slug: permalinkSlugSegment,
							...rest
						}}
					/>
				{/each}
			</SectionLayout>
		</ExpertSearchBox>
	</MainLayout>
</PageLayout>
