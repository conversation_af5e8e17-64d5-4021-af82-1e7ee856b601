<script lang="ts">
	import ExpertSummaryCard from '../../cards/ExpertSummaryCard.svelte';
	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import BlogSearchBox from '../../search/BlogSearchBox.svelte';

	export let criteriaFilterValue: string | null = null;
	export let categoryFilterValue: string | null = null;
</script>

<PageLayout class="SearchBlogPage flex flex-col h-full">
	<PageHeader title="Search for Posts" description="" />

	<MainLayout>
		<!-- SEARCHING FEATURES SECTION -->
		<BlogSearchBox
			criteria={criteriaFilterValue}
			useCriteriaFilterReference
			useCategory
			category={categoryFilterValue}
			useCategoryFilterReference
			useClearAll
			useExactMatchFilter
		>
			<!-- SEARCHING RESULTS SECTION (components composition)-->
			<SectionLayout
				class="SearchPage__resultsSection flex flex-col gap-6 lg:gap-8"
				slot="searchResults"
				let:posts
			>
				{#each posts as { permalinkSlugSegment, title, summary, category, ...rest }}
					<a href={`/${permalinkSlugSegment}`}>
						<div class="SearchBlogPage__items">
							<h4 class="text-fr-charcoal dark:text-fr-white text-2xl line-clamp-1 mt-0">
								{title}
							</h4>
							{#if category}
								<p class="text-gray-600 text-sm mb-1.5">{`Blog  /  ${category}`}</p>
							{/if}
							<p class="text-fr-charcoal dark:text-fr-white line-clamp-2">
								{@html summary}
							</p>
						</div>
					</a>
				{/each}
			</SectionLayout>
		</BlogSearchBox>
	</MainLayout>
</PageLayout>
