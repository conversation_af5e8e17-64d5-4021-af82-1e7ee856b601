<script lang="ts">
	import PageLayout from '../../layouts/PageLayout.svelte';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import RequestExpertBox from './RequestExpertBox.svelte';
	import type { TExtendedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
	import H2 from '../../system-design/headings/H2.svelte';
	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import Paragraph from '../../system-design/texts/Paragraph.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import Subtext from '../../system-design/texts/Subtext.svelte';
	import ExpertSearchBox from '../../search/ExpertSearchBox.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import ExpertSummaryCard from '../../cards/ExpertSummaryCard.svelte';

	export let expert: TExtendedExpertDto | null = null;
	$: _title = expert?.title;
	$: _description = expert?.description;
	$: _id = expert?.id;
	$: _country = expert?.country;
	$: _region = expert?.region;
	$: _state = expert?.state;
	$: _summary = expert?.summary;
	$: _hasContent = _description?.trim().length;
	$: _slug = expert?.permalinkSlugSegment;
	$: _imageUrl = expert?.imageUrl;
	$: _inactive = expert?.inactiveExpert;

	function replacePTagsWithH2Tags(htmlString?: string) {
		if (!htmlString) {
			return '';
		}

		return htmlString.replace(/<p>/g, '<h2>').replace(/<\/p>/g, '</h2>');
	}
</script>

<PageLayout class={`ExpertPage flex flex-col h-auto ${_imageUrl ? 'pt-0' : ''}`}>
	{#if _imageUrl}
		<div
			class="h-[400px] bg-center bg-cover absolute left-0 w-full z-0"
			style={`background-image: url('${_imageUrl}');`}
		/>
	{/if}
	<PageHeader
		title={_title}
		titleClass={`capitalize`}
		description={' '}
		class={`ExpertPage__header capitalize ${
			_imageUrl ? 'relative pl-5 bg-fr-white-rgba dark:bg-fr-charcoal-rgba' : ''
		}`}
		style={_imageUrl ? 'margin-top: 280px' : ''}
	>
		<Paragraph class="ExpertPage__header__subtitle flex flex-wrap mb-3" slot="richContent">
			<b>Expert Number:</b>&nbsp;{_id}&nbsp;|&nbsp;
			<b>State:</b>&nbsp;{_state}&nbsp;|&nbsp;
			<b>Region:</b>&nbsp;{_region}&nbsp;|&nbsp;
			<b>Country:</b>&nbsp;{_country}
		</Paragraph>
	</PageHeader>

	<MainLayout class="ExpertPage__container flex flex-col-reverse lg:flex-row gap-8 lg:gap-24">
		<div class="ExpertPage__content flex flex-col">
			{#if _summary}
				<H3 class="ExpertPage__container__label font-medium capitalize text-[1rem]"
					>Provides Testimony In</H3
				>
				<HTMLRenderer
					content={_summary ? replacePTagsWithH2Tags(_summary) : ''}
					class="ExpertPage__contentRenderer__summary dark:text-fr-white"
				/>
			{/if}

			{#if !_inactive}
				<HTMLRenderer
					content={_hasContent ? _description : ''}
					class="ExpertPage__contentRenderer__description dark:text-fr-white"
				/>
			{:else}
				<H2 class="mt-16"
					>Sorry, this expert is not currently available. Here are a few others with a similar
					background…</H2
				>
				<ExpertSearchBox
					criteria={_title?.split('Expert Witness')[0]}
					useCriteria={false}
					usePaginationIndicators={false}
					paginationLimit={10}
				>
					<!-- SEARCHING RESULTS SECTION (components composition)-->
					<SectionLayout
						class="ExpertPage__relatedExperts flex flex-col"
						slot="searchResults"
						let:experts
					>
						{#each experts as { permalink, permalinkSlugSegment, ...rest }}
							{#if rest.id !== _id}
								<ExpertSummaryCard
									expert={{
										detailsLink: permalink,
										slug: permalinkSlugSegment,
										...rest
									}}
								/>
							{/if}
						{/each}
					</SectionLayout>
				</ExpertSearchBox>
			{/if}
		</div>
		{#if _hasContent && _id && _slug && !_inactive}
			<RequestExpertBox class="mt-4" expertSlug={_slug} expertId={_id} />
		{/if}
	</MainLayout>

	{@html `<script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "${_title}",
        "description": "${_summary}",
        "url": "${'https://www.freereferral.com/expert/' + _slug}"
      }
    </script>`}
</PageLayout>

<style>
	:global(.ExpertPage__content .ExpertSummaryCard__container p) {
		@apply my-0;
	}

	:global(.ExpertPage__content p) {
		@apply my-3 tracking-wider;
	}

	:global(.ExpertPage__content span) {
		@apply font-bold block my-2;
	}

	:global(.ExpertPage__content ul) {
		list-style-type: disc;
		padding: 0 0 23px 1em;
		line-height: 26px;
		padding-left: 40px;
	}

	:global(.ExpertPage__contentRenderer__summary h2) {
		font-weight: 400;
		font-family: 'Source Serif Pro', serif;
	}

	:global(.ExpertPage__header h1) {
		text-transform: capitalize;
	}
</style>
