<script lang="ts">
	import PageLayout from '../../layouts/PageLayout.svelte';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import RequestExpertBox from './RequestExpertBox.svelte';
	import type { TExtendedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
	import H2 from '../../system-design/headings/H2.svelte';
	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import Paragraph from '../../system-design/texts/Paragraph.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import Subtext from '../../system-design/texts/Subtext.svelte';
	import ExpertSearchBox from '../../search/ExpertSearchBox.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import ExpertSummaryCard from '../../cards/ExpertSummaryCard.svelte';
	import Tag from '../../badges/tag/index.svelte';

	export let expert: TExtendedExpertDto | null = null;
	$: _title = expert?.title;
	$: _description = expert?.description;
	$: _id = expert?.id;
	$: _hasContent = _description?.trim().length;
	$: _slug = expert?.permalinkSlugSegment;
	$: _imageUrl = expert?.imageUrl;
	$: _inactive = expert?.inactiveExpert;
	$: _topicClusters = expert?.topicClusters ?? [];
</script>

<PageLayout class={`ExpertPage flex flex-col h-auto ${_imageUrl ? 'pt-0' : ''}`}>
	<PageHeader
		title={_title}
		titleClass={`capitalize`}
		description={' '}
		class={`ExpertPage__header capitalize ${
			_imageUrl ? 'relative pl-5 bg-fr-white-rgba dark:bg-fr-charcoal-rgba' : ''
		}`}
		style={_imageUrl ? 'margin-top: 280px' : ''}
	>
	</PageHeader>

	<MainLayout class="ExpertPage__container flex flex-col-reverse lg:flex-row gap-8 lg:gap-24">
		<div class="ExpertPage__content flex flex-col">
			{#if !_inactive}
				<HTMLRenderer
					content={_hasContent ? _description : ''}
					class="ExpertPage__contentRenderer__description dark:text-fr-white"
				/>
			{:else}
				<H2 class="mt-16"
					>Sorry, this expert is not currently available. Here are a few others with a similar
					background…</H2
				>
				<ExpertSearchBox
					criteria={_title?.split('Expert Witness')[0]}
					useCriteria={false}
					usePaginationIndicators={false}
					paginationLimit={10}
				>
					<!-- SEARCHING RESULTS SECTION (components composition)-->
					<SectionLayout
						class="ExpertPage__relatedExperts flex flex-col"
						slot="searchResults"
						let:experts
					>
						{#each experts as { permalink, permalinkSlugSegment, ...rest }}
							{#if rest.id !== _id}
								<ExpertSummaryCard
									expert={{
										detailsLink: permalink,
										slug: permalinkSlugSegment,
										...rest
									}}
								/>
							{/if}
						{/each}
					</SectionLayout>
				</ExpertSearchBox>
			{/if}
		</div>
		{#if _hasContent && _id && _slug && !_inactive}
			<RequestExpertBox
				expertSlug={_slug} 
				expertId={_id}
				expertGlobalRef={expert}
			/>
		{/if}
	</MainLayout>
</PageLayout>

<style lang="css">
	:global(.ExpertPage__content) {
		@apply text-lg;
	}
	
	:global(.ExpertPage__content .ExpertSummaryCard__container p) {
		@apply my-0;
	}

	:global(.ExpertPage__content p) {
		@apply my-3 tracking-wider;
	}

	:global(.ExpertPage__content span) {
		@apply font-bold block my-2;
	}

	:global(.ExpertPage__content ul) {
		list-style-position: inside;
		list-style-type: disc;
		margin-bottom: 1rem;
		margin-top: 1rem;
		padding-left: 1.5rem;
	}

	:global(.ExpertPage__content h2) {
		font-family: Oswald;
		font-size: 1.5rem;
		font-weight: 700;
	}

	:global(.ExpertPage__content h2::before) {
		@apply text-fr-charcoal-rgba dark:text-fr-light-gray mr-1;
		content: "# ";
	}

	:global(.ExpertPage__header h1) {
		text-transform: capitalize;
	}

	:global(.ExpertPage__content .disclaimer) {
		@apply border-fr-orange-rgba border-l-4 pl-2;
	}

	:global(.ExpertPage__content .note) {
		@apply border-fr-navy border-l-4 pl-2;
	}
</style>
