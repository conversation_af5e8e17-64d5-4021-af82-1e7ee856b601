<script lang="ts">
	import Link from '../../form-elements/Link.svelte';
	import Subtext from '../../system-design/texts/Subtext.svelte';
	import Paragraph from '../../system-design/texts/Paragraph.svelte';
	import type { TExtendedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';

	export let expertSlug: string;
	export let expertId: string;
	export let expertGlobalRef: Pick<TExtendedExpertDto, 'id' | 'region' | 'country' | 'state'> | null = null;
</script>

<div
	class={`ExpertRequest sticky top-5 bg-fr-white dark:bg-fr-charcoal flex flex-col justify-center items-center gap-4 p-4 text-center md:min-w-[300px] max-h-[450px] border-2 ${$$props.class}`}
>	{#if expertGlobalRef}
		<div class="flex flex-col gap-2">
			<Subtext class="mb-2">Expert Contact Information</Subtext>
			<ul class="flex flex-col text-left">
				<li><b>Expert Number:</b> {expertGlobalRef.id}</li>
				<li><b>State:</b> {expertGlobalRef.state}</li>
				<li><b>Regions served:</b> {expertGlobalRef.region}</li>
				<li><b>Country:</b> {expertGlobalRef.country}</li>
			</ul>
		</div>
		<hr class="w-full"/>
	{/if}
	<Subtext>Speak With This Expert For Free</Subtext>
	<Link
		linkUrl={`/request-expert-witness?slug=${expertSlug}&id=${expertId}`}
		color="primary"
		uiType="button"
		class="RequestExpertBox"
	>
		Request This Expert
	</Link>
	<Paragraph>Can’t Find The Right Expert?</Paragraph>
	<Link linkUrl={`/request-expert-witness`} color="primary" uiType="link" class="RequestExpertBox">
		Click Here & We Will Help You
	</Link>
</div>
