<script lang="ts">
	import type { TExtendedPageDto, TReducedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import HtmlRenderer from '$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import ArticleLayout from '../../layouts/ArticleLayout.svelte';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import type { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';

	export let experts: TReducedExpertDto[]  = [];
	export let page: TExtendedPageDto | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	$: _pageTitle = page?.title;
	$: _pageDescription = page?.description;
	$: _hasExperts = experts[0] ? true : false;
</script>

<PageLayout class="ExpertsListPage flex flex-col gap-6 px-5 lg:px-12 h-auto">
	<PageHeader title={_pageTitle} description={''} titleClass="capitalize" class="mb-5" {breadcrumbs} />

	<MainLayout class="ExpertsListPage__container flex flex-col gap-8">
		<div class="ExpertsListPage__content flex flex-col">
			<HtmlRenderer
				content={_pageDescription ?? ''}
				class="ExpertsListPage__contentRenderer__description dark:text-fr-white"
			/>
		</div>

		<SectionLayout class="ExpertsListPage__keywordsListSection flex flex-col">
			<ArticleLayout class="ExpertsListPage__keywords flex flex-col">
				{#if _hasExperts}
					<div class="w-full grid gap-y-1 grid-cols-1 md:grid-cols-3">
						{#each experts as { title, permalinkSlugSegment }}
							<a class="ExpertsListPage__keywords__link" href={`/expert/${permalinkSlugSegment}`} title={`See the ${title}' CV page.`}>
								{title}
							</a>
						{/each}
					</div>
				{:else}
					<span class="text-orange-400"
						>Sorry, the Experts Catalog is empty.</span
					>
				{/if}
			</ArticleLayout>
		</SectionLayout>
	</MainLayout>
</PageLayout>

<style lang="css">
	:global(.ExpertsListPage__content p) {
		@apply my-3 tracking-wider;
	}

	:global(.ExpertsListPage__content span) {
		@apply font-bold block my-2;
	}

	:global(.ExpertsListPage__content h2) {
		font-family: Oswald;
		font-size: 1.5rem;
		font-weight: 700;
	}

	:global(.ExpertsListPage__content h2::before) {
		@apply text-fr-charcoal-rgba mr-1;
		content: "# ";
	}

	:global(.ExpertsListPage__content ul, .ExpertsListPage__content ol) {
		list-style-type: disc;
		padding: 0 0 23px 1em;
		line-height: 26px;
		padding-left: 40px;
	}

	.ExpertsListPage__alphabetLetterSelector__link {
		@apply font-bold my-2 underline hover:text-fr-royal-blue mr-2 dark:text-fr-white;
	}

	.ExpertsListPage__alphabetLetterSelector__link--active {
		@apply text-fr-orange;
	}

	.ExpertsListPage__keywordsListSection h5 span a {
		@apply font-bold my-2 last:underline hover:text-fr-royal-blue mr-2 font-oswald;
	}

	.ExpertsListPage__keywordsListSection h5 {
		@apply mt-14;
	}

	.ExpertsListPage__keywords__link {
		@apply font-bold my-2 underline hover:text-fr-royal-blue mr-2 dark:text-fr-white;
	}
</style>
