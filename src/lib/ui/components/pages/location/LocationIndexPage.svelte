<script lang="ts">
	import type { TLocationsDto } from '$lib/shared/api-dtos/internal-rest/location-dtos';
	import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import { theme } from '$lib/ui/state-management/store/theme';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import ArticleLayout from '../../layouts/ArticleLayout.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H1 from '../../system-design/headings/H1.svelte';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import PageHeader from '../../headers/PageHeader.svelte';
	import H2 from '../../system-design/headings/H2.svelte';
	import LinkCard from '../../cards/link-card/LinkCard.svelte';
	import ExpertSearchBox from '../../search/ExpertSearchBox.svelte';
	import ExpertSummaryCard from '../../cards/ExpertSummaryCard.svelte';
	import type { TPreloadedData } from '../../search/_types-interfaces';

	export let locations: TLocationsDto = {};
	export let locationsIndex: string[] = [];
	export let locationIndexFiltered = 'countries';
	export let page: TExtendedPageDto | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	export let searchPreloadedData:  TPreloadedData | null = null;
	const searchParamsToExcludeFromUrl  = ['country', 'region', 'state'];
	$: _hasCountries = locations.countries?.[0] ? true : false;
	$: _countries = locations.countries ?? [];
	$: _hasRegions = locations.regions?.[0] ? true : false;
	$: _regions = locations.regions ?? [];
	$: _hasStates = locations.states?.[0] ? true : false;
	$: _states = locations.states
		? [
				locations.states.slice(0, Math.ceil(locations.states?.length / 2)),
				locations.states.slice(Math.ceil(locations.states?.length / 2))
		  ]
		: [];
</script>

<PageLayout class="LocationIndexPage flex flex-col gap-2 px-5 lg:px-12 h-auto">
	<PageHeader title={page?.title} description={''} titleClass="capitalize" class="mb-[0px]" {breadcrumbs} >
		<H2 slot="richContent" class="normal-case mb-0">{page?.subtitle}</H2>
	</PageHeader>
	<MainLayout class="LocationIndexPage__MainSection">
		<SectionLayout class="LocationIndexPage__MainSection__container flex flex-col">
			<HTMLRenderer content={page?.description} class="mb-5 text-fr-charcoal dark:text-fr-white" />
			<h5 class="LocationIndexPage__locationSelector mt-8">
				{#each locationsIndex as locationIndex}
					<span>
						<a
							class={`LocationIndexPage__locationSelector__link${locationIndex.toLowerCase() === locationIndexFiltered.toLowerCase() ? '--active' : ''} ${$theme ? 'text-fr-white' : 'text-fr-black'} underline mr-2`}
							href={`/${locationIndex.toLowerCase()}`}
						>
							ALL {locationIndex.toUpperCase()}
						</a>
					</span>
				{/each}
			</h5>
			<ArticleLayout class="LocationIndexPage__locationsList flex flex-col p-8 px-0 pt-0">
					<div class="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 justify-items-center gap-4">
						{#if _hasCountries}
							{#each _countries as country}
								<LinkCard data={{label: country.title, link: country.link}} />
							{/each}
						{:else if _hasRegions}
							{#each _regions as region}
								<LinkCard data={{label: region.title, link: region.link}} />
							{/each}
						{:else if _hasStates}
							{#each _states as stateGroup}
								{#each stateGroup as state}
									<LinkCard data={{label: state.title, link: state.link}} />
								{/each}
							{/each}
						{/if}
					</div>
			</ArticleLayout>
		</SectionLayout>

		<ExpertSearchBox
			useCriteria
			useCriteriaFilterReference
			useExactMatch
			useCategory
			useCategoryFilterReference
			excludeFromUrlSearchParams={searchParamsToExcludeFromUrl}
			preloadedData={searchPreloadedData}
		>
			<SectionLayout
				class="LocationIndexPage__resultsSection flex flex-col"
				slot="searchResults"
				let:experts
			>
				{#each experts as { permalink, permalinkSlugSegment, ...rest }}
					<ExpertSummaryCard
						expert={{
							detailsLink: permalink,
							slug: permalinkSlugSegment,
							...rest
						}}
					/>
				{/each}
			</SectionLayout>
		</ExpertSearchBox>
	</MainLayout>
</PageLayout>

<style lang="css">
	.LocationIndexPage__locationSelector p {
		@apply my-3 tracking-wider;
	}

	.LocationIndexPage__locationLinks__link {
		@apply font-bold my-2 underline hover:text-fr-royal-blue mr-2;
	}

	.LocationIndexPage__locationSelector__link--active {
		@apply text-fr-orange;
	}

	.LocationIndexPage__MainSection__container h5 span a {
		@apply font-bold my-2 last:underline hover:text-fr-royal-blue mr-2 font-oswald;
	}

	.LocationIndexPage__MainSection__container h5 {
		@apply mt-14;
	}
</style>
