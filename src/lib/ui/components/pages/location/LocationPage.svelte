<script lang="ts">
	import type { TExtendedLocationDto } from '$lib/shared/api-dtos/internal-rest/location-dtos';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import PageHeader from '../../headers/PageHeader.svelte';
	import ExpertSearchBox from '../../search/ExpertSearchBox.svelte';
	import ExpertSummaryCard from '../../cards/ExpertSummaryCard.svelte';
	import type { TPreloadedData } from '../../search/_types-interfaces';

	export let location: TExtendedLocationDto | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	export let searchPreloadedData:  TPreloadedData | null = null;
	const searchParamsToExcludeFromUrl  = ['country', 'region', 'state'];
</script>

<PageLayout class="LocationPage flex flex-col gap-2 px-5 lg:px-12 h-auto">
	<PageHeader title={location?.title} description={''} titleClass="capitalize" class="mb-[0px]" {breadcrumbs} />
	<MainLayout class="LocationPage__MainSection">
		<SectionLayout class="LocationPage__MainSection__container flex flex-col">
			<HTMLRenderer content={location?.description} class="mb-5 text-fr-charcoal dark:text-fr-white" />
		</SectionLayout>

		<ExpertSearchBox
			useCriteria
			useCriteriaFilterReference
			useExactMatch
			useCategory
			useCategoryFilterReference
			country={location?.locationType === 'expert-country' && location?.title ? location.title : null}
			useCountry={location?.locationType !== 'expert-country'}
			useCountryFilterReference={location?.locationType !== 'expert-country'}
			region={[(location?.locationType === 'expert-region' && location?.title ? location.title : null)]}
			useRegion={location?.locationType !== 'expert-region'}
			useRegionFilterReference={location?.locationType !== 'expert-region'}
			useState={location?.locationType !== 'expert-state'}
			state={[(location?.locationType === 'expert-state' && location?.title ? location.title : null)]}
			useStateFilterReference={location?.locationType !== 'expert-state'}
			excludeFromUrlSearchParams={searchParamsToExcludeFromUrl}
			preloadedData={searchPreloadedData}
		>
			<SectionLayout
				class="LocationPage__resultsSection flex flex-col"
				slot="searchResults"
				let:experts
			>
				{#each experts as { permalink, permalinkSlugSegment, ...rest }}
					<ExpertSummaryCard
						expert={{
							detailsLink: permalink,
							slug: permalinkSlugSegment,
							...rest
						}}
					/>
				{/each}
			</SectionLayout>
		</ExpertSearchBox>
	</MainLayout>
</PageLayout>

<style lang="css">
	.LocationPage__locationSelector p {
		@apply my-3 tracking-wider;
	}

	.LocationPage__locationLinks__link {
		@apply font-bold my-2 underline hover:text-fr-royal-blue mr-2;
	}

	.LocationPage__locationSelector__link--active {
		@apply text-fr-orange;
	}

	.LocationPage__MainSection__container h5 span a {
		@apply font-bold my-2 last:underline hover:text-fr-royal-blue mr-2 font-oswald;
	}

	.LocationPage__MainSection__container h5 {
		@apply mt-14;
	}
</style>
