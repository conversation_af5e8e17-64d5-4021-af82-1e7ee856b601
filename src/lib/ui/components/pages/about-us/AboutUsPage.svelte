<script lang="ts">
	import type { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
	import PageLayout from '../../layouts/PageLayout.svelte';
	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from "../../layouts/MainLayout.svelte";
    import HtmlRenderer from "$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte";

	export let aboutUsContent: TExtendedPageDto |  null = null;
	$: _pageTitle = aboutUsContent?.title;
	$: _pageDescription = aboutUsContent?.description;
		
</script>


<PageLayout class="AboutUsPage flex flex-col gap-8 px-5 lg:px-12 h-auto">
	<PageHeader title={_pageTitle} description={""}/>

	<MainLayout class="AboutUsPage__container flex flex-col-reverse lg:flex-row gap-8 lg:gap-24">
		<div class="AboutUsPage__content flex flex-col">

			<HtmlRenderer
					content={_pageDescription ?? ""}
					class="AboutUsPage__contentRenderer__description dark:text-fr-white"
				/>
		</div>
	</MainLayout>

</PageLayout>

<style>
	:global(.AboutUsPage__content .ExpertSummaryCard__container p) {
		@apply my-0;
	}

	:global(.AboutUsPage__content p) {
		@apply my-3 tracking-wider;
	}

	:global(.AboutUsPage__content span) {
		@apply font-bold block my-2;
	}

	:global(.AboutUsPage__content ul) {
		list-style-type: disc;
		padding: 0 0 23px 1em;
		line-height: 26px;
		padding-left: 40px;
	}

</style>