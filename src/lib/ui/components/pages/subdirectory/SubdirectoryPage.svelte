<script lang="ts">
	import type { TExtendedPageDto, TReducedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import HtmlRenderer from '$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte';
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import ArticleLayout from '../../layouts/ArticleLayout.svelte';
	import H2 from '../../system-design/headings/H2.svelte';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import Link from '../../form-elements/Link.svelte';

	export let subdirectoryPageContent: TExtendedPageDto | null = null;
	export let keywords: TExtendedPageDto[] = [];
	export let keywordIndexFiltered: string = 'a';
	export let keywordsIndexes: string[] = [keywordIndexFiltered];
	export let hasNextPage: boolean = false;
	export let hasPreviousPage: boolean = false;
	export let nextPageUrl: string  = '';
	export let previousPageUrl: string = '';
	export let total: number = 0;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	$: _pageTitle = subdirectoryPageContent?.title;
	$: _pageDescription = subdirectoryPageContent?.description;
	$: _hasKeywords = keywords[0] ? true : false;
</script>

<PageLayout class="SubdirectoryPage flex flex-col gap-6 px-5 lg:px-12 h-auto">
	<PageHeader title={_pageTitle} description={''} titleClass="capitalize" class="mb-5" {breadcrumbs}>
		<H2 slot="richContent" class="normal-case mb-0">
			Browse
			{#if total}among {total} {/if}
			specialties starting with "{keywordIndexFiltered.toUpperCase()}"</H2>
	</PageHeader>

	<MainLayout class="SubdirectoryPage__container flex flex-col gap-8">
		<div class="SubdirectoryPage__content flex flex-col">
			<HtmlRenderer
				content={_pageDescription ?? ''}
				class="SubdirectoryPage__contentRenderer__description dark:text-fr-white"
			/>
		</div>

		<SectionLayout class="SubdirectoryPage__keywordsListSection flex flex-col">
			<h5 class="SubdirectoryPage__alphabetLetterSelector text-center mt-8 mb-14">
				{#each keywordsIndexes as alphabetLetter}
					<span>
						<a
							class={`SubdirectoryPage__alphabetLetterSelector__link${
								keywordIndexFiltered.toUpperCase() === alphabetLetter.toUpperCase() ? '--active' : ''
							}`}
							href={`/subdirectory/${alphabetLetter.toLowerCase()}`}
						>
							{alphabetLetter.toUpperCase()}
						</a>
					</span>
				{/each}
			</h5>

			<ArticleLayout class="SubdirectoryPage__keywords flex flex-col gap-0 sm:gap-8">
				{#if _hasKeywords}
					<div class="SubdirectoryPage__keywords__navs flex flex-row justify-between">
						{#if hasPreviousPage}
							<Link
								color="primary"
								class="text-start w-28"
								linkUrl={previousPageUrl}
							>
								{'<< Previous'}
							</Link>
						{:else}
							<div></div>
						{/if}

						{#if hasNextPage}
							<Link
								color="primary"
								class="text-end w-28"
								linkUrl={nextPageUrl}
							>
								{'Next >>'}
							</Link>
						{:else}
							<div></div>
						{/if}
					</div>
					

					<div class="w-full grid gap-y-1 grid-cols-1 md:grid-cols-3">
						{#each keywords as { algoliaSearchKeyword, slug,  }}
							<a class="SubdirectoryPage__keywords__link" href={`/${slug}`}>
								{algoliaSearchKeyword}
							</a>
						{/each}
					</div>
				{:else}
					<span class="text-orange-400"
						>Sorry, there is no results for keywords that start with {keywordIndexFiltered}</span
					>
				{/if}
			</ArticleLayout>
		</SectionLayout>
	</MainLayout>
</PageLayout>

<style lang="css">
	:global(.SubdirectoryPage__content p) {
		@apply my-3 tracking-wider;
	}

	:global(.SubdirectoryPage__content span) {
		@apply font-bold block my-2;
	}

	:global(.SubdirectoryPage__content ul, .SubdirectoryPage__content ol) {
		list-style-type: disc;
		padding: 0 0 23px 1em;
		line-height: 26px;
		padding-left: 40px;
	}

	.SubdirectoryPage__alphabetLetterSelector__link {
		@apply font-bold my-2 underline hover:text-fr-royal-blue mr-2 dark:text-fr-white;
	}

	.SubdirectoryPage__alphabetLetterSelector__link--active {
		@apply text-fr-orange;
	}

	.SubdirectoryPage__keywordsListSection h5 span a {
		@apply font-bold my-2 last:underline hover:text-fr-royal-blue mr-2 font-oswald;
	}

	.SubdirectoryPage__keywordsListSection h5 {
		@apply mt-14;
	}

	.SubdirectoryPage__keywords__link {
		@apply font-bold my-2 underline hover:text-fr-royal-blue mr-2 dark:text-fr-white;
	}
</style>
