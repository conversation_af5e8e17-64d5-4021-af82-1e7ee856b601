import { createWritablePersistStore } from "$lib/ui/state-management/store/store";
import { derived, writable } from "svelte/store";
import { contactUsFormInitErrors, contactUsFormInitStatus, contactUsFormInitValues } from "./_constants";
import type { TContactUsFormErrors, TContactUsFormStatus, TContactUsFormValues } from "./_types-interfaces";

//TODO: Transform this into a generic Form Store
const contactUsFormValuesStore = createWritablePersistStore<TContactUsFormValues>('contact-us-form-values', contactUsFormInitValues);
const contactUsFormErrorsStore = writable<TContactUsFormErrors>(contactUsFormInitErrors);
const contactUsFormStatusStore = writable<TContactUsFormStatus>(contactUsFormInitStatus);

export const contactUsFormStore = derived(
    [contactUsFormValuesStore, contactUsFormErrorsStore, contactUsFormStatusStore], 
    ([$contactUsFormValuesStore, $contactUsFormErrorsStore]) => {
      return {
        formValues: contactUsFormValuesStore,
        formHasValues: () => $contactUsFormValuesStore.firstName ||
            $contactUsFormValuesStore.lastName ||
            $contactUsFormValuesStore.email ||
            $contactUsFormValuesStore.confirmEmail ||
            $contactUsFormValuesStore.phone ||
            $contactUsFormValuesStore.address ||
            $contactUsFormValuesStore.city ||
            $contactUsFormValuesStore.state ||
            $contactUsFormValuesStore.zipCode ||
            $contactUsFormValuesStore.company,
        formErrors: contactUsFormErrorsStore,
        formHasErrors: () => {
          let formKey: keyof typeof $contactUsFormErrorsStore;
          for (formKey in $contactUsFormErrorsStore) {
              if (formKey !== 'form' && $contactUsFormErrorsStore[formKey]) return true;
          }
          
          return false;
        },
        formStatus: contactUsFormStatusStore,
      };
    }
  );
