import type { TContactUsFormErrors, TContactUsFormStatus, TContactUsFormValues } from "../contact-us/_types-interfaces";

export const contactUsFormInitValues: TContactUsFormValues = {
    firstName: null,
    lastName: null,
    email: null,
    confirmEmail: null,
    phone: null,
    description: null,
    address: null,
    city: null,
    state: null,
    zipCode: null,
    company: null
}

export const contactUsFormInitErrors: TContactUsFormErrors = {
    form: null,
    firstName: null,
    lastName: null,
    email: null,
    confirmEmail: null,
    phone: null,
    description: null,
    address: null,
    city: null,
    state: null,
    zipCode: null,
    company: null,
}

export const contactUsFormInitStatus: TContactUsFormStatus = {
    success: null,
}; 