import type { TContactUsFormFieldsDto } from "$lib/shared/api-dtos/internal-rest/contact-dto";

export type TContactUsFormValues = Omit<
    TContactUsFormFieldsDto,
    'firstName' | 'lastName' | 'email' | 'confirmEmail' | 'phone' | 'description'
> & {
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    confirmEmail: string | null;
    phone: string | null;
    description: string | null;
};

export type TContactUsFormErrors = {
    [key in keyof (TContactUsFormFieldsDto)]?: string | null;
} & {form?: string | null;}

export type TContactUsFormStatus = {
    success: boolean | null;
    message?: string | undefined;
}

export type TContactUsForm = {
    values: TContactUsFormValues,
    errors: TContactUsFormErrors
};