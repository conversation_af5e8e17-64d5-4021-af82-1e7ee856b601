<script lang="ts">
	import PageLayout from '../../layouts/PageLayout.svelte';
	import PageHeader from '../../headers/PageHeader.svelte';
	import { PAGE_TYPES, type TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import StandardPage from './templates/StandardPage.svelte';
	import KeywordPage from './templates/KeywordPage.svelte';
	import { TOPIC_CLUSTER_ITEMS_TYPE, type TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import type { TPreloadedData } from '../../search/_types-interfaces';
	import type { TTopicClusterBlogPostItemsDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';
	import TopicClusterRelatedArticles from '../../topic-cluster/related-articles.svelte';

	export let page: TExtendedPageDto | null = null;
	export let topicClusterItems: TTopicClusterBlogPostItemsDto | null = null;
	export let breadcrumbs: TSeoBreadcrumb[] = [];
	export let searchPreloadedData:  TPreloadedData | null = null;
	$: _title = page?.title;
	$: _imageUrl = page?.imageUrl;
	$: _pageType = page?.pageType;
	$: {
		if(!breadcrumbs[1] || breadcrumbs[1].text.toLowerCase() !== 'subdirectory') {
			breadcrumbs.splice(1, 0, {text: 'Subdirectory', url: '/subdirectory/a'});
			const index = _title?.[0].toLowerCase() ?? 'a'
			breadcrumbs.splice(2, 0, {text: index, url: `/subdirectory/${index}`});
		}
	}
</script>

<PageLayout class={`GenericPage flex flex-col h-auto ${_imageUrl ? 'pt-0' : ''}`}>
	{#if _imageUrl}
		<div
			class="h-[400px] bg-center bg-cover absolute left-0 w-full z-0"
			style={`background-image: url('${_imageUrl}');`}
		/>
	{/if}
	<PageHeader
		title={_title}
		titleClass={`capitalize`}
		description={' '}
		class={`GenericPage__header capitalize ${
			_imageUrl ? 'relative pl-5 bg-fr-white-rgba dark:bg-fr-charcoal-rgba' : ''
		} ${page?.title.toLocaleLowerCase().includes('publish an article') ? 'text-center' : ''}`}
		style={_imageUrl ? 'margin-top: 280px' : ''}
		breadcrumbs={_pageType === PAGE_TYPES.KEYWORD ? breadcrumbs : []}
	/>

	<!-- USE THIS APPROACH TO ASSIGN THE PROPER TEMPLATE SECTION ACCORDING TO THE PAGE TYPE -->
	{#if _pageType === PAGE_TYPES.STANDARD}
		<StandardPage {page} />
	{/if}
	{#if _pageType === PAGE_TYPES.KEYWORD}
		<KeywordPage 
			pageData={page}
			{searchPreloadedData}
		/>
	{/if}
</PageLayout>

{#if topicClusterItems?.items[0]}
	{#if topicClusterItems?.type === TOPIC_CLUSTER_ITEMS_TYPE.POST}
		<TopicClusterRelatedArticles 
			relatedArticles={topicClusterItems?.items} 
			sectionTitle={`More about "${_title}"`}
		/>
	{/if}
{/if}

