<script lang="ts">
	import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';
	import HtmlRenderer from '$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte';

	export let page: TExtendedPageDto | null = null;
	$: _title = page?.title;
	$: _description = page?.description;
	$: _hasContent = _description?.trim().length;
	$: _slug = page?.slug;
	$: _imageUrl = page?.imageUrl;
</script>

<MainLayout class="StandardPage__container flex flex-col-reverse lg:flex-row gap-8 lg:gap-24">
	<div class="StandardPage__content flex flex-col">
		{#if _hasContent}
			<HtmlRenderer
				content={_description}
				class="StandardPage__contentRenderer__description dark:text-fr-white"
			/>
		{/if}
	</div>
</MainLayout>

<style lang="css">
	:global(.StandardPage__content p) {
		@apply my-3 tracking-wider;
	}

	:global(.StandardPage__content span a) {
		@apply font-bold my-2 last:underline hover:text-fr-royal-blue;
	}

	:global(.StandardPage__content h1) {
		@apply text-[2rem] leading-[2.5rem] text-fr-charcoal dark:text-fr-white uppercase;
	}
	:global(.StandardPage__content h2) {
		@apply text-[1.5rem] leading-[2rem] text-fr-charcoal dark:text-fr-white;
	}

	:global(.StandardPage__content h2::before) {
		@apply text-fr-charcoal-rgba dark:text-fr-light-gray mr-1;
		content: "# ";
	}

	:global(.StandardPage__content h3) {
		@apply text-[1.25rem] leading-[1.75rem] text-fr-charcoal dark:text-fr-white;
	}
	:global(.StandardPage__content h4) {
		@apply text-[1.125rem] leading-[1.5rem] text-fr-charcoal dark:text-fr-white uppercase;
	}
	:global(.StandardPage__content h5) {
		@apply mt-14;
	}

	:global(.StandardPage__content h2 a),
	:global(.StandardPage__content h3 a),
	:global(.StandardPage__content h4 a),
	:global(.StandardPage__content p a) {
		@apply font-bold my-2 underline text-fr-royal-blue;
	}

	:global(.StandardPage__content ul) {
		list-style-type: disc;
		padding: 0 0 23px 1em;
		line-height: 26px;
		padding-left: 40px;
	}
	:global(.StandardPage__contentRenderer__description h5 span strong a) {
		@apply font-oswald underline text-fr-orange;
	}

	:global(.StandardPage__contentRenderer__description table tbody tr td) {
		@apply pr-14;
	}

	:global(.StandardPage__content .disclaimer) {
		border-left-width: 4px;
		border-color: #fc5a2bb3;
		padding-left: .5rem;
	}

	.StandardPage__header h1 {
		text-transform: capitalize;
	}
	
</style>
