<script lang="ts">
	import type { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
	import MainLayout from "$lib/ui/components/layouts/MainLayout.svelte";
    import HtmlRenderer from "$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte";
	import ExpertSearchBox from "$lib/ui/components/search/ExpertSearchBox.svelte";
	import SectionLayout from "$lib/ui/components/layouts/SectionLayout.svelte";
	import ExpertSummaryCard from "$lib/ui/components/cards/ExpertSummaryCard.svelte";
	import type { TFilterSelected, TPreloadedData } from "$lib/ui/components/search/_types-interfaces";
	import { goto } from '$app/navigation';

	export let pageData: TExtendedPageDto | null = null;
	export let searchPreloadedData:  TPreloadedData | null = null;
	$: _description = pageData?.description;
	$: _hasContent = _description?.trim().length;
	$: _searchKeyword = pageData?.algoliaSearchKeyword ?? "";
	const excludeFromUrlSearchParams = ['criteria'];

	const _onSubmit = (event: CustomEvent<{searchValues: TFilterSelected[]}>) => {
		const {searchValues} = event.detail;
		const params = new URLSearchParams();
		// Check current filters selected
		for (const searchValue of searchValues) {
			if(searchValue && searchValue.value && searchValue.id) {
				const {id: queryParameter, value: queryValue} = searchValue;
				let shouldAppend = true;
				if(queryParameter.toLowerCase().includes('country') || queryParameter.toLowerCase().includes('criteria') || queryParameter.toLowerCase().includes('expertNumber'))
					shouldAppend = false;

				if(shouldAppend) {
					const idSegments = queryParameter.split('-', 1);
					if(idSegments[0] && !excludeFromUrlSearchParams.includes(idSegments[0])) params.append(idSegments[0], queryValue);
				} else {
					params.set(queryParameter, queryValue);
				}
			}
		}
		
		const paramsToString = params.toString();
		const newPath = `/search${paramsToString ? '?' : ''}${paramsToString}`;
		goto(newPath, { replaceState: true})
	}
</script>

<MainLayout class="Keywordpage__container flex flex-col-reverse lg:flex-row gap-8 lg:gap-24">
    <div class="Keywordpage__content flex flex-col">
        {#if _hasContent}
            <HtmlRenderer
                content={_description}
                class="Keywordpage__contentRenderer__description dark:text-fr-white"
            />
        {/if}

		{#if _searchKeyword}
				<ExpertSearchBox
					criteria={_searchKeyword}
					useCriteria={true}
					useCategory
					useCategoryFilterReference
					useCountry
					useCountryFilterReference
					useRegion
					useRegionFilterReference
					useState
					useStateFilterReference
					shouldUpdateUrlSearchParams
					excludeFromUrlSearchParams={['criteria']}
					on:beforeSubmit={_onSubmit}
					stopOnBeforeSubmitEvent
					preloadedData={searchPreloadedData}
				>
					<!-- SEARCHING RESULTS SECTION (components composition)-->
					<SectionLayout class="Keywordpage__relatedExperts flex flex-col" slot="searchResults" let:experts={experts}>
						{#each experts as {permalink, permalinkSlugSegment, ...rest}}
							<ExpertSummaryCard  
								expert={{
									detailsLink: permalink, 
									slug: permalinkSlugSegment,
									...rest
								}}
								titleHeader="h3"
							/>
						{/each}
					</SectionLayout>
				</ExpertSearchBox>
			{/if}
    </div>
</MainLayout>

<style lang="css">
	:global(.Keywordpage__content p) {
		@apply my-3 tracking-wider text-lg;
	}

	:global(.Keywordpage__content section) {
		@apply mt-10;
	}
	
	:global(.Keywordpage__content h2) {
		font-family: Oswald;
		font-size: 1.5rem;
		font-weight: 700;
	}

	:global(.Keywordpage__content h2::before) {
		@apply text-fr-charcoal-rgba dark:text-fr-light-gray mr-1;
		content: "# ";
	}

	:global(.Keywordpage__relatedExperts h2::before) {
		content: none;
	}

	:global(.Keywordpage__content details summary) {
		font-size: 18px;
		font-weight: 700;
	}

	:global(.Keywordpage__content details p) {
		@apply ml-[18px] mb-[18px];
	}
	:global(.Keywordpage__content details) {
		margin: 10px 10px 10px 20px;
	}

	:global(.Keywordpage__content .ExpertSummaryCard__container p) {
		@apply my-0;
	}

	:global(.Keywordpage__content span a) {
		@apply font-bold my-2 last:underline hover:text-fr-royal-blue;
	}

	:global(.Keywordpage__content h5) {
		@apply mt-14;
	}

	:global(.Keywordpage__content p a) {
		@apply font-bold my-2 underline hover:text-fr-royal-blue;
	}

	:global(.Keywordpage__content ul) {
		list-style-type: disc;
		padding: 0 0 23px 1em;
		line-height: 26px;
		padding-left: 40px;
	}
	:global(.Keywordpage__contentRenderer__description h5 span strong a ) {
		@apply font-oswald underline text-fr-orange;
	}

	:global(.Keywordpage__contentRenderer__description table tbody tr td ) {
		@apply pr-14;
	}

	.Keywordpage__header h1 {
		text-transform: capitalize;
	}

</style>
