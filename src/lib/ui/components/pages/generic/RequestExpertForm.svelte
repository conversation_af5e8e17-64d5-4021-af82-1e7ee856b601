<script lang="ts">
	import SectionLayout from '../../layouts/SectionLayout.svelte';
	import H3 from '../../system-design/headings/H3.svelte';
	import Paragraph from '../../system-design/texts/Paragraph.svelte';
	import Button from '../../form-elements/Button.svelte';
	import ExpertSimpleSearchBox from '../../search/ExpertSimpleSearchBox.svelte';
	import H2 from '../../system-design/headings/H2.svelte';
</script>

<SectionLayout
	class="FeatureSection w-full pt-10 pb-6 lg:pb-10 justify-items-center gap-4 text-center dark:text-fr-white bg-gray-100 dark:bg-zinc-800 dark:border-b-zinc-700"
>
	<div class="flex flex-col h-auto justify-center text-center gap-4 px-4">
		<div class="max-w-xl mx-auto">
			<H2 class="text-[2rem] leading-[2.5rem] capitalize mb-4">Find An Expert Witness</H2>
			<Paragraph class="dark:text-fr-white leading-normal">
				Search our directory of 30,000+ categories of CV's of highly-qualified and hard-to-find
				experts and consultants.
			</Paragraph>
		</div>

		<div class="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:gap-20 mt-2 lg:mt-6">
			<ExpertSimpleSearchBox
				useCustomSearchAction
				useNoPadding
				customPlaceholder="What type of Expert Witness are you looking for?"
			/>

			<div class="pt-4 lg:p-0">
				<a href={'/request-expert-witness'}>
					<Button color="Secondary" class="h-12">Request An Expert</Button>
				</a>

				<Paragraph class="text-gray-600 mt-4"
					>We take care of the details and search for you. we respond in as fast as within 1 hour.*</Paragraph
				>
			</div>
		</div>
	</div>
</SectionLayout>
