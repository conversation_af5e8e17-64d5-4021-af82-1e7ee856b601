<script lang="ts">
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import type { SubmitFunction } from '@sveltejs/kit';
	import { fade } from 'svelte/transition';
	import { onMount } from 'svelte';

	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import Select from '$lib/ui/components/form-elements/inputs/Select/index.svelte';
	import TextField from '$lib/ui/components/form-elements/inputs/TextField/index.svelte';
	import Button from '../../form-elements/Button.svelte';
	import SmallText from '../../system-design/texts/SmallText.svelte';
	import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import {BecomeAuthorFormSchema, BecomeAuthorModelSchema} from '$lib/shared/validation/schemas/contact-forms/become-contributing-author.schema';
	import { becomeAuthorFormStore } from './_store';
	import { positions } from './_constants';
	import Modal from '../../over-layer/Modal/index.svelte';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import IoMdCloseCircleOutline from 'svelte-icons/io/IoMdCloseCircleOutline.svelte';
	import IoMdCheckmarkCircleOutline from 'svelte-icons/io/IoMdCheckmarkCircleOutline.svelte';
	import type { TBecomeAuthorFormErrors } from './_types-interfaces';

	$: ({ formValues, formErrors, formHasValues, formHasErrors, formStatus } =
		$becomeAuthorFormStore);
	let scrollY: any;
	let isSubmitting: boolean | null = null;
	let toggleModalOpened: boolean = false;

	export let page: TExtendedPageDto;

	const clearForm = async () => {
		if (formHasValues()) {
			const emptyFormValues = $formValues;
			let formField: keyof typeof emptyFormValues;
			for (formField in emptyFormValues) {
				$formValues = { ...$formValues, [formField]: null };
			}

			// This will remove the Query Parameters from the URL in case they exists.
			await goto('become-contributing-author/');
		}
	};

	const _validateFormField = (value: string, field: string) => {
		$formValues = { ...$formValues, [field]: value };
		const schemaShape = BecomeAuthorFormSchema.shape;
		if(field in schemaShape) {
			const parsedFieldRes =
				BecomeAuthorFormSchema.shape[field as keyof typeof schemaShape].safeParse(value);
			if (!parsedFieldRes.success) {
				const errorMessage = parsedFieldRes.error.issues[0].message;
				$formErrors = { ...$formErrors, [field]: errorMessage };
			} else $formErrors = { ...$formErrors, [field]: null };
		}
	};

	const onTextFieldPressKey = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		_validateFormField(value, fieldId);
	};

	const onCleanField = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		_validateFormField(value, fieldId);
	};

	const onSubmit: SubmitFunction = (formRequest: any) => {
		if (!isSubmitting) {
			isSubmitting = true;
			$formErrors = {};
			scrollY = 0;
			const parsedFormRes = BecomeAuthorModelSchema.safeParse($formValues);
			if (!parsedFormRes.success) {
				parsedFormRes.error.issues.forEach((issue) => {
					$formErrors = { ...$formErrors, [issue.path[0]]: issue.message };
				});

				formRequest.cancel();
				$formStatus = {
					success: false,
					message: 'The form has invalid or empty values. Please check.'
				};
				isSubmitting = false;
				toggleModalOpened = true;
			}

			return async ({ result: { data } }: any) => {
				const {success, ...rest} = data;
				if (!success) {
					const {isAValidationError, errors} = rest;
					if (isAValidationError) {
						$formStatus = {
							success: false,
							message: 'The form has invalid or empty values. Please check.',
						};
					} else {
						$formStatus = {
							success: false,
							message: 'Your request could not be processed, please try again later'
						};
					}
					if(errors) $formErrors = errors as TBecomeAuthorFormErrors;

					isSubmitting = false;
					toggleModalOpened = true;
				} else {
					const {message} = rest;
					$formStatus = {
						success: true,
						message,
					};
					isSubmitting = false;
					toggleModalOpened = true;
					setTimeout(async() => {
						await goto('/thank-you-for-your-author-application');
					}, 1500);
				}
			};
		}
	};

	const closeModal = () => {
		toggleModalOpened = false;
		$formStatus = { success: null };
	};

	onMount(() => {
		$becomeAuthorFormStore.formValues.useLocalStorage();
	});
</script>

<!-- svelte-ignore missing-declaration -->
<PageLayout class="BecomeAuthorPage flex flex-col h-full">
	<PageHeader
		title={page?.title ?? ''}
		description={page?.description ?? ''}
		class="BecomeAuthorPage__header text-center"
	/>

	<MainLayout>
		<form
			method="POST"
			use:enhance={onSubmit}
			class={`BecomeAuthorPage__form flex flex-col gap-10 ${isSubmitting ? 'animate-pulse' : ''}`}
		>
			<!-- NAME  fields -->
			<div class="BecomeAuthorPage__form__row-container flex flex-col gap-5 lg:flex-row">
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/3">
					<TextField
						id="firstName"
						name="firstName"
						label="First Name"
						value={$formValues.firstName}
						maxlength=100
                        disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.firstName}
							<div class="text-fr-orange">{$formErrors.firstName}</div>
                        {/if}
					</SmallText>
				</div>
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/3">
					<TextField
						id="lastName"
						name="lastName"
						label="Last Name"
						value={$formValues.lastName}
						maxlength=100
                        disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.lastName}
							<div class="text-fr-orange">{$formErrors.lastName}</div>
                        {/if}
					</SmallText>
				</div>
				<!-- PHONE -->
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/3">
					<TextField
						id="phone"
						name="phone"
						label="Phone"
						value={$formValues.phone}
						maxlength=20
                        disabled={isSubmitting}
						useCleanIcon
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.phone}
							<div class="text-fr-orange">{$formErrors.phone}</div>
                        {/if}
					</SmallText>
				</div>
			</div>

			<!-- EMAIL  fields -->
			<div class="BecomeAuthorPage__form__row-container flex flex-col gap-5 lg:flex-row">
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/2">
					<TextField
						id="email"
						name="email"
						label="Email"
						value={$formValues.email}
                        disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.email}
							<div class="text-fr-orange">{$formErrors.email}</div>
						{/if}
					</SmallText>
				</div>
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/2">
					<TextField
						id="confirmEmail"
						name="confirmEmail"
						label="Confirm Email"
						value={$formValues.confirmEmail}
                        disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.confirmEmail}
							<div class="text-fr-orange">{$formErrors.confirmEmail}</div>
						{/if}
					</SmallText>
				</div>
			</div>

			<div class="BecomeAuthorPage__form__row-container flex flex-col gap-5 lg:flex-row">
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/2">
					<TextField
						id="company"
						name="company"
						label="Company Name"
						value={$formValues.company}
						maxlength=100
                        disabled={isSubmitting}
						useCleanIcon
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.company}
							<div class="text-fr-orange">{$formErrors.company}</div>
                        {/if}
					</SmallText>
				</div>
				<!-- POSITION -->
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/2">
					<Select
						id="position"
						name="position"
						label="Position"
						value={$formValues.position}
                        disabled={isSubmitting}
						options={positions}
						required
						on:onChange={(event) => onTextFieldPressKey(event)}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.position}
							<div class="text-fr-orange">{$formErrors.position}</div>
						{/if}
					</SmallText>
				</div>
			</div>

			<!-- EXPERTISE -->
			<div class="BecomeAuthorPage__form__field-container text-left">
				<label for="expertise" class=" flex items-baseline dark:text-fr-white">
					Expertise/Area of Law
				</label>
				<TextField
					id="expertise"
					name="expertise"
					value={$formValues.expertise}
					maxlength=100
					disabled={isSubmitting}
					useCleanIcon
					on:onKeyUp={onTextFieldPressKey}
					on:onCleanField={onCleanField}
				/>
				<SmallText class="mt-1 h-4 font-oswald">
					{#if $formErrors.expertise}
						<div class="text-fr-orange">{$formErrors.expertise}</div>
					{/if}
				</SmallText>
			</div>

			<!-- ADDRESS -->
			<div class="BecomeAuthorPage__form__field-container text-left">
				<TextField
					id="address"
					name="address"
					label="Address"
					value={$formValues.address}
					maxlength=150
					disabled={isSubmitting}
					useCleanIcon
					required
					on:onKeyUp={onTextFieldPressKey}
					on:onCleanField={onCleanField}
				/>
				<SmallText class="mt-1 h-4 font-oswald">
					{#if $formErrors.address}
						<div class="text-fr-orange">{$formErrors.address}</div>
					{/if}
				</SmallText>
			</div>

			<!-- ADDRESS complementary  fields -->
			<div class="BecomeAuthorPage__form__row-container flex flex-col gap-5 lg:flex-row">
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/3">
					<TextField
						id="city"
						name="city"
						label="City"
						value={$formValues.city}
						maxlength=100
						disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.city}
							<div class="text-fr-orange">{$formErrors.city}</div>
						{/if}
					</SmallText>
				</div>
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/3">
					<TextField
						id="state"
						name="state"
						label="State"
						value={$formValues.state}
						maxlength=100
						disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.state}
							<div class="text-fr-orange">{$formErrors.state}</div>
						{/if}
					</SmallText>
				</div>
				<div class="BecomeAuthorPage__form__field-container text-left w-full lg:w-1/3">
					<TextField
						id="zipCode"
						name="zipCode"
						label="Zip/Postal Code"
						value={$formValues.zipCode}
						maxlength=10
						disabled={isSubmitting}
						useCleanIcon
						required
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 font-oswald">
						{#if $formErrors.zipCode}
							<div class="text-fr-orange">{$formErrors.zipCode}</div>
						{/if}
					</SmallText>
				</div>
			</div>

			<Button
				class="BecomeAuthorPage__form__submit-cta"
				type="submit"
				disabled={isSubmitting === true}>Submit Form</Button
			>
			<Button
				color="Link"
				class="BecomeAuthorPage__form__clear-cta mx-0 capitalize max-w-max self-center px-1 lg:w-auto"
				disabled={!formHasValues() || isSubmitting === true}
				on:click={() => {
					clearForm();
				}}
			>
				Clear Form
			</Button>
		</form>
	</MainLayout>
</PageLayout>

<Modal
	isOpened={toggleModalOpened}
	title={`Form Submission ${$formStatus.success ? 'Successful!' : 'Failed'}`}
	on:onClose={closeModal}
	useClose={$formStatus.success ? false : true}
>
	<div class="BecomeExpertPage__notificationModal__content flex flex-col w-full justify-center" slot="content">
		<div class="flex min-h-[76px] items-center" transition:fade={{ duration: 500 }}>
			{#if $formStatus.success === true}
				<div class="h-[50px] mr-3 text-fr-royal-blue">
					<IoMdCheckmarkCircleOutline />
				</div>
				<HTMLRenderer
					content={'<div class="flex flex-col"><p>Your request was sent correctly.</p><p class="animate-pulse"> Redirecting...</p></div>'}
					class="max-w-[400px] mt-2 dark:text-fr-white mb-4"
				/>
			{:else if $formStatus.success === false}
				<div class="h-[50px] mr-3 text-fr-orange">
					<IoMdCloseCircleOutline />
				</div>
				<HTMLRenderer
					content={$formStatus.message}
					class="max-w-[400px] mt-2 dark:text-fr-white mb-4"
				/>
			{/if}
		</div>
	</div>
</Modal>

<svelte:window bind:scrollY />
