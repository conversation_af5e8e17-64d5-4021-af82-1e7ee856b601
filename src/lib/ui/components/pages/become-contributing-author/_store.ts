import { createWritablePersistStore } from '$lib/ui/state-management/store/store';
import { derived, writable } from 'svelte/store';
import {
	becomeAuthorFormInitErrors,
	becomeAuthorFormInitStatus,
	becomeAuthorFormInitValues
} from './_constants';
import type {
	TBecomeAuthorFormErrors,
	TBecomeAuthorFormStatus,
	TBecomeAuthorFormValues
} from './_types-interfaces';

//TODO: Transform this into a generic Form Store
const becomeAuthorFormValuesStore = createWritablePersistStore<TBecomeAuthorFormValues>(
	'become-author-form-values',
	becomeAuthorFormInitValues
);
const becomeAuthorFormErrorsStore = writable<TBecomeAuthorFormErrors>(becomeAuthorFormInitErrors);
const becomeAuthorFormStatusStore = writable<TBecomeAuthorFormStatus>(becomeAuthorFormInitStatus);

export const becomeAuthorFormStore = derived(
	[becomeAuthorFormValuesStore, becomeAuthorFormErrorsStore, becomeAuthorFormStatusStore],
	([$becomeAuthorFormValuesStore, $becomeAuthorFormErrorsStore]) => {
		return {
			formValues: becomeAuthorFormValuesStore,
			formHasValues: () =>
				$becomeAuthorFormValuesStore.firstName ||
				$becomeAuthorFormValuesStore.lastName ||
				$becomeAuthorFormValuesStore.email ||
				$becomeAuthorFormValuesStore.confirmEmail ||
				$becomeAuthorFormValuesStore.phone ||
				$becomeAuthorFormValuesStore.address ||
				$becomeAuthorFormValuesStore.city ||
				$becomeAuthorFormValuesStore.state ||
				$becomeAuthorFormValuesStore.zipCode ||
				$becomeAuthorFormValuesStore.expertise ||
				$becomeAuthorFormValuesStore.company ||
				$becomeAuthorFormValuesStore.position,
			formErrors: becomeAuthorFormErrorsStore,
			formHasErrors: () => {
				let formKey: keyof typeof $becomeAuthorFormErrorsStore;
				for (formKey in $becomeAuthorFormErrorsStore) {
					if (formKey !== 'form' && $becomeAuthorFormErrorsStore[formKey]) return true;
				}

				return false;
			},
			formStatus: becomeAuthorFormStatusStore
		};
	}
);
