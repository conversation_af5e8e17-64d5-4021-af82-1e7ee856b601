import type {
	TBecomeAuthorFormErrors,
	TBecomeAuthorFormStatus,
	TBecomeAuthorFormValues,
	TPosition
} from './_types-interfaces';

export const positions: TPosition[] = [
	{ title: 'Attorney', slug: 'attorney' },
	{ title: 'Paralegal', slug: 'paralegal' },
	{ title: 'Insurance Co. Rep', slug: 'insurance' },
	{ title: 'Consultant/Expert', slug: 'consultant' },
	{ title: 'Other', slug: 'other' }
];

export const becomeAuthorFormInitValues: TBecomeAuthorFormValues = {
	firstName: null,
	lastName: null,
	email: null,
	confirmEmail: null,
	phone: null,
	description: null,
	address: null,
	city: null,
	state: null,
	zipCode: null,
	company: null,
	position: null
};

export const becomeAuthorFormInitErrors: TBecomeAuthorFormErrors = {
	form: null,
	firstName: null,
	lastName: null,
	email: null,
	confirmEmail: null,
	phone: null,
	description: null,
	address: null,
	city: null,
	state: null,
	zipCode: null,
	company: null,
	position: null
};

export const becomeAuthorFormInitStatus: TBecomeAuthorFormStatus = {
	success: null
};
