import type { TBecomeAuthorFormFieldsDto } from '$lib/shared/api-dtos/internal-rest/contact-dto';

export type TPosition = {
	title: string;
	slug: string;
};

export type TBecomeAuthorFormValues = Omit<
	TBecomeAuthorFormFieldsDto,
	| 'firstName'
	| 'lastName'
	| 'email'
	| 'confirmEmail'
	| 'phone'
	| 'description'
	| 'position'
	| 'address'
> & {
	firstName: string | null;
	lastName: string | null;
	email: string | null;
	confirmEmail: string | null;
	phone: string | null;
	expertise: string | null;
	company: string | null;
	position: string | null;
	address: string | null;
};

export type TBecomeAuthorFormErrors = {
	[key in keyof TBecomeAuthorFormFieldsDto]?: string | null;
} & { form?: string | null; expertise?: string | null; };

export type TBecomeAuthorFormStatus = {
	success: boolean | null;
	message?: string | undefined;
};

export type TBecomeAuthorForm = {
	values: TBecomeAuthorFormValues;
	errors: TBecomeAuthorFormErrors;
};
