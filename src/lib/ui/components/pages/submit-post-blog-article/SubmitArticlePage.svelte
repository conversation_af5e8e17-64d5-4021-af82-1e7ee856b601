<script lang="ts">
	import { goto } from '$app/navigation';
	import { enhance } from '$app/forms';
	import type { SubmitFunction } from '@sveltejs/kit';
	import { fade } from 'svelte/transition';
	import { onMount } from 'svelte';

	import PageHeader from '../../headers/PageHeader.svelte';
	import MainLayout from '../../layouts/MainLayout.svelte';
	import PageLayout from '../../layouts/PageLayout.svelte';
	import Checkbox from '$lib/ui/components/form-elements/inputs/Checkbox/index.svelte';
	import TextField from '$lib/ui/components/form-elements/inputs/TextField/index.svelte';
	import TextArea from '$lib/ui/components/form-elements/inputs/TextArea/index.svelte';
	import Button from '../../form-elements/Button.svelte';
	import SmallText from '../../system-design/texts/SmallText.svelte';
	import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
	import SubmitArticleFormSchema from '$lib/shared/validation/schemas/contact-forms/submit-article.schema';
	import { submitArticleFormStore } from './_store';
	import Modal from '../../over-layer/Modal/index.svelte';
	import { theme } from '$lib/ui/state-management/store/theme';
	import H3 from '../../system-design/headings/H3.svelte';
	import HTMLRenderer from '../../cms-content-renderer/HtmlRenderer/index.svelte';
	import IoMdCloseCircleOutline from 'svelte-icons/io/IoMdCloseCircleOutline.svelte';
	import IoMdCheckmarkCircleOutline from 'svelte-icons/io/IoMdCheckmarkCircleOutline.svelte';

	$: ({ formValues, formErrors, formHasValues, formHasErrors, formStatus } =
		$submitArticleFormStore);
	let scrollY: any;
	let isSubmitting: boolean | null = null;
	let isVerified: boolean = false;

	export let page: TExtendedPageDto;

	const _onAcceptChange = (event: CustomEvent) => {
		const { value } = event.detail;
		$formValues.copyright = value === true ? 'on' : 'off';
	};

	const clearForm = async () => {
		if (formHasValues()) {
			const emptyFormValues = $formValues;
			let formField: keyof typeof emptyFormValues;
			for (formField in emptyFormValues) {
				$formValues = { ...$formValues, [formField]: null };
			}

			// This will remove the Query Parameters from the URL in case they exists.
			await goto('submit-post-blog-article/');
		}
	};

	const _validateFormField = (value: string, field: string) => {
		$formValues = { ...$formValues, [field]: value };
		const schemaShape = SubmitArticleFormSchema.shape;
		const parsedFieldRes =
			SubmitArticleFormSchema.shape[field as keyof typeof schemaShape].safeParse(value);

		if (!parsedFieldRes.success) {
			const errorMessage = parsedFieldRes.error.issues[0].message;
			$formErrors = { ...$formErrors, [field]: errorMessage };
		} else $formErrors = { ...$formErrors, [field]: null };
	};

	const onTextFieldPressKey = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		_validateFormField(value, fieldId);
	};

	const onCleanField = async (event: CustomEvent) => {
		const { value, fieldId } = event.detail;
		_validateFormField(value, fieldId);
	};

	const onSecuritySubmit: SubmitFunction = (formRequest: any) => {
		if (!isSubmitting) {
			isVerified = false;
			isSubmitting = true;

			return async ({ result: { data } }: any) => {
				if (!data.success) {
					$formStatus = data;
					isSubmitting = false;
				} else {
					isVerified = true;
					isSubmitting = false;
				}
			};
		}
	};

	const onSubmit: SubmitFunction = (formRequest: any) => {
		if (!isSubmitting) {
			isSubmitting = true;
			$formErrors = {};
			scrollY = 0;
			const parsedFormRes = SubmitArticleFormSchema.safeParse($formValues);
			if (!parsedFormRes.success) {
				parsedFormRes.error.issues.forEach((issue) => {
					$formErrors = { ...$formErrors, [issue.path[0]]: issue.message };
				});

				formRequest.cancel();
				$formStatus = {
					success: false,
					message: 'The form has invalid or empty values. Please check.'
				};
				isSubmitting = false;
			}

			return async ({ result: { data } }: any) => {
				if (!data.success) {
					if (data.isAValidationError) {
						$formErrors = { ...$formErrors, ...data?.errors };

						$formStatus = {
							success: false,
							message: 'The form has invalid or empty values. Please check.'
						};
					} else if (data?.isUserError) {
						$formStatus = {
							success: false,
							message: data.message
						};
					} else {
						$formStatus = {
							success: false,
							message: 'Your request could not be processed, please try again later'
						};
					}
					isSubmitting = false;
				} else {
					await goto('/thank-you-for-your-blog-submission');
				}
			};
		}
	};

	const closeFormNotification = () => {
		$formStatus = { success: null };
	};

	onMount(() => {
		$submitArticleFormStore.formValues.useLocalStorage();
	});
</script>

<!-- svelte-ignore missing-declaration -->
<PageLayout class="SubmitArticlePage flex flex-col h-full">
	<PageHeader
		title={page?.title ?? ''}
		description={page?.description ?? ''}
		class="SubmitArticlePage__header text-center"
	/>

	<MainLayout>
		{#if isVerified}
			<form
				method="POST"
				action="?/article"
				use:enhance={onSubmit}
				class={`SubmitArticlePage__form flex flex-col gap-10 ${
					isSubmitting ? 'animate-pulse' : ''
				}`}
			>
				<!-- NAME  fields -->
				<div class="SubmitArticlePage__form__row-container flex flex-col gap-5 lg:flex-row">
					<div class="SubmitArticlePage__form__field-container text-left w-full lg:w-1/2">
						<TextField
							id="firstName"
							name="firstName"
							label="First Name"
							value={$formValues.firstName}
							useCleanIcon
							required
							on:onKeyUp={onTextFieldPressKey}
							on:onCleanField={onCleanField}
						/>
						<SmallText class="mt-1 h-4 text-fr-orange font-oswald dark:text-fr-orange">
							{#if $formErrors.firstName}
								<div class="dark:text-fr-orange">{$formErrors.firstName}</div>
							{/if}
						</SmallText>
					</div>
					<div class="SubmitArticlePage__form__field-container text-left w-full lg:w-1/2">
						<TextField
							id="lastName"
							name="lastName"
							label="Last Name"
							value={$formValues.lastName}
							useCleanIcon
							required
							on:onKeyUp={onTextFieldPressKey}
							on:onCleanField={onCleanField}
						/>
						<SmallText class="mt-1 h-4 text-fr-orange font-oswald dark:text-fr-orange">
							{#if $formErrors.lastName}
								<div class="dark:text-fr-orange">{$formErrors.lastName}</div>
							{/if}
						</SmallText>
					</div>
				</div>

				<!-- EMAIL  fields -->
				<div class="SubmitArticlePage__form__row-container flex flex-col gap-5 lg:flex-row">
					<div class="SubmitArticlePage__form__field-container text-left w-full lg:w-1/2">
						<TextField
							id="email"
							name="email"
							label="Email"
							value={$formValues.email}
							useCleanIcon
							required
							on:onKeyUp={onTextFieldPressKey}
							on:onCleanField={onCleanField}
						/>
						<SmallText class="mt-1 h-4 text-fr-orange font-oswald dark:text-fr-orange">
							{#if $formErrors.email}
								<div class="dark:text-fr-orange">{$formErrors.email}</div>
							{/if}
						</SmallText>
					</div>
					<div class="SubmitArticlePage__form__field-container text-left w-full lg:w-1/2">
						<TextField
							id="confirmEmail"
							name="confirmEmail"
							label="Confirm Email"
							value={$formValues.confirmEmail}
							useCleanIcon
							required
							on:onKeyUp={onTextFieldPressKey}
							on:onCleanField={onCleanField}
						/>
						<SmallText class="mt-1 h-4 text-fr-orange font-oswald dark:text-fr-orange">
							{#if $formErrors.confirmEmail}
								<div class="dark:text-fr-orange">{$formErrors.confirmEmail}</div>
							{/if}
						</SmallText>
					</div>
				</div>

				<!-- TITLE -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="title" class=" flex items-baseline dark:text-fr-white">
						Title of Article
					</label>
					<TextField
						id="title"
						name="title"
						value={$formValues.title}
						useCleanIcon
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
				</div>

				<!-- Excerpt -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="excerpt" class=" flex items-baseline dark:text-fr-white">Excerpt</label>
					<p class="my-1 text-sm">
						Write a concise one to three sentence excerpt of your article that answers the question
						"who should read this article and what major benefit will they get from it if they do?"
					</p>
					<TextArea
						id="excerpt"
						name="excerpt"
						useCleanIcon
						value={$formValues.excerpt}
						rows={5}
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
				</div>

				<!-- Contents -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="content" class=" flex items-baseline dark:text-fr-white"
						>Table of Contents</label
					>
					<p class="my-1 text-sm">
						Outline your article in the creation phase that uses headings as sections so scan
						readers can "jump" or "scan" the sections and read those sections they are most
						interested in.
					</p>
					<TextArea
						id="content"
						name="content"
						useCleanIcon
						value={$formValues.content}
						rows={5}
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
				</div>

				<!-- Body 1 -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="body1" class=" flex items-baseline dark:text-fr-white">Body 1</label>
					<p class="my-1 text-sm">
						Use the body of your article to demonstrate your expertise in the subject area, not to
						"sell" your services, broken up by topics that are the Table of Content. Show potential
						clients how you have solved problems like theirs, or what your company represents
						instead.
					</p>
					<TextArea
						id="body1"
						name="body1"
						useCleanIcon
						value={$formValues.body1}
						rows={5}
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
				</div>

				<!-- Body 2 -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="body2" class=" flex items-baseline dark:text-fr-white">Body 2</label>
					<p class="my-1 text-sm">Use this section if you run out room in Body 1</p>
					<TextArea
						id="body2"
						name="body2"
						useCleanIcon
						value={$formValues.body2}
						rows={5}
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
				</div>

				<!-- Body 3 -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="body3" class=" flex items-baseline dark:text-fr-white">Body 3</label>
					<p class="my-1 text-sm">Use this section if you run out room in Body 2</p>
					<TextArea
						id="body3"
						name="body3"
						useCleanIcon
						value={$formValues.body3}
						rows={5}
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
				</div>

				<!-- Conclusion -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="conclusion" class=" flex items-baseline dark:text-fr-white">Conclusion</label>
					<p class="my-1 text-sm">
						Summarize your point of view and opinion and restate your excerpt or introduction in the
						beginning.
					</p>
					<TextArea
						id="conclusion"
						name="conclusion"
						useCleanIcon
						value={$formValues.conclusion}
						rows={5}
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
				</div>

				<!-- Canonical Tag -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="canonicalTag" class=" flex items-baseline dark:text-fr-white"
						>Canonical Tag</label
					>
					<p class="my-1 text-sm">
						If your articles are already published on the Internet (on your own website for
						example), we will add what is called a Canonical Tag. It will tell search engines that
						the article is a duplicate and the copyright of the article belongs to the site linked
						by the canonical tag. You will still benefit fromFreeReferral.com quality traffic and
						will retain the original copyright of your work. You will be able to add the Canonical
						Tag when submitting your article from your FreeRefferral.com account. However, we
						strongly encourage you to post only original content as Google devalues content that is
						duplicated elsewhere on the internet as evidenced by their many public statements and
						many algorithm updates. For this reason, for maximum benefit and impact, we recommend
						submitting only original content and not duplicating content from elsewhere. Promoting
						content on social media that was posted on a website is considering promotion and not
						duplication. If you are unsure if a piece of content is duplicate, reach out to us and
						we can discuss this together.
					</p>
					<TextField
						id="canonicalTag"
						name="canonicalTag"
						value={$formValues.canonicalTag}
						placeholder="https://"
						useCleanIcon
						on:onKeyUp={onTextFieldPressKey}
						on:onCleanField={onCleanField}
					/>
					<SmallText class="mt-1 h-4 text-fr-orange font-oswald dark:text-fr-orange">
						{#if $formErrors.canonicalTag}
							<div class="dark:text-fr-orange">{$formErrors.canonicalTag}</div>
						{/if}
					</SmallText>
				</div>

				<!-- Copyright -->
				<div class="SubmitArticlePage__form__field-container text-left">
					<label for="copyright" class=" flex items-baseline dark:text-fr-white"
						>Copyright of My Article</label
					>
					<p class="my-1 text-sm">
						By submitting an article you acknowledge that you retain full copyright to your work and
						you agree that FreeReferral.com and Consolidated Consultants Company may feature it on
						our Website and other marketing channels as long as we provide you with proper credit.
					</p>

					<Checkbox
						label="Accept"
						value={$formValues.copyright === 'on'}
						id="copyright"
						on:onChange={_onAcceptChange}
					/>

					<SmallText class="mt-1 h-4 text-fr-orange font-oswald dark:text-fr-orange">
						{#if $formErrors.copyright}
							<div class="dark:text-fr-orange">{$formErrors.copyright}</div>
						{/if}
					</SmallText>
				</div>

				<Button
					class="SubmitArticlePage__form__submit-cta"
					type="submit"
					disabled={isSubmitting === true}>Submit Form</Button
				>
				<Button
					color="Link"
					class="SubmitArticlePage__form__clear-cta mx-0 capitalize max-w-max self-center px-1 lg:w-auto"
					disabled={!formHasValues() || isSubmitting === true}
					on:click={() => {
						clearForm();
					}}
				>
					Clear Form
				</Button>
			</form>
		{:else}
			<form
				method="POST"
				action="?/verify"
				use:enhance={onSecuritySubmit}
				class={`SubmitArticlePage__form flex flex-col gap-2 ${isSubmitting ? 'animate-pulse' : ''}`}
			>
				<TextField
					id="email"
					name="email"
					label="Email"
					value={$formValues.email}
					useCleanIcon
					required
					on:onKeyUp={onTextFieldPressKey}
					on:onCleanField={onCleanField}
				/>

				<Button
					class="SubmitArticlePage__form__submit-cta"
					type="submit"
					disabled={isSubmitting === true}>Verify Author</Button
				>
			</form>
		{/if}
	</MainLayout>
</PageLayout>

<Modal
	isOpened={$formStatus.success !== null}
	class="SubmitArticlePage__notificationModal border-none flex flex-col justify-center"
	style={`height: 100%; width: 100%; background:${
		!$theme
			? 'radial-gradient(circle at center, rgba(255, 255, 255, 1) 60%, rgba(255, 255, 255, 0) 80%)'
			: 'radial-gradient(circle at center, rgba(30, 30, 30, 1) 70%, rgba(30, 30, 30, 0) 80%)'
	}`}
	useClose={false}
>
	<div
		class="SubmitArticlePage__notificationModal__content flex flex-col w-full justify-center"
		slot="bodyContent"
	>
		{#if $formStatus.success === true}
			<div
				class="flex flex-col min-h-[76px] justify-center items-center"
				transition:fade={{ duration: 500 }}
			>
				<div class="h-[10%] min-h-[76px] text-fr-royal-blue">
					<IoMdCheckmarkCircleOutline />
				</div>
				<H3 class="w-full text-center m-0 dark:text-fr-charcoal"
					>{'Your request was sent correctly.'}</H3
				>
				<HTMLRenderer
					content={$formStatus.message}
					class="max-w-[400px] mt-2 dark:text-fr-white mb-4"
				/>
				<Button
					color="Primary"
					class="h-[46px] px-1 capitalize self-center"
					style="width: 144px"
					type="button"
					on:click={closeFormNotification}
				>
					Close
				</Button>
			</div>
		{:else if $formStatus.success === false}
			<div class="flex flex-col min-h-[76px] justify-center" transition:fade={{ duration: 500 }}>
				<div class="h-[50px] text-fr-orange">
					<IoMdCloseCircleOutline />
				</div>
				<H3 class="w-full text-center m-0 dark:text-fr-charcoal mb-4">{$formStatus.message}</H3>
				<Button
					color="Outlined"
					class="h-[46px] px-1 capitalize self-center"
					style="width: 144px"
					type="button"
					on:click={closeFormNotification}
				>
					Close
				</Button>
			</div>
		{/if}
	</div>
</Modal>

<svelte:window bind:scrollY />
