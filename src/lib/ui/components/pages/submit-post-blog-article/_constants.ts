import type {
	TSubmitArticleFormErrors,
	TSubmitArticleFormStatus,
	TSubmitArticleFormValues
} from './_types-interfaces';

export const submitArticleFormInitValues: TSubmitArticleFormValues = {
	firstName: null,
	lastName: null,
	email: null,
	confirmEmail: null,
	title: null,
	excerpt: null,
	content: null,
	body1: null,
	body2: null,
	body3: null,
	conclusion: null,
	canonicalTag: null,
	copyright: ''
};

export const submitArticleFormInitErrors: TSubmitArticleFormErrors = {
	form: null,
	firstName: null,
	lastName: null,
	email: null,
	confirmEmail: null,
	title: null,
	excerpt: null,
	content: null,
	body1: null,
	body2: null,
	body3: null,
	conclusion: null,
	canonicalTag: null,
	copyright: null
};

export const submitArticleFormInitStatus: TSubmitArticleFormStatus = {
	success: null
};
