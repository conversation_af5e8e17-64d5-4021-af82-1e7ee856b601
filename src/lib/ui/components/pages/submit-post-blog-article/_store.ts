import { createWritablePersistStore } from '$lib/ui/state-management/store/store';
import { derived, writable } from 'svelte/store';
import {
	submitArticleFormInitErrors,
	submitArticleFormInitStatus,
	submitArticleFormInitValues
} from './_constants';
import type {
	TSubmitArticleFormErrors,
	TSubmitArticleFormStatus,
	TSubmitArticleFormValues
} from './_types-interfaces';

//TODO: Transform this into a generic Form Store
const submitArticleFormValuesStore = createWritablePersistStore<TSubmitArticleFormValues>(
	'submit-article-form-values',
	submitArticleFormInitValues
);
const submitArticleFormErrorsStore = writable<TSubmitArticleFormErrors>(
	submitArticleFormInitErrors
);
const submitArticleFormStatusStore = writable<TSubmitArticleFormStatus>(
	submitArticleFormInitStatus
);

export const submitArticleFormStore = derived(
	[submitArticleFormValuesStore, submitArticleFormErrorsStore, submitArticleFormStatusStore],
	([$submitArticleFormValuesStore, $submitArticleFormErrorsStore]) => {
		return {
			formValues: submitArticleFormValuesStore,
			formHasValues: () =>
				$submitArticleFormValuesStore.firstName ||
				$submitArticleFormValuesStore.lastName ||
				$submitArticleFormValuesStore.email ||
				$submitArticleFormValuesStore.confirmEmail ||
				$submitArticleFormValuesStore.title ||
				$submitArticleFormValuesStore.excerpt ||
				$submitArticleFormValuesStore.content ||
				$submitArticleFormValuesStore.body1 ||
				$submitArticleFormValuesStore.body2 ||
				$submitArticleFormValuesStore.body3 ||
				$submitArticleFormValuesStore.conclusion ||
				$submitArticleFormValuesStore.canonicalTag ||
				$submitArticleFormValuesStore.copyright,
			formErrors: submitArticleFormErrorsStore,
			formHasErrors: () => {
				let formKey: keyof typeof $submitArticleFormErrorsStore;
				for (formKey in $submitArticleFormErrorsStore) {
					if (formKey !== 'form' && $submitArticleFormErrorsStore[formKey]) return true;
				}

				return false;
			},
			formStatus: submitArticleFormStatusStore
		};
	}
);
