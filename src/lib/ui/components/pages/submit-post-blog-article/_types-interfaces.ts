import type { TSubmitArticleFormFieldsDto } from '$lib/shared/api-dtos/internal-rest/contact-dto';

export type TSubmitArticleFormValues = Omit<
	TSubmitArticleFormFieldsDto,
	| 'firstName'
	| 'lastName'
	| 'email'
	| 'confirmEmail'
	| 'phone'
	| 'address'
	| 'city'
	| 'state'
	| 'zipCode'
	| 'description'
> & {
	firstName: string | null;
	lastName: string | null;
	email: string | null;
	confirmEmail: string | null;
	title: string | null;
	excerpt: string | null;
	content: string | null;
	body1: string | null;
	body2: string | null;
	body3: string | null;
	conclusion: string | null;
	canonicalTag: string | null;
	copyright: string | null;
};

export type TSubmitArticleFormErrors = {
	[key in keyof TSubmitArticleFormFieldsDto]?: string | null;
} & { form?: string | null };

export type TSubmitArticleFormStatus = {
	success: boolean | null;
	message?: string | undefined;
};

export type TSubmitArticleForm = {
	values: TSubmitArticleFormValues;
	errors: TSubmitArticleFormErrors;
};
