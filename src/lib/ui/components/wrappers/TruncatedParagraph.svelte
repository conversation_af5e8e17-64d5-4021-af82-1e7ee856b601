<script lang="ts">
	  import { onMount } from "svelte";
	  import { fade, slide } from 'svelte/transition';
    import { quartOut } from 'svelte/easing';

	  import Button from "../form-elements/Button.svelte";
    
    export let lines: number = 3;
    let expanded: boolean = false;
    let isTruncated: boolean = false;
    let paragraphElement: HTMLParagraphElement;
    let lineHeight = 0;

    $: contentStyle = !isTruncated 
    ? '' 
    : `display: -webkit-box; -webkit-line-clamp: ${!expanded ? lines: 'unset'}; -webkit-box-orient: vertical; overflow: hidden;`;
  
    $: gradientStyle = `height: ${lineHeight}px; bottom: 1px;`;
  
    function checkOverflow() {
      lineHeight = parseFloat(getComputedStyle(paragraphElement).lineHeight);
      const maxHeight = lineHeight * lines;
      isTruncated = paragraphElement.scrollHeight > maxHeight;
    }
  
    function toggleExpanded() {
      expanded = !expanded;
    }

    onMount(() => {
      checkOverflow();
      lineHeight = parseInt(getComputedStyle(paragraphElement).lineHeight);
    });
</script>
  
<div class={`TruncatedParagraph w-full relative mb-5 ${$$props.class}`}
     transition:fade
>
  <p 
    bind:this={paragraphElement} 
    style={contentStyle}
    class="truncated text-fr-charcoal dark:text-fr-white"
  >
    <slot/>
  </p>

  {#if isTruncated}
    {#if !expanded}
      <div 
        class="TruncatedParagraph__fade gradient 
              bg-gradient-to-r from-transparent to-fr-white to-50% 
              dark:text-fr-royal-blue dark:bg-gradient-to-r dark:from-transparent 
              dark:to-fr-charcoal dark:to-50%
        "
        style={gradientStyle}
      />
    {/if}
    <Button 
      class={`TruncatedParagraph__cta--${!expanded ? 'see-more' : 'see-less'} text-fr-royal-blue dark:text-fr-royal-blue`}
      on:click={toggleExpanded}
      color="Link"
    >
      {#if !expanded}...Show More{:else}Show Less{/if}
    </Button>
  {/if}
</div>

  <style>
    .truncated {
        line-height: 1.2em
    }
    .gradient {
      position: absolute;
      right: 0;
      width: 150px;
      pointer-events: none;
    }
    .gradient-fade-charcoal {
      background: linear-gradient(to right, transparent, #1E1E1E 50%);
    }
    .gradient-fade-white {
      background: linear-gradient(to right, transparent, #FFFFFF 50%);
    }
    :global(.TruncatedParagraph__cta--see-more) {
      position: absolute;
      right: 0;
      bottom: 1px;
      line-height: 1.2em;
      height: auto !important;
    }
    :global(.TruncatedParagraph__cta--see-less) {
      line-height: 1.2em;
      height: auto;
    }
  </style>
  