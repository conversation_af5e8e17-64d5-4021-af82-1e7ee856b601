<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import IoIosClose from 'svelte-icons/io/IoIosClose.svelte';
	import IoIosArrowDown from 'svelte-icons/io/IoIosArrowDown.svelte'

	import type { TSelectInputOption } from './_types-interfaces';

	const dispatch = createEventDispatcher<any>();
	
	export let options: TSelectInputOption[];
	export let value: TSelectInputOption['title'] | null = null;
	export let placeholder: string = 'Select an option';
	export let label: string | null = null;
	export let disabled: boolean = false;

	const _onChange = (event: Event) => {
		dispatch('onChange', {value, fieldId: $$props.id}, { cancelable: true });
	};

	const _cleanField = (event: Event) => {
		value = null;
		dispatch('onCleanField', {value, fieldId: $$props.id}, { cancelable: true });
	};
</script>

{#if label}
	<label for={$$props.id} class="text-fr-charcoal dark:text-fr-white">
		{label}
	</label>
{/if}
<div
	class={`Select__container 
		block relative h-10 rounded-[0.3125rem] 
		border-2 
		${$$props.disabled ? 'border-gray-300 text-gray-400' : 'border-fr-charcoal'} 
		bg-fr-white 
		dark:border-fr-white dark:bg-fr-charcoal dark:text-fr-white 
		${$$props.class}
	`}
>
	<!--<select
		class={`Select appearance-none outline-none shadow-none flex-1 pl-2 w-full h-full pr-10 absolute z-[2] bg-transparent dark:bg-fr-charcoal`}
		id={$$props.id}
		name={$$props.name}
		disabled={$$props.disabled}
		bind:value
		on:change={_onChange}
	>
		<option 
			value={null}
			disabled
			selected={!value}
		>
			{placeholder}
		</option>
		{#each options as {title}}
			<option value={title} selected={value === title}>{title}</option>
		{/each}
	</select>-->
	{#if !value}
		<div class={`w-10 h-9 dark:text-fr-white absolute z-0 right-0 top-0`}>
			<IoIosArrowDown />
		</div>
	{/if}
	{#if value}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div class={`w-10 h-9 text-fr-royal-blue cursor-pointer absolute z-[3] right-0 top-0`} on:click={_cleanField}>
			<IoIosClose />
		</div>
	{/if}
</div>
