<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import type { TSelectInputOption } from '../MultipleSelect/_types-interfaces';
	import IoIosArrowDown from 'svelte-icons/io/IoIosArrowDown.svelte';
	import IoIosClose from 'svelte-icons/io/IoIosClose.svelte';
	import IoIosArrowUp from 'svelte-icons/io/IoIosArrowUp.svelte';

	const dispatch = createEventDispatcher<any>();

	export let options: TSelectInputOption[];
	export let placeholder: string = 'Type a suggestion or select an option';
	export let label: string | null = null;
	export let value: TSelectInputOption['title'] | null = null;
	export let openOnFocus: boolean = false;
	
	let filteredOptions: TSelectInputOption[] = [];
	let isOpen = false;
	let timer: any;

	$: {filteredOptions = options}
  
	const filterValue = () => {
	  if (options[0] && value) {
		filteredOptions = options.filter(item => item.title.toLowerCase().includes(value!.toLowerCase()));
		if (filteredOptions[0] && filteredOptions[0].title.toLowerCase() === value.toLowerCase()) {
			isOpen = false;
			_onChange();
		}
	  } else if (!value) _cleanField();
	};
  
	const handleSelect = (selectedValue: TSelectInputOption | null) => {
		value = selectedValue?.title ?? null;
		isOpen = false;
	  	_onChange();
	};
  
	const _onChange = () => {
		dispatch('onChange', {value, fieldId: $$props.id}, { cancelable: true });
	};

	const _cleanField = () => {
		value = null;
		filteredOptions = options;
		isOpen = false;
		dispatch('onChange', {value, fieldId: $$props.id}, { cancelable: true });
		dispatch('onCleanField', {value, fieldId: $$props.id}, { cancelable: true });
	};

	const _onKeyUp = (event: KeyboardEvent) => {
		const {key} = event;
		let _shouldFilterValue = false;
		switch(key) {
			case 'Enter':
				if(value) _shouldFilterValue = true;
			break;
			case 'Escape':
				_cleanField();
			break;
			default:
				_shouldFilterValue = true;
		}
		if(_shouldFilterValue) {
			if(!isOpen) isOpen = true;
			clearTimeout(timer);
			timer = setTimeout(filterValue, 500);
		}
	};

	const _checkOpenOnFocus = () => {
		if(openOnFocus && !isOpen) isOpen = true;
	};
  </script>
  
{#if label}
	<label for={$$props.id} class="text-fr-charcoal dark:text-fr-white">
		{label}
	</label>
{/if}
<div
	class={`SelectAutcomplete bg-fr-white block 
		relative border-[2px] h-10 rounded-[0.3125rem] 
		w-full my-4 mt-2 dark:bg-fr-charcoal
		${$$props.disabled ? 'border-gray-300 text-gray-300' : 'border-fr-charcoal dark:border-fr-white'}
		${$$props.class}`
	}
>
	<input 
		type="text"
		{placeholder}
		bind:value
		on:keyup={_onKeyUp}
		on:focus={_checkOpenOnFocus}
		class={`SelectAutcomplete__input 
			w-full pl-2 pr-4 h-9 bg-inherit 
			font-oswald ${$$props.disabled ? 'text-gray-300' : 'text-fr-charcoal dark:text-fr-white'}`
		}
		id={$$props.id}
		name={$$props.name}
		disabled={$$props.disabled}
	/>
	{#if !value && !isOpen}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div class={`w-10 h-9 dark:text-fr-white absolute z-0 right-0 top-0`} on:click={()=>{isOpen = !isOpen;}}>
			<IoIosArrowDown />
		</div>
	{:else if !value && isOpen}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div class={`w-10 h-9 dark:text-fr-white absolute z-0 right-0 top-0`} on:click={()=>{isOpen = !isOpen;}}>
			<IoIosArrowUp />
		</div>
	{:else}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div class={`w-10 h-9 text-fr-royal-blue cursor-pointer absolute z-[3] right-0 top-0`} on:click={_cleanField}>
			<IoIosClose />
		</div>
	{/if}
	{#if isOpen && !$$props.disabled}
		<ul class="SelectAutcomplete__menu 
			block absolute z-10 bg-fr-white w-full 
			text-fr-charcoal border-[2px] 
			border-fr-charcoal dark:border-fr-white 
			dark:bg-fr-charcoal dark:text-fr-white
			max-h-[350px] overflow-y-scroll"
		>	
			{#each filteredOptions as option}
				<!-- svelte-ignore a11y-click-events-have-key-events -->
				<li 
					on:click={() => handleSelect(option)}
					class="SelectAutcomplete__menu__item p-1 pointer hover:text-fr-royal-blue hover:cursor-pointer font-oswald"
				>
					{option.title}
				</li>
			{/each}
		</ul>
	{/if}
</div>
  