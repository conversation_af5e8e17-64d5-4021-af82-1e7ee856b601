<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import IoIosSearch from 'svelte-icons/io/IoIosSearch.svelte';

	import CleanIcon from '../../../system-design/icons/CleanIcon.svelte';
	import type { TFieldTextInputEventDispatcher } from './_types-interfaces';
	import SmallText from '$lib/ui/components/system-design/texts/SmallText.svelte';
	type inputType = 'text' | 'email' | 'number';

	const dispatch = createEventDispatcher<TFieldTextInputEventDispatcher>();
	let inputRef: HTMLInputElement;
	let timer: any;

	export let useSearchIcon: boolean = false;
	export let useCleanIcon: boolean = false;
	export let placeholder: string = '';
	export let noDarkMode: boolean = false;
	export let value: string | null = null;
	export let type: inputType = 'text';
	export let label: string | null = null;
	export let required: boolean | null = false;
	export let disabled: boolean | null = false;
	export let hidden: boolean | null = false;
	export let requireValueOnEnter: boolean | null = true;

	// Handlers for emitting events
	const _onKeyUp = (event: KeyboardEvent) => {
		let dispatchEvent = false;
		const {key} = event;

		if (key) {
			switch(key.toLowerCase()) {
				case 'escape':
				_cleanField();
				break;
				case 'enter':
					if(!requireValueOnEnter || (requireValueOnEnter && value)) dispatchEvent = true;
				break;
				default: // Any other Key
					dispatchEvent = true;
			}
		} else // If no key is pressed(as it happens when the user selected browser preloaded values such as email values), then we dispatch the event anyway.
			dispatchEvent = true;

		if(dispatchEvent)
			dispatch('onKeyUp', { value, keyCode: event.code, fieldId: $$props.id }, { cancelable: true });
	};

	const _cleanField = () => {
		value = null;
		dispatch('onCleanField', { value, fieldId: $$props.id }, { cancelable: true });
	};

	onMount(() => {
		if (inputRef) {
			inputRef.type = type;
		}
	});
</script>

{#if label && !hidden}
	<label for={$$props.id} class="text-fr-charcoal flex items-baseline dark:text-fr-white">
		{label}
		{#if required}
			<SmallText class="ml-1">(*)</SmallText>
		{/if}
	</label>
{/if}
<div
	class={`TextField__container${disabled ? '--disabled' : ''} flex items-center relative rounded-[0.3125rem] border-2 border-fr-charcoal bg-fr-white ${
		!noDarkMode ? 'dark:border-fr-white dark:bg-fr-charcoal dark:text-fr-white' : ''
	} ${$$props.class} ${disabled ? 'border-gray-500 bg-gray-200' : ''}`}
>
	{#if useSearchIcon && !hidden}
		<div class={`TextField__searchIcon w-9 bg-transparent`}>
			<IoIosSearch />
		</div>
	{/if}
	<input
		hidden={hidden}
		bind:this={inputRef}
		id={$$props.id}
		name={$$props.name}
		class={`TextField ${
			useSearchIcon
				? `outline-none ${!noDarkMode ? 'dark:bg-fr-charcoal dark:text-fr-white' : 'dark:bg-fr-white dark:text-fr-charcoal'} pl-2 ${!useCleanIcon ? 'pr-4' : 'pr-0'} h-10 w-full`
				: `outline-none ${!noDarkMode ? 'dark:bg-fr-charcoal dark:text-fr-white' : 'dark:bg-fr-white dark:text-fr-charcoal'} px-4 h-10 w-full`}`}
		disabled={disabled}
		{placeholder}
		maxlength={$$props.maxlength}
		bind:value
		on:keyup={_onKeyUp}
	/>
	{#if useCleanIcon && value && !disabled}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div on:click={_cleanField} class="cursor-pointer">
			<CleanIcon class={'text-fr-royal-blue'} />
		</div>
	{/if}
</div>
