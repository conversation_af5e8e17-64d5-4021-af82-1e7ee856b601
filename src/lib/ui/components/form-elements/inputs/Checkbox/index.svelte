<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import IoIosCheckmark from 'svelte-icons/io/IoIosCheckmark.svelte';

	const dispatch = createEventDispatcher<any>();

	export let value: boolean = false;
	export let label: string | null = null;
	export let disabled: boolean = false;
	export let id: string;

	const _onChange = () => {
		if (!disabled) {
			value = !value;
			dispatch('onChange', { value, fieldId: id }, { cancelable: true });
		}
	};
</script>

<!-- svelte-ignore a11y-click-events-have-key-events -->
<div
	class={`Checkbox flex cursor-pointer items-center ${$$props.class}`}
	aria-checked={value}
	aria-disabled={disabled}
>
	{#if value && !disabled}
		<div
			class="absolute mt-[-5px] ml-[-12px] h-[44px] w-[44px] text-fr-royal-blue"
			on:click={_onChange}
		>
			<IoIosCheckmark />
		</div>
	{/if}

	<div
		class={`Checkbox_container 
			flex w-[16px] h-[16px] border-[2px] 
			${disabled ? 'border-gray-300 text-gray-400' : 'border-fr-charcoal dark:border-fr-white'} 
			mr-2`}
		on:click={_onChange}
	>
		<input
			class="Checkbox_container_input"
			type="checkbox"
			bind:checked={value}
			{id}
			name={id}
			hidden
		/>
	</div>
	<label
		class={`Checkbox_label ${disabled ? 'text-gray-400' : 'text-fr-charcoal dark:text-fr-white'}`}
		for={id}>{label}</label
	>
</div>
