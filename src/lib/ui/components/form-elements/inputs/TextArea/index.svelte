<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import IoIosSearch from 'svelte-icons/io/IoIosSearch.svelte';

	import CleanIcon from '../../../system-design/icons/CleanIcon.svelte';
	import type { TFieldTextInputEventDispatcher } from './_types-interfaces';
	import SmallText from '$lib/ui/components/system-design/texts/SmallText.svelte';

	const dispatch = createEventDispatcher<TFieldTextInputEventDispatcher>();
	let inputRef: HTMLInputElement;

	export let useSearchIcon: boolean = false;
	export let useCleanIcon: boolean = false;
	export let placeholder: string = '';
	export let noDarkMode: boolean = false;
	export let value: string | null = null;
	export let label: string | null = null;
	export let required: boolean | null = false;
	export let disabled: boolean | null = false;
	export let hidden: boolean | null = false;
	export let rows: number | null = 5;

	// Handlers for emitting events
	const _onKeyUp = (event: KeyboardEvent) => {
		dispatch('onKeyUp', { value, keyCode: event.code, fieldId: $$props.id }, { cancelable: true });
	};

	const cleanValue = () => {
		value = null;
		dispatch('onCleanField', { value, fieldId: $$props.id }, { cancelable: true });
	};
</script>

{#if label && !hidden}
	<label for={$$props.id} class="text-fr-charcoal flex items-baseline dark:text-fr-white">
		{label}
		{#if required}
			<SmallText class="ml-1">(*)</SmallText>
		{/if}
	</label>
{/if}

<div
	class={`TextArea__container${
		disabled ? '--disabled' : ''
	} flex items-center relative rounded-[0.3125rem] border-2 border-fr-charcoal bg-fr-white ${
		!noDarkMode ? 'dark:border-fr-white dark:bg-fr-charcoal dark:text-fr-white' : ''
	} ${$$props.class} ${disabled ? 'border-gray-500 bg-gray-200' : ''}`}
>
	{#if useSearchIcon && !hidden}
		<div class={`TextArea__searchIcon w-9 bg-transparent`}>
			<IoIosSearch />
		</div>
	{/if}
	<textarea
		{hidden}
		bind:this={inputRef}
		id={$$props.id}
		name={$$props.name}
		{rows}
		class={`TextArea ${
			useSearchIcon
				? `outline-none ${
						!noDarkMode
							? 'dark:bg-fr-charcoal dark:text-fr-white'
							: 'dark:bg-fr-white dark:text-fr-charcoal'
				  } pl-4 ${!useCleanIcon ? 'pr-4' : 'pr-0'} w-full`
				: `outline-none ${
						!noDarkMode
							? 'dark:bg-fr-charcoal dark:text-fr-white'
							: 'dark:bg-fr-white dark:text-fr-charcoal'
				  } px-4 w-full resize-none`
		}`}
		{disabled}
		{placeholder}
		bind:value
		on:keyup={_onKeyUp}
	/>
	{#if useCleanIcon && value && !disabled}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<div on:click={cleanValue} class="cursor-pointer">
			<CleanIcon class={'text-fr-royal-blue'} />
		</div>
	{/if}
</div>
