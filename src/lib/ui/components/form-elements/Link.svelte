<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	type TColor = 'default' | 'primary' | 'secondary';
	type TUIType = 'link' | 'button';

	export let color: TColor = 'default';
	export let uiType: TUIType = 'link';
	export let disabled = false;
	export let linkUrl = '/';
	export let linkTarget = '_self';
	export let activePrefetch: 'hover' | 'off' = 'hover';

	const dispatch = createEventDispatcher();
	const _linkBaseClass = 'Link__Anchor border-0 h-10 bg-transparent underline w-auto';
	const _buttonBaseClass =
		'Link__Button rounded-[0.3125rem] border-2 h-10 w-full flex justify-center items-center';

	// TODO: Check how to this inside a block (multiple reactive statements)
	$: _buttonEnabledDisabledClass = !disabled
		? `${
				color === 'primary'
					? 'border-fr-royal-blue bg-fr-royal-blue text-fr-white'
					: color === 'secondary'
					? 'border-fr-navy bg-fr-navy text-fr-white dark:bg-fr-white dark:text-fr-charcoal'
					: ''
		  }`
		: 'text-gray-500';
	$: _linkEnabledDisabledClass = !disabled
		? `${
				color === 'primary'
					? 'text-fr-charcoal hover:text-fr-royal-blue dark:text-fr-white dark:hover:text-fr-royal-blue'
					: color === 'secondary'
					? 'text-fr-navy'
					: ''
		  }`
		: 'text-gray-500';

	const handleClick = () => {
		if (!disabled) dispatch('click');
	};
</script>

{#if uiType === 'link' && !disabled}
	<a 
		class={`${$$props.class} ${_linkBaseClass} ${_linkEnabledDisabledClass}`} href={linkUrl}
		style={`${$$props.style}`}
		target={linkTarget}
		data-sveltekit-preload-data={activePrefetch}
	>
		<slot />
	</a>
{:else if uiType === 'link'}
	<div class={`${_linkBaseClass} ${_linkEnabledDisabledClass} ${$$props.class}`}>
		<slot />
	</div>
{/if}

{#if uiType === 'button'}
	<a
		class={`${_buttonBaseClass} ${_buttonEnabledDisabledClass} ${$$props.class}`}
		href={linkUrl}
		on:click={handleClick}
	>
		<slot />
	</a>
{/if}

<style>
	:global(.Link__Anchor, .Link__Button) {
		font-family: 'Oswald', sans-serif;
		font-weight: 500;
		text-transform: capitalize;
	}
</style>
