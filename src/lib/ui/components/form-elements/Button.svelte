<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	type colors = 'Primary' | 'Secondary' | 'White' | 'Outlined' | 'Link';
	type buttonType = 'submit' | 'button';

	export let color: colors = 'Primary';
	export let disabled = false;
	export let type: buttonType = 'button';

	const dispatch = createEventDispatcher();
	const _buttonBaseClass = 'Button rounded-[0.3125rem] border-2 h-10 w-full';

	// TODO: Check how to this inside a block (multiple reactive statements)
	$: _primaryEnabledDisabledClass = !disabled
		? 'border-fr-royal-blue bg-fr-royal-blue text-fr-white'
		: 'cursor-auto text-gray-400 border-gray-400';
	$: _secondaryEnabledDisabledClass = !disabled ? 'border-fr-navy bg-fr-navy text-fr-white dark:bg-fr-white dark:text-fr-charcoal cursor-pointer' : '';
	$: _whiteEnabledDisabledClass = !disabled
		? 'border-fr-white bg-fr-white text-fr-navy dark:text-fr-white'
		: 'cursor-auto';
	$: _outlinedEnabledDisabledClass = !disabled
		? 'border-fr-navy bg-fr-white text-fr-navy dark:bg-inherit dark:border-fr-white dark:text-fr-white'
		: 'cursor-auto';
	$: _linkEnabledDisabledClass = !disabled
		? 'hover:text-fr-royal-blue dark:text-fr-white dark:hover:text-fr-royal-blue cursor-pointer'
		: 'text-gray-500 cursor-auto';

	const handleClick = () => {
		if (!disabled) dispatch('click');
	};
</script>

{#if color === 'Primary'}
	<button
		{type}
		class={`${_buttonBaseClass} ${_primaryEnabledDisabledClass} ${$$props.class}`}
		style={$$props.style}
		on:click={handleClick}
	>
		<slot />
	</button>
{:else if color === 'Secondary'}
	<button
		{type}
		class={`${_buttonBaseClass} ${_secondaryEnabledDisabledClass} ${$$props.class}`}
		style={$$props.style}
		on:click={handleClick}
	>
		<slot />
	</button>
{:else if color === 'White'}
	<button
		{type}
		class={`${_buttonBaseClass} ${_whiteEnabledDisabledClass} ${$$props.class}`}
		style={$$props.style}
		on:click={handleClick}
	>
		<slot />
	</button>
{:else if color === 'Link'}
	<button
		{type}
		class={`border-0 h-10 bg-transparent underline ${_linkEnabledDisabledClass} ${$$props.class}`}
		style={$$props.style}
		on:click={handleClick}
	>
		<slot />
	</button>
{:else}
	<!-- Outlined -->
	<button
		{type}
		class={`${_buttonBaseClass} ${_outlinedEnabledDisabledClass} ${$$props.class}`}
		style={$$props.style}
		on:click={handleClick}
	>
		<slot />
	</button>
{/if}
