<script lang="ts">
	import Link from '../form-elements/Link.svelte';

	export let items: Array<{text: string, url: string}> = [];
	export let prefetch = true;

	const removeUrlTrailingSlash = (url: string): string => url.endsWith('/') && url.length > 1 ?  url.replace(/\/$/, '') : url;
</script>

<nav aria-label="Breadcrumb flex max-w-screen-xl">
	<ol class="flex flex-wrap items-center gap-2 text-sm">
		{#each items as item, index}
			<li class="flex flex-row">
				<Link 
					color={ index !== (items.length -1) ? 'primary' : 'default'} 
					class={`${index === (items.length -1) ? 'no-underline' : 'underline'} font-light h-auto`}
					linkUrl={removeUrlTrailingSlash(item.url) ?? '/'}
					disabled={index === (items.length -1)}
					activePrefetch={prefetch ? 'hover' : 'off'}
				>
					{item.text ?? ''}
				</Link>
				<div class="pl-2">{index !== (items.length -1) ? ' /' : ''}</div>
			</li>
		{/each}
	</ol>
</nav>