<script lang="ts">
	import sanitizeHtml from 'sanitize-html';
	import { marked } from 'marked';

	export let sanitize = true;
	export let content = '<p>An <strong>HTML</strong> <br/>content goes here.</p>';
	$: _sanitizedContent = sanitize
		? sanitizeHtml(marked.parse(content), {
			allowedTags: [
					'details',
					'summary',
					'p',
					'h1',
					'h2',
					'h3',
					'h4',
					'h5',
					'a',
					'img',
					'div',
					'hr',
					'strong',
					'quote',
					'ul',
					'ol',
					'li',
					'main',
					'article',
					'section', 
					'table',
					'tbody',
					'tr',
					'td',
					'th',
					'br',
					'blockquote',
					'pre',
					'code',
					'em',
					'span'
				],
				allowedAttributes: {
					details: ['open'],
					summary: ['style', 'class'],
					p: ['style', 'class'],
					h1: ['style'],
					h2: ['style', 'id'],
					h3: ['style'],
					h4: ['style'],
					h5: ['style'],
					a: ['href', 'name', 'target', 'title'],
					img: ['src', 'srcset', 'alt', 'title', 'width', 'height', 'loading', 'class', 'style'],
					div: ['class'],
					section: ['class', 'id'],
					em: ['class', 'id']
				}
		  })
		: marked.parse(content);
</script>

<div class={`HtmlRenderer ${$$props.class}`}>
	{@html _sanitizedContent}
</div>

<style lang="css">
	.grey-color-style {
		color: #798DA3 !important;
	}

	:global(.HtmlRenderer a) {
		@apply font-bold text-fr-royal-blue hover:underline cursor-pointer;
	}

	:global(.HtmlRenderer hr) {
		@apply mt-8 border-fr-tru-grey border-[1px] mb-4;
	}
</style>

