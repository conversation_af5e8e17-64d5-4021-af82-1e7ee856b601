<script lang="ts">
    import IoIosArrowDown from 'svelte-icons/io/IoIosArrowDown.svelte'
    import IoIosArrowUp from 'svelte-icons/io/IoIosArrowUp.svelte'

    import HtmlRenderer from "../../cms-content-renderer/HtmlRenderer/index.svelte";
	import Button from "../../form-elements/Button.svelte";

    export let content = '<p>This is an example of <strong>excerpt</strong> to be rendered.</p>';
    export let showSeeMore = true;
    
    let _isExpanded = false;
    let _container: HTMLElement;
    let _contained: HTMLElement;
    let _wasContainerOverflowed = false;
    $: _isContainerOverflowed = _contained?.clientHeight > _container?.clientHeight;
    $: {
        if (_isContainerOverflowed) _wasContainerOverflowed = true;
    }
    $: _containerHeight = _isExpanded ? 'h-auto' : 'h-[72px]';

    const _collapseOrExpandContent = () => _isExpanded = !_isExpanded;

</script>

<div class={`Excerpt`}>
    <div class={`Excerpt__container overflow-hidden ${_containerHeight} ${$$props.class}`} bind:this={_container}>
        <div class="Excerpt__contained h-auto" bind:this={_contained}>
            <HtmlRenderer
                class="Excerpt__htmlContainer text-fr-black dark:text-fr-white"
                content={content}
            />
        </div>
        
    </div>
    
    {#if _wasContainerOverflowed && showSeeMore}
        <Button 
            color="Link" 
            class="Excerpt__containerSizeCta mb-1 mt-1 mx-0 lowercase flex h-auto text-sm"
            on:click={_collapseOrExpandContent}
        >
            {!_isExpanded ? 'see more' : 'See less'}
            <div class="w-[24px]">
                {#if !_isExpanded}
                    <IoIosArrowDown/>
                {:else}
                    <IoIosArrowUp/>
                {/if}
                
            </div>
            
        </Button>
    {/if}
</div>