<script lang="ts">
	import type { TSeo } from "$lib/shared/api-dtos/internal-rest/base-dtos";

    export let data: TSeo |  null = null;
    $: _title = data?.title ?? 'Expert Witness Referrals';
    $: _description = data?.description ?? '';
    $: _url = data?.url ?? '/';
    //Having a Canonical URL is a good SEO practice even if the page has unique content(in this case it could point to the page URL)
    $: _canonicalUrl = data?.canonicalUrl ?? _url;
    $: _focuskw = data?.focuskw;
    $: _metaRobotsNoIndex = data?.metaRobotsNoIndex ?? 'index';
    $: _metaRobotsNoFollow = data?.metaRobotsNoFollow ?? 'follow';
</script>

<svelte:head>
    <title>{_title}</title>
    <meta name="description" content={_description} />
    <meta property="og:title" content={_title} />
    <meta property="og:description" content={_description} />
    <meta name = "Keywords" content={_focuskw} />

    <link rel="canonical" href={_canonicalUrl} />
    <!-- Both OpenGraph URL and TwitterCard URL should points to the page URL-->
    <meta property="og:url" content={_url} />
    <meta name="twitter:url" content={_url} />

    <meta name="author" content="Consolidated Consultants" />
    <meta name="copyright" content="Consolidated Consultants" />
    <meta name="application-name" content="Free Referral by Consolidated Consultants" />
    <meta name="publisher" content="Consolidated Consultants" />

    <meta name="robots" content={`${_metaRobotsNoIndex}, ${_metaRobotsNoFollow}`} />
</svelte:head>