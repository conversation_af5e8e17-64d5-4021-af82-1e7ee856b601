<script lang="ts">
    import { onMount } from 'svelte';
    
    export let schema: string;
    let scriptElement: HTMLScriptElement | undefined;
  
    const injectSchema = (schemaData: string): void => {
      if (typeof window === 'undefined') return;
  
      if (scriptElement?.parentNode) {
        scriptElement.parentNode.removeChild(scriptElement);
      }
  
      scriptElement = document.createElement('script');
      scriptElement.type = 'application/ld+json';
      scriptElement.textContent = JSON.parse(JSON.stringify(schemaData));
      document.head.appendChild(scriptElement);
    };
  
    onMount(() => {
      return () => {
        if (scriptElement?.parentNode) {
          scriptElement.parentNode.removeChild(scriptElement);
        }
      };
    });
  
    $: if (schema) {
      injectSchema(schema);
    }
  </script>