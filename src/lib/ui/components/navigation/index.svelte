<script lang="ts">
	import { theme } from '$lib/ui/state-management/store/theme';

  export let onClose: () => void;
</script>

<div class={`${$theme ? 'text-fr-white' : 'text-fr-black'} pt-10`}>
  <h2 class={`${$theme ? 'border-fr-white' : 'border-fr-black'} border-solid border-b-4 pb-4 text-xl md:text-3xl`}>SITE NAVIGATION</h2>
  <ul class="nav__items text-xl pl-2">
    <li><a href="/" on:click={() => onClose()}>Home</a></li>
    <li><a href="/about-us" on:click={() => onClose()}>About Us</a></li>
    <li><a href="/search" on:click={() => onClose()}>Search Experts</a></li>
    <li><a href="/subdirectory" on:click={() => onClose()}>Search Experts by Keyword</a></li>
    <li><a href="/request-expert-witness" on:click={() => onClose()}>Request an Expert</a></li>
    <li><a href="/become-expert-witness" on:click={() => onClose()}>Become an Expert</a></li>
    <li><a href="/contact-us" on:click={() => onClose()}>Contact Us</a></li>
    <li><a href="/blog" on:click={() => onClose()}>Blog</a></li>
  </ul>
</div>

<style>
  .nav__items {
    font-family: Oswald, sans-serif;
  }
  .nav__items li:not(:last-child) {
    margin-bottom: 0.5rem;
  }
</style>
