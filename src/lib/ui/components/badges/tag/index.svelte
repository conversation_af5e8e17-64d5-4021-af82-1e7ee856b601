<script lang="ts">
	import { createEventDispatcher } from "svelte";
	import CleanIcon from "../../system-design/icons/CleanIcon.svelte";
	import SmallText from "../../system-design/texts/SmallText.svelte";
	import type { TTagEventDispatcher } from "./_types-interfaces";

    export let label: string;
    export let id: string;
    export let disabled: boolean = false;
    export let closable: boolean = false;
    const dispatch = createEventDispatcher<TTagEventDispatcher>();

    const _onCloseTag = () => {
        if (closable && !disabled) {
            dispatch('onClose', { label, id}, { cancelable: true });
        }
    };
</script>

<div class={`Tag Tag--${disabled? 'disabled' : 'enabled border-fr-charcoal dark:border-fr-white'} 
        flex w-auto 
        max-w-full 
        border-[2px] 
        rounded-[0.3125rem]
         pl-0 pr-2 px-2 items-center
        mr-2 mb-2
        ${$$props.class}
    `}
>
    {#if closable && label && id}
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <div on:click={_onCloseTag} class="Tag__closeButton--${disabled? 'disabled cursor-auto' : 'enabled cursor-pointer'}">
            <CleanIcon class={`${disabled ? 'text-gray-300' : 'text-fr-royal-blue'}`} />
        </div>
    {/if}
    <SmallText class={`${disabled ? 'text-gray-400' : ''}`}>{label}</SmallText>
</div>