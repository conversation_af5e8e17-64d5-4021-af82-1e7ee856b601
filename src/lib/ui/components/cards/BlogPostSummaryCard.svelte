<script lang="ts">
	import type { TReducedPostDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';
	import ArticleLayout from '../layouts/ArticleLayout.svelte';

	export let articleData: TReducedPostDto | null = null;
    $: _thumbnailUrl = articleData?.thumbnailUrl;
    $: _title = articleData?.title;
    $: _permalinkSlugSegment = articleData?.permalinkSlugSegment;
	let isHovered = false;
</script>

<ArticleLayout class="flex py-3" id="item.id">
    <a class="flex" href={_permalinkSlugSegment}
        on:mouseenter={() => isHovered = true}
        on:mouseleave={() => isHovered = false}
    >
        <img src={_thumbnailUrl} alt={_title}§/>
        <h3 
            class={`text-fr-charcoal dark:text-fr-white text-[1.25rem] leading-[1.75rem] mb-2 mt-0 ml-2 ${isHovered ? 'text-fr-royal-blue underline' : ''}`}>
            {_title}
        </h3>
    </a>
</ArticleLayout>

<style lang="css">
	:global(.GenericPage__topicCluster img) {
		@apply w-[150px] h-[150px];
	}
</style>

