<script lang="ts">
	import ArticleLayout from '../layouts/ArticleLayout.svelte';
	import H2 from '../system-design/headings/H2.svelte';
	import H3 from '../system-design/headings/H3.svelte';
	import Paragraph from '../system-design/texts/Paragraph.svelte';
	import Excerpt from '../cms-content-renderer/Excerpt/index.svelte';
	import Link from '../form-elements/Link.svelte';

	type TExpertSummary = {
		title: string;
		id: string;
		state: string;
		region: string;
		country: string;
		summary?: string;
		slug?: string;
		detailsLink?: string;
	};

	export let expert: TExpertSummary = {
		title: '<Technical Expert Title goes here>',
		id: '<Expert number goes here>',
		state: '<Expert state goes here>',
		region: '<Expert region goes here>',
		country: '<Expert country goes here>',
		summary: `<The expert summary goes here. It is intended to describe what the expert do and the experience. The summary content can be trim if the string size oversize an specific value. >`
	};
	export let titleHeader = 'h2';
	$: ({ title, id, state, region, country, summary, slug } = expert);
	let isHovered = false;
</script>

<ArticleLayout
	class="ExpertSummaryCard flex flex-col pt-7 pb-7 border-b-[1px] border-b-gray-500"
>
	<a href={`/expert/${slug}`} 
		class="ExpertSummaryCard__link"
		on:mouseenter={() => isHovered = true}
		on:mouseleave={() => isHovered = false}
	>
		<div
			class="ExpertSummaryCard__container pl-4 border-l-4 border-l-transparent"
		>
			{#if titleHeader === 'h3'}
				<H3 class={`ExpertSummaryCard__header__title mb-2 capitalize ${isHovered ? 'title_hovered' : 'text-inherit'}`}>{title}</H3>
			{:else}
				<H2 class={`ExpertSummaryCard__header__title mb-2 capitalize ${isHovered ? 'title_hovered' : 'text-inherit'}`}>{title}</H2>
			{/if}
			<div  class="ExpertSummaryCard__header__subtitle mb-3">
				<Paragraph class="break-words flex flex-col lg:flex-row">
					<div><b>Expert Number:</b>&nbsp;{id}&nbsp;|&nbsp;</div>
					<div><b>State:</b>&nbsp;{state}&nbsp;</div>
					{#if !country.includes('United States') && country !== '--'}
						<div>|&nbsp <b>Country:</b>&nbsp;{country}</div>
					{/if}
				</Paragraph>
			</div>

			<div class="ExpertSummaryCard__body__label font-medium capitalize text-[1rem] mb-1 font-semibold dark:text-fr-white">
				Provides Testimony In
			</div>
			<Excerpt
				class="ExpertSummaryCard__body__content max-w-full lg:max-w-[856px]"
				content={summary}
				showSeeMore={false}
			/>
		</div>
	</a>
</ArticleLayout>

<style>
	:global(.title_hovered){
		@apply text-fr-royal-blue underline;
	}
</style>
