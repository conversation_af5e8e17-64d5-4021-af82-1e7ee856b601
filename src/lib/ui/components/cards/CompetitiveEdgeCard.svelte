<script lang="ts">
	import ArticleLayout from '../layouts/ArticleLayout.svelte';
	import H3 from '../system-design/headings/H3.svelte';
	import Paragraph from '../system-design/texts/Paragraph.svelte';

	export let title: string;
	export let description: string;
	export let image: string;
</script>

<ArticleLayout
	class={`CompetitiveEdgeCard flex flex-col items-center md:items-stretch  w-full md:w-fit md:flex-row z-10 ${$$props.class}`}
>
	<div
		style="background-image: url('{image}');"
		class="flex flex-col min-w-[10rem] bg-cover bg-no-repeat rounded-md mr-8"
	/>
	<div style="background-image: url('{image}');" class="CompetitiveEdgeCard__Image--mobile">
		<div
			class="flex flex-col max-w-[31rem] min-h-[9.375rem] gap-4 px-4 text-center md:text-left border-0 py-8 md:py-0 bg-[#0C1E33B0] md:bg-inherit rounded-md"
		>
			<H3 class="m-0 text-fr-white md:text-fr-charcoal">{title}</H3>
			<Paragraph class="text-justify text-fr-white md:text-fr-charcoal">{description}</Paragraph>
		</div>
	</div>
</ArticleLayout>

<style>
	@media screen and (min-width: 768px) {
		.CompetitiveEdgeCard__Image--mobile {
			background-image: none !important;
		}
	}
	.CompetitiveEdgeCard__Image--mobile {
		@apply rounded-md bg-no-repeat bg-cover;
	}
</style>
