<script lang="ts">
	import type { TReducedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
	import ArticleLayout from '../layouts/ArticleLayout.svelte';
	import H3 from '../system-design/headings/H3.svelte';

	export let category: TReducedCategoryDto;
	$: ({ title, image, link } = category);
</script>

<a href={`${link}`} class="CategoryCard w-full h-auto" style="text-decoration: none;">
	{#if image}
		<ArticleLayout
			class={`flex flex-col justify-end w-full h-[14rem] bg-cover rounded-md group cursor-pointer`}
			style={image ? `background-image: url('${image}');` : ''}
		>
			<div
				class="flex flex-row h-full justify-center items-center text-center rounded-md px-4 py-2 gap-4 bg-[#0C1E33B0] group-hover:bg-[#3553EF94] ease-in-out duration-300"
			>
				<H3 class="text-fr-white">{title}</H3>
			</div>
		</ArticleLayout>
	{:else}
		<ArticleLayout
			class={`CategoryCard__container flex flex-col justify-end w-full h-[5rem] rounded-md group cursor-pointer bg-fr-charcoal dark:bg-fr-white`}
		>
			<div
				class="flex flex-row h-full justify-center items-center text-center rounded-md px-4 py-2 gap-4 group-hover:bg-fr-royal-blue ease-in-out duration-300"
			>
				<div
					class="CategoryCard__container__title text-[0.7rem] leading-[1.75rem] text-fr-white font-oswald uppercase dark:text-fr-charcoal"
				>
					{title}
				</div>
			</div>
		</ArticleLayout>
	{/if}
</a>
