<script lang="ts">
	import ArticleLayout from '../layouts/ArticleLayout.svelte';
	import H3 from '../system-design/headings/H3.svelte';
	import Paragraph from '../system-design/texts/Paragraph.svelte';

	export let title: string;
	export let description: string;
	export let image: string;
	export let url: string | null = null;
</script>

<ArticleLayout
	class={`w-full h-[16.875rem] bg-cover rounded-md`}
	style="background-image: url('{image}');"
>
	{#if url}
		<a href={url}
			class="cursor-pointer bg-[#0C1E33B0] hover:bg-[#3553EF94] ease-in-out duration-500 flex flex-col w-full h-[16.875rem] justify-end text-left rounded-md p-4 gap-1"
		>
			<H3 class="text-fr-white">{title}</H3>
			<Paragraph class="text-fr-white">{description}</Paragraph>
		</a>
	{:else}
	<div
		class="bg-[#0C1E33B0] hover:bg-[#3553EF94] ease-in-out duration-500 flex flex-col w-full h-[16.875rem] justify-end text-left rounded-md p-4 gap-1"
	>
		<H3 class="text-fr-white">{title}</H3>
		<Paragraph class="text-fr-white">{description}</Paragraph>
	</div>
	{/if}
</ArticleLayout>
