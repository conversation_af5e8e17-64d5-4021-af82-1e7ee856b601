<script lang="ts">
	import SectionLayout from '../layouts/SectionLayout.svelte';
	import H1 from '../system-design/headings/H1.svelte';
	import HtmlRenderer from '$lib/ui/components/cms-content-renderer/HtmlRenderer/index.svelte';
	import Breadcrumb from '../breadcrumb/Breadcrumb.svelte';
	import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';

	export let title: string = '';
	export let titleClass: string = '';
	export let description: string = '';
	export let descriptionClass: string = '';
	export let breadcrumbs: TSeoBreadcrumb[] = [];
</script>

<SectionLayout class={`PageHeader ${$$props.class} mb-10`} style={$$props.style}>
	{#if breadcrumbs[0]}
		<Breadcrumb items={breadcrumbs}/>
	{/if}
	<H1 class={`PageHeader__headerSection__title ${titleClass}`}>{title}</H1>

	{#if $$slots.richContent}
		<slot name="richContent" />
	{/if}

	<HtmlRenderer content={description} class={`PageHeader__headerSection__description grey-style ${descriptionClass} md:text-xl dark:text-fr-white`} />
</SectionLayout>

<style>
	:global(.PageHeader__headerSection__description h2) {
		@apply font-normal;
	}
	:global(.PageHeader__headerSection__description ul) {
		list-style: inherit;
		padding: revert;
		margin: revert;
	}
</style>
