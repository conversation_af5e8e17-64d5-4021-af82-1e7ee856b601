<script lang="ts">
	import { page } from '$app/stores';
	import { derived } from 'svelte/store';

	import NavIcon from '../system-design/icons/NavIcon.svelte';
	import NavMenu from '../navigation/index.svelte';
	import Sidebar from '../sidebar/Sidebar.svelte';
	import ThemeIcon from '../system-design/icons/ThemeIcon.svelte';
	import { theme } from '$lib/ui/state-management/store/theme';
	import { globalInfo } from '$lib/ui/state-management/store/global';
	import { isMobile } from '$lib/ui/state-management/store/device';
	import IoIosSearch from 'svelte-icons/io/IoIosSearch.svelte';
	import SectionLayout from '../layouts/SectionLayout.svelte';
	import ExpertSimpleSearchBox from '../search/ExpertSimpleSearchBox.svelte';

	let sidebarOpenedState = false;
	const currentPath = derived(page, ($page: { url: { pathname: string; }; }) => $page.url.pathname)
	let _showHeaderSearchingBar = true;
	$: {
		_showHeaderSearchingBar = !$currentPath.includes('search')
	}

	function setSidebarOpenedState(state?: boolean) {
		sidebarOpenedState = state ?? false;
	}

	function onNavMenuClick() {
		setSidebarOpenedState(true);
	}
</script>

<header class="Header flex min-h-16 w-full justify-center bg-fr-white dark:bg-fr-charcoal">
	<SectionLayout
		class="Header__itemsContainer flex flex-row items-center justify-between w-full px-4 py-2 gap-4 md:gap-2 xl:gap-4 lg:py-4"
	>
		{#if $theme}
			<a href="/" class="w-56 xl:w-[273px]" title="Light Free Referral Logo">
				<img
					src="/images/light-logo.svg"
					alt="Light Free Referral Logo"
					loading="eager"
					title="Light Free Referral Logo"
				/>
			</a>
		{:else}
			<a href="/" class="w-56 xl:w-[273px]" title="Dark Free Referral Logo">
				<img
					src="/images/dark-logo.svg"
					alt="Dark Free Referral Logo"
					loading="eager"
					title="Dark Free Referral Logo"
				/>
			</a>
		{/if}

		{#if $isMobile}
			<div class="flex flex-row gap-2 justify-center items-center">
				<a href="/search">
					<div class={`TextField__searchIcon w-8 bg-transparent dark:text-fr-white`}>
						<IoIosSearch />
					</div>
				</a>

				<div class="lg:hidden flex items-center">
					<ThemeIcon />
				</div>
				<a
					href="tel:************"
					class="Header__callButton hidden sm:flex font-semibold text-[1.25rem] leading-[1.75rem] text-fr-charcoal dark:text-fr-white uppercase"
				>
					CALL: ************
				</a>
				<button
					class={`${
						$theme ? 'text-fr-white border-fr-white' : 'text-fr-black border-fr-black'
					} pl-4 lg:pl-5 pr-1 border-solid border-l-[3px]`}
					on:click={() => onNavMenuClick()}
					title="Click to view menu navigation"
				>
					<NavIcon />
				</button>
				<Sidebar onClose={() => setSidebarOpenedState()} show={sidebarOpenedState}>
					<NavMenu onClose={() => setSidebarOpenedState()} />
				</Sidebar>
			</div>
		{:else}
			{#if $globalInfo?.headerLinks?.length}
				<ul class="desktop__menu flex flex-row justify-center items-center">
					{#each $globalInfo.headerLinks as { label, url }}
						<li
							class="px-3 xl:px-6 text-sm xl:text-base 2xl:text-lg whitespace-nowrap dark:text-fr-white"
						>
							<a href={url} title={label}>{label}</a>
						</li>
					{/each}
				</ul>
			{/if}

			<div class="flex flex-row gap-2 xl:gap-4 justify-center items-center">
				{#if _showHeaderSearchingBar}
					<ExpertSimpleSearchBox
						useCustomSearchAction
						useNoPadding
						customPlaceholder="Search Experts CV's"
					/>
				{/if}

				<!-- <ThemeIcon /> -->
				<a
					href="tel:************"
					class="Header__callButton hidden sm:flex m-0 whitespace-nowrap text-[1.25rem] leading-[1.75rem] text-fr-charcoal dark:text-fr-white uppercase font-semibold"
				>
					CALL: ************
				</a>
			</div>
		{/if}
	</SectionLayout>
</header>

<style>
	header {
		@apply sticky top-0 left-0 right-0 z-[1000];
	}

	.desktop__menu li:not(:last-of-type) {
		@apply border-r-2 border-solid border-gray-400;
	}

	.Header__callButton {
		font-family: 'Oswald', sans-serif;
	}
</style>
