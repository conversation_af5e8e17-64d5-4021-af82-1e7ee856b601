import type { PageServerData } from '../../../src/routes/blog/[slug]/$types';

type networkEnum = 'facebook' | 'twitter' | 'linkedin' | 'email';
type Post = PageServerData['post'];

const clientUrl = encodeURI('https://www.freereferral.com');

const getSlug = (post: Post): string => {
  return encodeURI(post!.slug!);
}

const getPath = (post: Post): string => {
	return `/blog/${getSlug(post)}`;
};

const getExcerpt = (post: Post): string => {
	return encodeURI(post!.excerpt!);
};

const getTagString = (post: Post): string => {
  const tags = post!.tags!.nodes.map(({ name }) => name);
	return encodeURI(tags.join(', ')) ?? '';
};

const getTitle = (post: Post): string => {
	return encodeURI(post!.title!) ?? '';
};

const getFacebookShareLink = (post: Post): string => {
  const sharerUrl = `https://www.facebook.com/sharer.php`;
	return `${sharerUrl}?u=${clientUrl}${getPath(post)}`;
};

const getEmailShareLink = (post: Post): string => {
	const emailSubject = `Check out this post on Free Referral: ${getTitle(post)}`;
  const emailBody = `${getExcerpt(post)}\n\n${clientUrl}${getPath(post)}`;
	return `mailto:?subject=${emailSubject}&amp;body=${emailBody}`;
};

const getLinkedinShareLink = (post: Post): string => {
	const sharerUrl = `https://www.linkedin.com/shareArticle`;
	return `${sharerUrl}?url=${clientUrl}${getPath(post)}&title=${getTitle}&summary=${getExcerpt(
		post
	)}&source=${encodeURI('Free Referral')}`;
};

const getTwitterShareLink = (post: Post): string => {
	const sharerUrl = `https://twitter.com/intent/tweet`;
	return `${sharerUrl}?url=${clientUrl}${getPath(post)}&text=${getExcerpt(
		post
	)}&hashtags=${getTagString(post)}`;
};

export const getSocialShareLink = (network: networkEnum, post: Post): string => {
  switch (network) {
		case 'facebook': {
			return getFacebookShareLink(post);
		}
		case 'twitter': {
			return getTwitterShareLink(post);
		}
		case 'linkedin': {
			return getLinkedinShareLink(post);
		}
		case 'email': {
			return getEmailShareLink(post);
		}
		default: {
			throw new Error(`Invalid social network provided: ${network}`);
		}
	} 
};
