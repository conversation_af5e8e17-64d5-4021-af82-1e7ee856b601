//@TODO: Review this, refactor it and and remove the use of dotenv
import dotenvx from "@dotenvx/dotenvx";

dotenvx.config();

export const backendUrl = process.env.BACKEND_URL;
export const acfRestEndpoint = `https://${backendUrl}/wp-json/acf/v3/options`;
export const wpGraphQlApiEndpoint = `https://${backendUrl}/graphql`;

export const wpGraphQlCategoriesApiEndpoint = (id: unknown) => `https://${backendUrl}/wp-json/wp/v2/categories/${id}`
export const wpGraphQlGravityFormsApiEndpoint = (formId: number) => `https://${backendUrl}/wp-json/gf/v2/forms/${formId}/submissions`;
export const wpGraphQlMediaApiEndpoint = (media: unknown) => `https://${backendUrl}/wp-json/wp/v2/media/${media}`

interface WpGraphQlPostsApiEndpointParams { offset: number, perPage: number, categoryId: unknown }
export const wpGraphQlPostsApiEndpoint = ({ offset, perPage, categoryId }: WpGraphQlPostsApiEndpointParams) => `https://${backendUrl}/wp-json/wp/v2/posts?offset=${offset}&per_page=${perPage}&orderby=date&order=desc${categoryId ? `&categories=${categoryId}` : '' }`

export const wpGraphQlUsersApiEndpoint = (postAuthor: string) => `https://${backendUrl}/wp-json/wp/v2/users/${postAuthor}`
