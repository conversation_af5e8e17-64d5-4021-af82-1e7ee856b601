import { sequence } from "@sveltejs/kit/hooks";
import urlFirewall from "$lib/server/utils/hooks/url-firewall";
import { HandleServerError } from "@sveltejs/kit";
import LoggerService from "$lib/server/services/logger";
import { Honey<PERSON><PERSON>gerAdapter, THoneyBadgerContext } from "$lib/server/services/logger/honeybadger/adapter";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import BaseControlledError from "$lib/shared/controlled-response/errors/base-controlled-error";
import { LOGGER_ERROR_LEVEL } from "$lib/server/services/logger/_constants";

export const handle = sequence(urlFirewall);

export const handleError: HandleServerError = async ({ error, event}) => {
    if(!(error instanceof BaseControlledError)) {
        const caughtError =  new InternalError(error, (error as any)?.message ?? error);
        const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
        loggerService.log(
			caughtError,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Server Hook',
					action: 'handleError'
				}					
			} as THoneyBadgerContext
		);
    }

	return {
		message: 'An internal error has happened. Please try again later.'
	};
};