import { error, <PERSON><PERSON> } from "@sveltejs/kit";
import type { TUrlRule } from "./_types-interfaces";
import { TPageError } from "../../../../../routes/_types-interfaces";

const _rules: TUrlRule[] = [
    {
      url: '/.*\\?.*gammatax.*/i',
      responseCode: 404,
      responseMessage: 'Not Found' 
    },
    {
      url: '/\\/sitemap\\.xml$/',
      responseCode: 200, 
    },/*
    { // Create specific rules for specific requested files instead (.php, .js, etc)
      url: '/\\.+/',
      responseCode: 404,
      responseMessage: 'Not Found' 
    },*/
    { 
      url: '^/.+/$',
      responseCode: 200,
      responseHeaders: {
        'X-Robots-Tag': ['noindex, follow']
      }
    },
    {
      url: '/\\?.*(?:p=|s=).*/i',
      responseCode: 404,
      responseMessage: 'Not Found' 
    }
];

/**
 * 
 * Checks if a URL matches against a pattern using either direct comparison or regex
 * @param url The current URL to check
 * @param pattern The pattern to match against
 * @returns Boolean indicating if the URL matches the pattern
 */
function _matchesUrlPattern(pathName: string, pattern: string): boolean {
  
  try {
    // First, try to interpret the pattern as a regex string with format indicators
    if (pattern.startsWith('/') && pattern.lastIndexOf('/') > 0) {
      const lastSlashIndex = pattern.lastIndexOf('/');
      const regexPattern = pattern.substring(1, lastSlashIndex);
      const flags = pattern.substring(lastSlashIndex + 1);
      
      // Create and test the regex
      const regex = new RegExp(regexPattern, flags);
      return regex.test(pathName);
    }
    
    // Second option: treat it as a raw regex if it contains special regex characters
    // This helps with patterns defined directly in the JSON without the slash notation
    if (pattern.includes('.*') || pattern.includes('\\') || 
        pattern.includes('[') || pattern.includes('+') || 
        pattern.includes('(') || pattern.includes('|')) {
      // For raw regex patterns in JSON, we need to handle escaping differently
      // Replace double backslashes with single backslashes (JSON escaping)
      const processedPattern = pattern.replace(/\\\\/g, '\\');
      const regex = new RegExp(processedPattern);
      return regex.test(pathName);
    }
    
    // Fallback to simple string inclusion test
    return pathName.includes(pattern);
  } catch (error) {
    console.error(`Error matching URL pattern: ${pattern}`, error);
    // Fallback to simple string inclusion in case of regex errors
    return pathName.includes(pattern);
  }
}

/**
 * Processes a URL against the defined rules
 * @param url The current URL object
 * @param rulesConfig The configuration containing all URL rules
 * @returns An object containing the HTTP status code to return and whether processing should continue
 */
function _processUrlAgainstRules(pathName: string, rulesConfig: TUrlRule[]): { 
    statusCode: number;
    shouldContinue: boolean;
    responseBody?: {message?: string}
    responseHeaders?: Record<string, string[]>
  } {
    for (const rule of rulesConfig) {
      if (_matchesUrlPattern(pathName, rule.url)) {
        return {
          statusCode: rule.responseCode,
          // Continue only if status code is 200
          shouldContinue: rule.responseCode === 200,
          responseBody: {
            message: rule.responseMessage
          },
          responseHeaders: rule.responseHeaders
        };
      }
    }
    
    // Default: continue processing with 200 status
    return { statusCode: 200, shouldContinue: true };
}

const urlFirewall: Handle = async ({ event, resolve })  =>  {
    const {url} = event;
    const fullPath = `${url.pathname}${url.search}`;
    // 1- Process URL against rules
    const { statusCode, shouldContinue, responseBody, responseHeaders:headersToSet } = _processUrlAgainstRules(fullPath, _rules);
    // If we should not continue, return the appropriate status
    if (!shouldContinue) throw error(statusCode, responseBody as TPageError)
    
    // 2- Resolve the event if it should continue
    const response = await resolve(event);

    // 3- Check for response headers to set
    if(headersToSet) {
      for (const [headerName, headerValues] of Object.entries(headersToSet)) {
        for (const headerValue of headerValues) {
           response.headers.set(headerName, headerValue);
         }
     }
    }
    
    return response;
};
export default urlFirewall;