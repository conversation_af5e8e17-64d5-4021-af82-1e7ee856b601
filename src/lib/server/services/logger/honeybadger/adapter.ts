import Honeybadger from '@honeybadger-io/js';
import { PRIVATE_HONEYBADGER_API_KEY, PRIVATE_HONEYBADGER_ENVIRONMENT } from '$env/static/private';

import type { ILoggerAdapter, ILoggerContext } from '../_types-interfaces';
import type { IBaseControlledError } from '$lib/shared/controlled-response/_types-interfaces';

export type THoneyBadgerContext = ILoggerContext;

export const HoneyBadgerAdapter = (): ILoggerAdapter<THoneyBadgerContext> => {
	const _setupHoneybadger = () => {
		if (typeof window === 'undefined' && !Honeybadger?.config?.apiKey) {
			Honeybadger.configure({
				apiKey: PRIVATE_HONEYBADGER_API_KEY,
				reportData: true,
				enableUncaught: true,
				enableUnhandledRejection: true,
				breadcrumbsEnabled: true,
				timeoutWarningThresholdMs: 1000, // for serverless like Vercel cron jobs
				environment: PRIVATE_HONEYBADGER_ENVIRONMENT
			});
		}
	};

	const logError = (error: IBaseControlledError<unknown>, context: THoneyBadgerContext) => {
		_setupHoneybadger();
		try {
			const { data, message, scope } = context;
			Honeybadger.notify({
				name: error.constructor.name,
				message,
				context: {
					data: JSON.parse(JSON.stringify(data, null, 2)),
					tags: `error${context && context.tags && context.tags.length ? ', ' + context.tags : ''}`
				},
				component: scope?.component,
				action: scope?.action
			});
		} catch (error: any) {
			console.error(
				`Failed attempt to notify HoneyBadger Error: ${error?.message || JSON.stringify(error)}`
			);
		}
	};

	const _notifyRaw = ({
		name,
		component,
		action,
		error,
		context
	}: {
		name: string;
		component?: string;
		action?: string;
		error: Error;
		context?: Record<string, unknown>;
	}) => {
		_setupHoneybadger();
		Honeybadger.notify({
			name,
			message: error.message,
			context,
			component,
			action
		});
	};

	const logWarning = (error: IBaseControlledError<unknown>, context: THoneyBadgerContext) => {
		_setupHoneybadger();
		try {
			const { data, message, scope } = context;
			Honeybadger.notify({
				name: error.constructor.name,
				message,
				context: {
					data: JSON.parse(JSON.stringify(data, null, 2)),
					tags: `warning${
						context && context.tags && context.tags.length ? ', ' + context.tags : ''
					}`
				},
				component: scope?.component,
				action: scope?.action
			});
		} catch (error: any) {
			console.error(
				`Failed attempt to notify HoneyBadger Warning: ${error?.message || JSON.stringify(error)}`
			);
		}
	};

	return {
		logError,
		logWarning,
        notify: _notifyRaw,
	};
};
