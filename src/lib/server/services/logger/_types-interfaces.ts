import type { IBaseControlledError } from '$lib/shared/controlled-response/_types-interfaces';
import type { LOGGER_ERROR_LEVEL } from './_constants';

export interface ILoggerContext {
	message: string;
	data: unknown;
	scope?: {
		component?: string;
		action?: string;
	};
	tags?: string;
}

export type ILoggerAdapter<C extends ILoggerContext> = {
	logError: (error: IBaseControlledError<unknown>, context: C) => void;
	logWarning: (error: IBaseControlledError<unknown>, context: C) => void;
	notify: (data: {
		name: string;
		component?: string;
		action?: string;
		error: Error;
		context?: Record<string, unknown>;
	}) => void;
};

export type ILogger<C extends ILoggerContext> = {
	getScope: () => C['scope'];
	setScope: (scope: C['scope']) => void;
	log: (error: IBaseControlledError<unknown>, level: LOGGER_ERROR_LEVEL, context: C) => void;
};
