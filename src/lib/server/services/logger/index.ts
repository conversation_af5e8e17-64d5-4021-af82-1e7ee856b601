import type { IBaseControlledError } from "$lib/shared/controlled-response/_types-interfaces";
import { LOGGER_ERROR_LEVEL } from "./_constants";
import type { ILogger, ILoggerAdapter, ILoggerContext } from "./_types-interfaces";


const LoggerService = <C extends ILoggerContext>(loggerAdapter: ILoggerAdapter<C>):ILogger<C> => {
    let _scope: C['scope'];

    const _error = (error: IBaseControlledError<unknown>, context: C) => {
        loggerAdapter.logError(error, {
            ...context,
            scope: {..._scope, ...context?.scope}, 
        })
    }

    const _warning = (error: IBaseControlledError<unknown>, context: C) => {
        loggerAdapter.logWarning(error, {
            ...context,
            scope: {..._scope, ...context?.scope}, 
        })
    }

    const setScope = (scope: C['scope']) => {
        _scope = scope;
    }

    const getScope = ():C['scope'] => {
        return _scope;
    };

    const log = (error: IBaseControlledError<unknown>, level: LOGGER_ERROR_LEVEL, context: C) => {
        if (level == LOGGER_ERROR_LEVEL.ERROR) {
            _error(error, context);
        } else {
            _warning(error, context);
        }
    };

    return {
        setScope,
        getScope,
        log,
    };
}

export default LoggerService;