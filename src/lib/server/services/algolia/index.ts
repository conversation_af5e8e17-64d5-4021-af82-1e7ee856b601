import { PRIVATE_ALGOLIA_APPLICATION_ID, PRIVATE_ALGOLIA_WRITE_API_KEY } from '$env/static/private';
import algoliasearch, { type SearchIndex } from 'algoliasearch';

import type { IResult } from '$lib/shared/controlled-response/_types-interfaces';
import Success from '$lib/shared/controlled-response/result/success';
import Failure from '$lib/shared/controlled-response/result/failure';
import type {
	TAlgoliaExpertHit,
	TAlgoliaSaveIndexParams,
	TAlgoliaSaveIndexResponse,
	TAlgoliaSearchFacetValuesParams,
	TAlgoliaSearchFacetValuesResponse,
	TAlgoliaSearchIndexParams,
	TAlgoliaSearchIndexResponse,
	TAlgoliaService
} from './_types-interfaces';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import { ALGOLIA_FACETS_BY_INDEX_MAP, ALGOLIA_FACETS_SIMPLE_REFS, ALGOLIA_INDEXES } from './_constants';
import type BaseControlledError from '$lib/shared/controlled-response/errors/base-controlled-error';

export const AlgoliaService = (): TAlgoliaService => {
	// internal definitions
	const _client = algoliasearch(PRIVATE_ALGOLIA_APPLICATION_ID, PRIVATE_ALGOLIA_WRITE_API_KEY);
	const _indexes = new Map<ALGOLIA_INDEXES, SearchIndex>();

	// internal methods
	const _getIndex = (indexRef: ALGOLIA_INDEXES) => {
		let index = _indexes.get(indexRef);
		if (!index) {
			index = _client.initIndex(indexRef);
			_indexes.set(indexRef, index);
		}

		return index;
	};

    const _getFacetFilters = (indexRef: ALGOLIA_INDEXES, facetRefs: {[key: string]: string | string[] | null}) => {
        const facets: Array<string | string[]> = [];
		const index = ALGOLIA_FACETS_BY_INDEX_MAP.get(indexRef);
        let key: keyof typeof facetRefs;
        for (key in facetRefs) {
            const facetName = index?.[key];
            // For non-hierarchical facets which value is composed as <facetName:facetValue>
            if (facetName && facetRefs[key]) {
                if(facetRefs[key] instanceof Array) {
                    const multipleOptions = (facetRefs[key] as string[]).reduce<string[]>((acc, value) => {
                        acc.push(`${facetName}:${value}`)
                        return acc;
                    }, []);
                    facets.push(multipleOptions);
                }
                else facets.push(`${facetName}:${facetRefs[key]}`);
            }
            // For hierarchical facets which value is composed as <facetName> only
            else if (!facetRefs[key]) facets.push(`${facetName}`);
        }
        return facets;
    };

	const _getFacetsName = (indexRef: ALGOLIA_INDEXES, facetRefs: ALGOLIA_FACETS_SIMPLE_REFS[]) => {
		const facetsName: Array<string> = [];
		const indexFacets = ALGOLIA_FACETS_BY_INDEX_MAP.get(indexRef);
		for (const facetRef of facetRefs) {
			const facetName = indexFacets?.[facetRef];
			if(facetName) facetsName.push(facetName)
		}

		return facetsName;
	};

	// exposed methods
	const searchIndex = async <THit>(
		params: TAlgoliaSearchIndexParams
	): Promise<IResult<TAlgoliaSearchIndexResponse<THit> | BaseControlledError<any>>> => {
		try {
			const { 
				indexRef, 
				query, 
				length = 20, 
				offset = 0,
				facets, 
				filters,
				exactMatch
			} = params;
			const exactMatchSymbol = exactMatch ? '"' : '';
			const _query = `${exactMatchSymbol}${query ?? ''}${exactMatchSymbol}`;
			const index = _getIndex(indexRef);
			let facetRefs = {};
			if(facets) {
				const {taxonomies, links, inactive, terms} = facets;
				facetRefs = { ...taxonomies, ...links, ...terms, inactive: typeof inactive === 'boolean' ? Number(inactive).toString() : null };
			}
			const facetFilters = facetRefs ? _getFacetFilters(indexRef, facetRefs) : [];
			
			const res = await index.search<THit>(_query, {
				offset,
				length,
				facetFilters,
				filters
			});

			return new Success({
				total: res.nbHits,
				items: res.hits as THit[],
				offset,
				length: length ?? res.nbHits
			});
		} catch (error: any) {
			console.error(`
                Error on AlgoliaService
                Method: searchIndex
                Trace: ${error?.message ?? error}
            `);
			return new Failure(new InternalError(error, error?.message));
		}
	};

	const searchFacetValues = async <TFHit>(
		params: TAlgoliaSearchFacetValuesParams
	): Promise<IResult<TAlgoliaSearchFacetValuesResponse<TFHit> | BaseControlledError<any>>> => {
		try {
			const { indexRef, query, facet } = params;
			const index = _getIndex(indexRef);
			const _facet = facet ? _getFacetsName(indexRef, [facet])[0] : '*';
			const res = await index.searchForFacetValues(_facet, query ?? '');

			return new Success({
				total: res.facetHits.length,
				items: res.facetHits as TFHit[]
			});
		} catch (error: any) {
			console.error(`
                Error on AlgoliaService
                Method: searchFacetValues
                Trace: ${error?.message ?? error}
            `);
			return new Failure(new InternalError(error, error?.message));
		}
	};

	const saveIndex = async<THit>(params: TAlgoliaSaveIndexParams): Promise<IResult<TAlgoliaSaveIndexResponse<THit> | BaseControlledError<any>>>  => {
		try {
			const { indexRef, indexes } = params;
			const index = _getIndex(indexRef);
			
			const res = await index.saveObjects([
				...indexes as TAlgoliaExpertHit[]
			])

			return new Success({
				indexRef, 
				indexes: indexes as THit[],
			});
		} catch (error: any) {
			console.error(`
                Error on AlgoliaService
                Method: saveIndex
                Trace: ${error?.message ?? error}
            `);
			return new Failure(new InternalError(error, error?.message));
		}
	};

	return {
		searchIndex,
		searchFacetValues,
		saveIndex
	};
};
