import type BaseControlledError from "$lib/shared/controlled-response/errors/base-controlled-error";
import type { IResult } from "$lib/shared/controlled-response/_types-interfaces";
import type { ALGOLIA_FACETS_SIMPLE_REFS, ALGOLIA_INDEXES } from "./_constants";

export type TAlgoliaExpertHit = {
    objectID: string;
    post_id: number;
    post_type: string;
    post_title: string;
    post_excerpt: string;
    slug?: string;
    content: string;
    permalink: string ;
    taxonomies?: {
        ['expert-state']?: string[];
        ['expert-region']?: string[];
        ['expert-country']?: string[];
        ['expert-group']?: string[];
        ['expert-category']?: string[];
    };
    inactive_expert?: boolean;
    expert_id?: string;
    post_date?: number;
    post_modified?: number;
    taxonomies_hierarchical?: {
        ['expert-state']?: {
            lvl0: string[];
            lvl1: string[];
            lvl2: string[];
            lvl3: string[];
        };
        ['expert-region']?: {
            lvl0: string[];
        };
        ['expert-country']?: {
            lvl0: string[];
        };
        ['expert-group']?: {
            lvl0: string[];
        };
        ['expert-category']?: {
            lvl0: string[];
        };
    }
};

export type TAlgoliaPostHit = {
    objectID: string;
    post_id: number;
    post_type: string;
    post_title: string;
    post_author: string;
    post_excerpt: string;
    content: string;
    permalink: string;
    post_date_formatted: string;
    taxonomies?: {
        ['category']?: string[];
    };
    post_date?: number;
    post_modified?: number;
    images?: {
        thumbnail?: {
            url: string;
            height: number;
            width: number;
        };
    };
};

export type TAlgoliaPageHit = {
    objectID: string;
    post_id: number;
    post_type: string;
    post_title: string;
    post_excerpt: string;
    content: string;
    permalink: {url: string};
    slug: string;
    post_date?: number;
    post_modified?: number;
    page_type: {
        value: string;
        label: string;
    };
    algolia_search_keyword: string;
};

export type TAlgoliaSaveIndexParams = {
    indexRef: ALGOLIA_INDEXES;
    indexes: TAlgoliaExpertHit[];
};

export type TAlgoliaSearchIndexTaxonomies = {
    category?: string[] | null;
    'category-lvl0'?: string | null;
    'category-lvl1'?: string | null;
    'category-lvl2'?: string | null;
    country?: string | null;
    region?: string[] | null;
    state?: string[] | null;
    pageType?: string | null;
    pageKeyword?: string | null;
    keywordIndex?: string | null;
}

export type TAlgoliaSearchIndexLinks = {
    permalink?: string | null;
}

export type TAlgoliaSearchIndexTerms = {
    name?: string |  null;
    topicCluster?: string | null;
}

export type TAlgoliaSearchIndexFacets = {
    taxonomies?: TAlgoliaSearchIndexTaxonomies;
    links?: TAlgoliaSearchIndexLinks,
    inactive?: boolean;
    terms?: TAlgoliaSearchIndexTerms
};

export type TAlgoliaSearchIndexParams = {
    indexRef: ALGOLIA_INDEXES;
    query?: string | null;
    offset?: number;
    length?: number;
    facets?: TAlgoliaSearchIndexFacets;
    filters?: string;
    exactMatch?: boolean;
};

export type TAlgoliaSearchIndexResponse<THit> = {
    total: number;
    items: THit[];
    offset: number;
    length: number;
};

export type TAlgoliaSaveIndexResponse<THit> = {
    indexRef: ALGOLIA_INDEXES;
    indexes: THit[];
};

export type TAlgoliaSearchFacetValuesParams = {
    indexRef: ALGOLIA_INDEXES;
    facet: ALGOLIA_FACETS_SIMPLE_REFS;
    query?: string;
};

export type TAlgoliaSearchFacetValuesResponse<TFHit> = {
    total: number;
    items: TFHit[];
};

export type TAlgoliaService = {
    searchIndex: <THit>(params: TAlgoliaSearchIndexParams) => Promise<IResult<TAlgoliaSearchIndexResponse<THit> | BaseControlledError<any>>>;
    searchFacetValues: <TFHit>(params: TAlgoliaSearchFacetValuesParams) => Promise<IResult<
        TAlgoliaSearchFacetValuesResponse<TFHit> | BaseControlledError<any>
    >>;
    saveIndex: <THit>(params: TAlgoliaSaveIndexParams) => Promise<IResult<TAlgoliaSaveIndexResponse<THit> | BaseControlledError<any>>>;
}