import Failure from "$lib/shared/controlled-response/result/failure";
import type { TContentService } from "./_types-interfaces";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import type{ ILogger, ILoggerContext } from '../logger/_types-interfaces';
import { LOGGER_ERROR_LEVEL } from '../logger/_constants';
import { TGetAllIndexedPagesResponse, TPageService } from "../pages/_types-interfaces";
import { TAlgoliaService } from "../algolia/_types-interfaces";
import Success from "$lib/shared/controlled-response/result/success";
import { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import { IResult } from "$lib/shared/controlled-response/_types-interfaces";
import { TCategoryService } from "../category/_types-interfaces";
import { TExtendedCategoryDto } from "$lib/shared/api-dtos/internal-rest/category-dtos";

export const ContentService = <LC extends ILoggerContext>(
    logger: ILogger<LC>,
    pageService: TPageService,
    categoryService: TCategoryService,
    algoliaService: TAlgoliaService
): TContentService => {
    logger.setScope({component: 'ContentService'});

    // exposed methods
    const getKeywordContent = async(keyword: string, category: string): Promise<IResult<string | null>> => {
        try {
            let response;
            //1- Get the Keyword page where the keyword match
            // - filter Algolia page by Keyword facet and get Content
            if(keyword) {
                response = await pageService.getAllIndexedPages(
                    {
                        pageTypeFilter: 'keyword',
                        keywordFilter: keyword
                    },
                    algoliaService,
                    true,
                    false
                );
                const {value} = response.getResultProps();
                const {items} = value;
                const matchedPage = (items as TExtendedPageDto[])[0];
                return new Success(matchedPage?.content || null);
            }

            // 2- If its not any Keyword Page and a Category was passed, then get the Expert Category which name match with the Keyword.
            if(category) {
                response = await categoryService.getAllIndexedCategories(
                    {nameFilter: category},
                    false
                );
                const {value} = response.getResultProps();
                const {items} = value;
                const matchedCategory = (items as TExtendedCategoryDto[])[0];
                return new Success(matchedCategory?.description || null);
            }
            
            return new Failure(null);
        } catch (error: any) {
            const caughtError = new InternalError(error, error?.message ?? error);
            logger.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: caughtError.getMessage(),
                    scope: {
                        ...logger.getScope(),
                        action: 'getKeywordContent'
                    }					
                } as LC,
            );
            return new Failure(null);
        }
    };

    return {
        getKeywordContent
    };
};