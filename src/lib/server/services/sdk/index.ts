import type { DocumentNode } from 'graphql';
import apolloWordPressClient from './apollo-wp/client';
import type { ApolloClient, MutationOptions, QueryOptions } from '@apollo/client';
import { getSdk, type Requester } from '$lib/shared/api-dtos/external-graphql/generated';

export type ApolloRequesterOptions<V, R> =
	| Omit<QueryOptions<V>, 'variables' | 'query'>
	| Omit<MutationOptions<R, V>, 'variables' | 'mutation'>;

const validDocDefOps = ['mutation', 'query', 'subscription'];

function getSdkApollo<C>(client: ApolloClient<C>) {
	const requester: Requester = async <R, V>(
		doc: DocumentNode,
		variables: V,
		options?: ApolloRequesterOptions<V, R>
	): Promise<R> => {
		// Valid document should contain *single* query or mutation unless it's has a fragment
		if (
			doc.definitions.filter(
				(d) => d.kind === 'OperationDefinition' && validDocDefOps.includes(d.operation)
			).length !== 1
		) {
			throw new Error('DocumentNode passed to Apollo Client must contain single query or mutation');
		}

		const definition = doc.definitions[0];

		// Valid document should contain *OperationDefinition*
		if (definition.kind !== 'OperationDefinition') {
			throw new Error('DocumentNode passed to Apollo Client must contain single query or mutation');
		}

		switch (definition.operation) {
			case 'mutation': {
				const response = await client.mutate<R, V>({
					mutation: doc,
					variables,
					...options
				});

				if (response.errors) {
					throw new Error(response.errors.map((e) => e.message).join('\n'));
				}

				if (response.data === undefined || response.data === null) {
					throw new Error('No data presented in the GraphQL response');
				}

				return response.data;
			}
			case 'query': {
				const response = await client.query<R, V>({
					query: doc,
					variables,
					...options
				});

				if (response.errors) {
					throw new Error(response.errors.map((e) => e.message).join('\n'));
				}

				if (response.data === undefined || response.data === null) {
					throw new Error('No data presented in the GraphQL response');
				}

				return response.data;
			}
			case 'subscription': {
				throw new Error('Subscription requests through SDK interface are not supported');
			}
			default: {
				throw new Error('Unsupported operation type');
			}
		}
	};

	return getSdk(requester);
}

export type Sdk = ReturnType<typeof getSdkApollo>;

export const sdk = getSdkApollo(apolloWordPressClient);
