import { type DefaultOptions, ApolloClient, InMemoryCache } from '@apollo/client/core';
import { HttpLink } from '@apollo/client/link/http';
import { from } from '@apollo/client/link/core';
import { wpGraphQlApiEndpoint } from '$lib/utils/endpoints';
import { onError } from '@apollo/client/link/error';

export const noCachingOption: DefaultOptions = {
	watchQuery: {
		fetchPolicy: 'no-cache',
		errorPolicy: 'ignore'
	},
	query: {
		fetchPolicy: 'no-cache',
		errorPolicy: 'all'
	}
};

export const makeErrorLink = (serviceName: string) =>
	onError(({ graphQLErrors, networkError }) => {
		if (graphQLErrors)
			graphQLErrors.forEach(({ message, locations, path }) =>
				console.log(
					`[GraphQL error]: Service: ${serviceName}, Message: ${message}, Location: ${JSON.stringify(
						locations,
						null,
						3
					)}, Path: ${path}`
				)
			);
		if (networkError) console.log(`[Network error]: Service: ${serviceName}, ${networkError}`);
	});


const uri = wpGraphQlApiEndpoint;

const httpLink = new HttpLink({
	uri,
	headers: {
		'Content-Type': 'application/json',
		Accept: 'application/json',
	}
});

const errorLink = makeErrorLink('Contentful');

const apolloConfig = {
	link: from([errorLink, httpLink]),
	cache: new InMemoryCache(),
	defaultOptions: noCachingOption
};

const apolloContentfulClient = new ApolloClient(apolloConfig);

export default apolloContentfulClient;
