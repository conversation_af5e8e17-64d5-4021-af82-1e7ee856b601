import Failure from '$lib/shared/controlled-response/result/failure';
import Success from '$lib/shared/controlled-response/result/success';
import type { IResult } from '$lib/shared/controlled-response/_types-interfaces';
import { ALGOLIA_INDEXES } from '../algolia/_constants';
import type { TAlgoliaSearchIndexResponse, TAlgoliaService } from '../algolia/_types-interfaces';
import type {
    TAlgoliaSearchPostHit,
    TExtendedExpertCategory,
    TGetIndexedPostsPaginatedResponse,
    TSearchPostsServiceParams,
    TSearchPostServiceResponse
} from './_types-interfaces';
import type {
    TExtendedCategoryDto,
    TReducedCategoryDto
} from '$lib/shared/api-dtos/internal-rest/category-dtos';
import type { TExtendedPostDto, TReducedPostDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';

export const PostsService = () => {
    // internal definitions
    const _mapIndexHitsToDto = (
        posts: TAlgoliaSearchPostHit[],
        reduced?: boolean
    ): TReducedPostDto[] => {
        return posts.reduce<TReducedPostDto[]>((acc, post) => {
            const { post_title, post_excerpt, permalink, post_id, taxonomies, images } = post;
            const permalinkFullSlugSegment = permalink.split('.com/').pop();
            const reducedPostDto: TReducedPostDto = {
                title: post_title,
                id: String(post_id ?? '--'),
                summary: post_excerpt,
                permalink,
                permalinkSlugSegment: permalinkFullSlugSegment ?? '',
                category: taxonomies?.['category']?.[0] ?? '',
                thumbnailUrl: images?.thumbnail?.url ?? ''
            };
            if (reduced) acc.push(reducedPostDto);
            else {
                const { objectID } = post;
                const extendedPostDto: TExtendedPostDto = {
                    ...reducedPostDto,
                    algoliaId: objectID
                };
                acc.push(extendedPostDto);
            }

            return acc;
        }, []);
    };

    const _mapGqlToDto = (
        categories: TExtendedExpertCategory[],
        reduced?: boolean
    ): TReducedCategoryDto[] | TExtendedCategoryDto[] => {
        return categories.reduce<Array<TReducedCategoryDto | TExtendedCategoryDto>>((acc, value) => {
            const { name, description, slug, uri, link, children, seo } = value;
            const reducedCategory: TReducedCategoryDto = {
                title: name ?? '',
                slug: slug ?? '',
                uri: uri ?? '',
                link: link ?? ''
            };
            if (reduced) acc.push(reducedCategory);
            else {
                const extendedCategory: TExtendedCategoryDto = {
                    ...reducedCategory,
                    image: '',
                    description: description ?? '',
                    seo: {
                        title: `${reducedCategory.title} - Expert Witness Referrals`,
                        url: seo?.canonical ?? '',
                        description:
                            seo?.metaDesc && seo.metaDesc !== ''
                                ? seo.metaDesc
                                : `${reducedCategory.title} as an expert witness category where experts can provide opinion and testify for your legal case`,
                        focuskw: seo?.focuskw
                    },
                    subCategories: children?.nodes?.[0] ? _mapGqlToDto(children.nodes, false) : []
                };
                acc.push(extendedCategory);
            }
            return acc;
        }, []);
    };

    // exposed methods


    const _checkGetPostsResponseTerms = (
        params: Pick<TSearchPostsServiceParams, 'start' | 'limit'> & {
            experts: TAlgoliaSearchPostHit[];
            totalPosts: number;
            mapped?: boolean;
            reduced?: boolean;
        }
    ): IResult<TSearchPostServiceResponse> => {
        const { start, limit, experts, totalPosts, reduced } = params;
        // let isResponseValid = true;
        const _total = totalPosts;
        let _items: TReducedPostDto[] = [];

        // Checking Response Terms for searching unique Expert by Expert Number
        // if (postNumberFilter) {
        //     isResponseValid = experts[0]?.permalink.includes(postNumberFilter);
        //     _total = isResponseValid ? 1 : 0;
        //     _items = isResponseValid ? _mapIndexHitsToDto([experts[0]], reduced) : [];
        // }
        // else
        if (!_items[0] && experts) _items = _mapIndexHitsToDto(experts, reduced);

        return new Success({
            total: _total,
            items: _items,
            start: start ?? 0,
            limit: limit ?? 1
        });
    };

    const getIndexedPostsPaginated = async (
        params: TSearchPostsServiceParams,
        algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ): Promise<IResult<TGetIndexedPostsPaginatedResponse>> => {
        const {
            limit = 1000,
            start = 0,
            criteriaFilter,
            categoryFilter,
            permalinkFilter,
            queryFilter,
            exactMatch,
            topicFilter
        } = params;
        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchPostHit>({
                indexRef: ALGOLIA_INDEXES.WP_POSTS_POST,
                offset: start,
                length: limit,
                query: criteriaFilter,
                facets: {
                    taxonomies: {
                        category: categoryFilter
                    },
                    links: {
                        permalink: permalinkFilter
                    },
                    terms: {
                        topicCluster: topicFilter
                    }
                },
                filters: queryFilter,
                exactMatch
            });
            const { isSuccess, value } = response.getResultProps();
            if (isSuccess) {
                const { items, offset, length, total } =
                    value as TAlgoliaSearchIndexResponse<TAlgoliaSearchPostHit>;
                return mapped
                    ? _checkGetPostsResponseTerms({
                        experts: items,
                        start: offset,
                        limit: length,
                        totalPosts: total,
                        reduced
                    })
                    : new Success({
                        total,
                        items,
                        start: offset,
                        limit: length
                    });
            }

            return new Failure({
                total: 0,
                items: [],
                start: start,
                limit: length
            });
        } catch (error: any) {
            console.error(`
                    Error on CategoryService
                    Method: getIndexedPostsPaginated
                    Trace: ${error?.message ?? error}
                `);

            return new Failure({
                total: 0,
                items: [],
                start: start,
                limit: length
            });
        }
    };

    const getAllIndexedPosts = async (
        params: TSearchPostsServiceParams,
        algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ): Promise<
        IResult<{
            total: number;
            items: Array<TReducedPostDto | TExtendedPostDto | TAlgoliaSearchPostHit>;
        }>
    > => {
        try {
            let start = 0;
            let limit = 1000;
            let total = 1;
            const posts: Array<TReducedPostDto | TExtendedPostDto | TAlgoliaSearchPostHit> = [];

            do {
                const response = await getIndexedPostsPaginated(
                    { ...params, limit, start },
                    algoliaHandler,
                    mapped,
                    reduced
                );
                const { value } = response.getResultProps();
                const { items, total: totalExperts, limit: paginationLimitIndex } = value;
                total = totalExperts ?? 0;
                limit = paginationLimitIndex ?? 0;
                start += items.length;
                if (items?.length) posts.push(...items);
            } while (posts.length < total);

            return new Success({ total, items: posts });
        } catch (error: any) {
            console.error(`
                Error on CategoryService
                Method: getAllIndexedPosts
                Trace: ${error?.message ?? error}
            `);

            return new Failure({
                total: 0,
                items: []
            });
        }
    };

    const getIndexedPosts = async (
        params: TSearchPostsServiceParams,
        algoliaHandler: TAlgoliaService,
        reduced?: boolean
    ): Promise<IResult<TSearchPostServiceResponse>> => {
        const { start = 0, limit = 10, criteriaFilter, categoryFilter, permalinkFilter } = params;

        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchPostHit>({
                indexRef: ALGOLIA_INDEXES.WP_POSTS_EXPERT,
                offset: start,
                length: limit,
                query: criteriaFilter,
                facets: {
                    taxonomies: {
                        category: categoryFilter
                    },
                    links: {
                        permalink: permalinkFilter
                    }
                }
            });
            const { isSuccess, value } = response.getResultProps();

            if (isSuccess) {
                const { items, offset, length, ...rest } =
                    value as TAlgoliaSearchIndexResponse<TAlgoliaSearchPostHit>;
                return _checkGetPostsResponseTerms({
                    experts: items,
                    start: offset,
                    limit: length,
                    totalPosts: rest.total
                });
            }

            return new Failure({
                total: 0,
                items: [],
                start,
                limit
            });
        } catch (error: any) {
            console.error(`
                Error on ExpertService
                Method: getIndexedExperts
                Trace: ${error?.message ?? error}
            `);

            return new Failure({
                total: 0,
                items: [],
                start,
                limit
            });
        }
    };

    return {
        getAllIndexedPosts,
        getIndexedPosts,
        getIndexedPostsPaginated
    };
};
