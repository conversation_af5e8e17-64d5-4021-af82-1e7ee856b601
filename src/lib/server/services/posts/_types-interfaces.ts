import type {
	BaseExpertCategoryFragment,
	ExtendedExpertCategoryFragment
} from '$lib/shared/api-dtos/external-graphql/generated';
import { TExtendedPostDto, TReducedPostDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';
import { TAlgoliaPostHit } from '../algolia/_types-interfaces';

export type TAlgoliaSearchPostHit = TAlgoliaPostHit;

export type TAlgoliaSearchHierarchicalCategoryHit = {
	readonly value: string;
};

export type TSearchPostsServiceParams = {
	start?: number;
	limit?: number;
	criteriaFilter?: string | null;
	categoryFilter?: string[] | null;
	permalinkFilter?: string | null;
	queryFilter?: string;
	exactMatch?: boolean;
	topicFilter?: string | null;
};

export type TSearchPostServiceResponse = {
	total: number;
	items: TReducedPostDto[] | TExtendedPostDto[];
	start: number;
	limit: number;
};

export type TGetIndexedPostsPaginatedResponse = {
	total: number;
	items: TReducedPostDto[] | TExtendedPostDto[] | TAlgoliaPostHit[];
	start: number;
	limit: number;
};

export type TBaseExpertCategory = BaseExpertCategoryFragment;
export type TExtendedExpertCategory = ExtendedExpertCategoryFragment;
