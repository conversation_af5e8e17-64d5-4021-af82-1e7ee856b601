import Failure from "$lib/shared/controlled-response/result/failure";
import Success from "$lib/shared/controlled-response/result/success";
import type { IBaseControlledError, IResult } from "$lib/shared/controlled-response/_types-interfaces";
import type {
    TBaseLocation,
    TExtendedCountryDto,
    TExtendedLocationDto,
    TExtendedRegionDto,
    TExtendedStateDto,
    TLocationType,
    TReducedCountryDto,
    TReducedRegionDto,
    TReducedStateDto
} from "$lib/shared/api-dtos/internal-rest/location-dtos";
import { ALGOLIA_INDEXES } from "../algolia/_constants";
import type { TAlgoliaSearchIndexResponse, TAlgoliaService } from "../algolia/_types-interfaces";
import type { TAlgoliaBaseSearchLocationHit, TAlgoliaSearchCountryHit, TAlgoliaSearchRegionHit, TBaseExpertLocation, TExtendedExpertLocation, TGetLocationResponse, TLocationTypeInput } from "./_types-interfaces";
import { wpGraphQlApiEndpoint } from "$lib/utils/endpoints";
import { GetExpertCountryQuery, GetExpertRegionQuery, GetExpertStateQuery } from "$lib/shared/api-dtos/external-graphql/generated";
import NotFoundError from "$lib/shared/controlled-response/errors/not-found-error";
import { TSeoFromGraphQL, TSeoService } from "../seo/_types-interfaces";

export const LocationService = (algoliaHandler: TAlgoliaService, seoService: TSeoService) => {
    // internal definitions
    const _mapHitToDto = (locations: TAlgoliaBaseSearchLocationHit[], reducedVersion?: boolean): TBaseLocation[] => {
        return locations.reduce<any>((acc, location) => {
            const {name: title, slug, permalink: link} = location;
            acc.push({title, slug, link});

            return acc;
        }, [])
    };

    const _mapGqlToDto = (
		locations: TExtendedExpertLocation[],
		reduced?: boolean,
	): TBaseLocation[] | TExtendedLocationDto[] => {
		return locations.reduce<Array<TBaseLocation | TExtendedLocationDto>>((acc, value) => {
			const { name, slug, link, uri} = value as TBaseExpertLocation;
			const reducedLocation: TBaseLocation = {
				title: name ?? '',
				slug: slug ?? '',
				link: uri ?? link ?? '',
			};
			if (reduced) acc.push(reducedLocation);
			else {
				const {
					description,
					seo,
                    taxonomyName: locationType
				} = value as TExtendedExpertLocation;
				// SEO is read from Custom Fields, not from default SEO field(see issue with WP All Import in Wordpress).
				const mappedSeo = seoService.mapSeoGqlToDto(
					seo as TSeoFromGraphQL,
                    uri ?? link ?? ''
				);
				const extendedLocation: TExtendedLocationDto = {
					...reducedLocation,
                    locationType: locationType as TLocationType,
					description: description ?? '',
					seo: {...mappedSeo},
				};
				acc.push(extendedLocation);
			}
			return acc;
		}, []);
	};

    // exposed methods
    const _getIndexedCountries = async(reducedVersion?: boolean): Promise<IResult<TReducedCountryDto[] | TExtendedCountryDto[]>> => {
        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchCountryHit>({
                indexRef: ALGOLIA_INDEXES.WP_TERMS_EXPERT_COUNTRY,
                length: 1000
            });
            const {isSuccess, value} = response.getResultProps(); 

            if (isSuccess) {
                const {items} = value as TAlgoliaSearchIndexResponse<TAlgoliaSearchCountryHit>;
                return new Success(_mapHitToDto(items, reducedVersion));
            }

            return new Failure([]);
        } catch(error: any) {
            console.error(`
                Error on LocationService
                Method: _getIndexedCountries
                Trace: ${error?.message ?? error}
            `);

            return new Failure([]);
        }
    };

    const _getIndexedRegions = async(reducedVersion?: boolean): Promise<IResult<TReducedRegionDto[] | TExtendedRegionDto[]>> => {
        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchRegionHit>({
                indexRef: ALGOLIA_INDEXES.WP_TERMS_EXPERT_REGION,
                length: 1000
            });
            const {isSuccess, value} = response.getResultProps(); 

            if (isSuccess) {
                const {items} = value as TAlgoliaSearchIndexResponse<TAlgoliaSearchRegionHit>;
                return new Success(_mapHitToDto(items, reducedVersion));
            }

            return new Failure([]);
        } catch(error: any) {
            console.error(`
                Error on LocationService
                Method: _getRegions
                Trace: ${error?.message ?? error}
            `);

            return new Failure([]);
        }
    };

    const _getIndexedStates = async(reducedVersion?: boolean): Promise<IResult<TReducedStateDto[] | TExtendedStateDto[]>> => {
        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchCountryHit>({
                indexRef: ALGOLIA_INDEXES.WP_TERMS_EXPERT_STATE,
                length: 1000
            });
            const {isSuccess, value} = response.getResultProps(); 

            if (isSuccess) {
                const {items} = value as TAlgoliaSearchIndexResponse<TAlgoliaSearchCountryHit>;
                return new Success(_mapHitToDto(items, reducedVersion));
            }

            return new Failure([]);
        } catch(error: any) {
            console.error(`
                Error on LocationService
                Method: _getIndexedStates
                Trace: ${error?.message ?? error}
            `);

            return new Failure([]);
        }
    };

    const getIndexedLocations = async(locationType: TLocationTypeInput, reducedVersion?: boolean): TGetLocationResponse => {
        let response ;
        switch(locationType.toLowerCase()) {
            case 'countries':
                response = await _getIndexedCountries(reducedVersion);
            break;
            case 'regions':
                response = await _getIndexedRegions(reducedVersion);
            break;
            default: // STATE
                response = await _getIndexedStates(reducedVersion);
        }
        
        return response;
    };

    const _getCountry = async(slug: string, reducedVersion?: boolean): Promise<IResult<TReducedCountryDto | TExtendedCountryDto | IBaseControlledError<string>>> => {
        try {
			const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify({
					query: `
					query GetExpertCountry($slug: [String]) {
						expertCountries(where: { slug: $slug }) {
							nodes {
								id
                                databaseId
                                name
                                slug
                                description
                                uri
                                link
                                taxonomyName
                                seo {
                                    title
                                    metaDesc
                                    canonical
                                    focuskw
                                    metaKeywords
                                    schema {
                                        raw
                                    }
                                    breadcrumbs {
                                        text
                                        url
                                    }
                                }
							}
						}
					}
					`,
					variables: {
						slug: [slug]
					}
				})
			});

			const result = (((await fetchedResult.json()).data as GetExpertCountryQuery)
				.expertCountries?.nodes ?? []) as TExtendedExpertLocation[];
			return result[0]
				? new Success(_mapGqlToDto(result, reducedVersion)[0])
				: new Failure(new NotFoundError(slug));
		} catch (error: any) {
			console.error(`
				Error on LocationService
				Method: _getCountry
				Trace: ${error?.message ?? error}
			`);
			return new Failure({} as TExtendedCountryDto);
		}
    };

    const _getRegion = async(slug: string, reducedVersion?: boolean): Promise<IResult<TReducedRegionDto | TExtendedRegionDto | IBaseControlledError<string>>> => {
        try {
			const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify({
					query: `
					query GetExpertRegion($slug: [String]) {
						expertRegions(where: { slug: $slug }) {
							nodes {
								id
                                databaseId
                                name
                                slug
                                description
                                uri
                                link
                                taxonomyName
                                seo {
                                    title
                                    metaDesc
                                    canonical
                                    focuskw
                                    metaKeywords
                                    schema {
                                        raw
                                    }
                                    breadcrumbs {
                                        text
                                        url
                                    }
                                }
							}
						}
					}
					`,
					variables: {
						slug: [slug]
					}
				})
			});

			const result = (((await fetchedResult.json()).data as GetExpertRegionQuery)
				.expertRegions?.nodes ?? []) as TExtendedExpertLocation[];

			return result[0]
				? new Success(_mapGqlToDto(result, reducedVersion)[0])
				: new Failure(new NotFoundError(slug));
		} catch (error: any) {
			console.error(`
				Error on LocationService
				Method: _getRegion
				Trace: ${error?.message ?? error}
			`);
			return new Failure({} as TExtendedCountryDto);
		}
    };

    const _getState = async(slug: string, reducedVersion?: boolean): Promise<IResult<TReducedRegionDto | TExtendedRegionDto | IBaseControlledError<string>>> => {
        try {
			const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify({
					query: `
					query GetExpertState($slug: [String]) {
						expertStates(where: { slug: $slug }) {
							nodes {
								id
                                databaseId
                                name
                                slug
                                description
                                uri
                                link
                                taxonomyName
                                seo {
                                    title
                                    metaDesc
                                    canonical
                                    focuskw
                                    metaKeywords
                                    schema {
                                        raw
                                    }
                                    breadcrumbs {
                                        text
                                        url
                                    }
                                }
							}
						}
					}
					`,
					variables: {
						slug: [slug]
					}
				})
			});

			const result = (((await fetchedResult.json()).data as GetExpertStateQuery)
				.expertStates?.nodes ?? []) as TExtendedExpertLocation[];

			return result[0]
				? new Success(_mapGqlToDto(result, reducedVersion)[0])
				: new Failure(new NotFoundError(slug));
		} catch (error: any) {
			console.error(`
				Error on LocationService
				Method: _getState
				Trace: ${error?.message ?? error}
			`);
			return new Failure({} as TExtendedCountryDto);
		}
    };

    const getLocation = async(locationType: TLocationTypeInput, slug: string, reducedVersion?: boolean) => {
        let response ;
        switch(locationType.toLowerCase()) {
            case 'countries':
                response = await _getCountry(slug, reducedVersion);
            break;
            case 'regions':
                response = await _getRegion(slug, reducedVersion);
            break;
            default: // STATES
                response = await _getState(slug, reducedVersion);
        }
        
        return response;
    };

    

    return {
        getIndexedLocations,
        getLocation
    };
};