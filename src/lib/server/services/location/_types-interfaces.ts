import { BaseExpertCountryFragmentFragment, ExtendedExpertLocationFragment } from "$lib/shared/api-dtos/external-graphql/generated";
import type { TBaseLocation } from "$lib/shared/api-dtos/internal-rest/location-dtos";
import type { IResult } from "$lib/shared/controlled-response/_types-interfaces";

export type TAlgoliaBaseSearchLocationHit = {
    readonly term_id: number;
    readonly taxonomy: string;
    readonly name: string;
    readonly description: string;
    readonly slug: string;
    readonly permalink: string;
};

export type TAlgoliaSearchCountryHit = TAlgoliaBaseSearchLocationHit & unknown;

export type TAlgoliaSearchRegionHit = TAlgoliaBaseSearchLocationHit & unknown;

export type TGetLocationResponse = Promise<IResult<TBaseLocation[]>>;

export type TExtendedExpertLocation = ExtendedExpertLocationFragment;

export type TBaseExpertLocation = BaseExpertCountryFragmentFragment

export type TLocationTypeInput = 'countries' | 'regions' | 'states';