import { PUBLIC_SITE_URL } from "$env/static/public";
import { wpGraphQlApiEndpoint } from '$lib/utils/endpoints';
import { PAGE_TYPES, type TExtendedPageDto, type TReducedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import NotFoundError from "$lib/shared/controlled-response/errors/not-found-error";
import Failure from "$lib/shared/controlled-response/result/failure";
import Success from "$lib/shared/controlled-response/result/success";
import type { TAlgoliaSearchPageHit, TGetAllIndexedPagesResponse, TGetIndexedPagesPaginatedResponse, TGetKeywordsContentPageResponse, TGetPageBySlugServiceResponse, TPageFromGraphQL, TPageService, TSearchIndexedPagesParams } from "./_types-interfaces";
import type { TAlgoliaSearchIndexResponse, TAlgoliaService } from "../algolia/_types-interfaces";
import { ALGOLIA_INDEXES } from "../algolia/_constants";
import { TSeoFromGraphQL, TSeoService } from "../seo/_types-interfaces";
import { ILogger, ILoggerContext } from "../logger/_types-interfaces";
import { LOGGER_ERROR_LEVEL } from "../logger/_constants";

export const PagesService = <LC extends ILoggerContext>(seoService: TSeoService, loggerService?: ILogger<LC>): TPageService => {
    const _mapIndexHitsToDto = (pages: TAlgoliaSearchPageHit[], reduced?: boolean): Array<TReducedPageDto | TExtendedPageDto> => {
        return pages.reduce<TReducedPageDto[]>((acc, page) => {
            const {
                post_title,
                slug,
                permalink
            } = page;

            const reducedPageDto: TReducedPageDto = {
                title: post_title,
                slug,
                link: permalink?.url ?? permalink ?? `${PUBLIC_SITE_URL}${slug}`
            };
            if (reduced) acc.push(reducedPageDto);
            else {
                const {page_type, algolia_search_keyword, content} = page;
                const extendedPageDto: TExtendedPageDto = {
                    ...reducedPageDto,
                    pageType: page_type?.value as PAGE_TYPES,
                    algoliaSearchKeyword: algolia_search_keyword,
                    content
                }
                acc.push(extendedPageDto);
            }

            return acc;
        }, [])
    };

    const _mapGqlToDto = (pages: TPageFromGraphQL[], reduced?: boolean): Array<TReducedPageDto | TExtendedPageDto> => {
        return pages.reduce<Array<TReducedPageDto | TExtendedPageDto>>((acc, page) => {
            const {
                slug,
                title,
            } = page;

            const reducedPage: TReducedPageDto = {
                title: title ?? "",
                slug: slug ?? "",
            };

            if (reduced) acc.push(reducedPage);
            else {
                const {
                    author,
                    link,
                    content,
                    featuredImage,
                    seo,
                    slug,
                    pageCustomFields
                } = page;
                const extendedPage: TExtendedPageDto = {
                    ...reducedPage,
                    description: content ?? "",
                    featuredImage: {
                        url: featuredImage?.node?.mediaItemUrl,
                        altText: featuredImage?.node?.altText,
                        caption: featuredImage?.node?.caption
                    },
                    author: {
                        email: author?.node?.email ?? "",
                    },
                    seo: seoService.mapSeoGqlToDto(seo as TSeoFromGraphQL, (link ?? `${PUBLIC_SITE_URL}${slug}`)),
                    pageType: pageCustomFields && pageCustomFields.pageType ?
                             (pageCustomFields.pageType as Array<string>)[0] as PAGE_TYPES :
                             PAGE_TYPES.STANDARD,
                    algoliaSearchKeyword: pageCustomFields?.algoliaSearchKeyword,
                    subtitle: pageCustomFields?.subtitle
                }
                
                acc.push(extendedPage);
            }

            return acc;
        }, []);
    };

    // exposed methods
    const getIndexedPagesPaginated = async(
        params: TSearchIndexedPagesParams,
		algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ): TGetIndexedPagesPaginatedResponse => {
        const {
            limit = 1000,
            start = 0,
            pageTypeFilter,
            keywordFilter,
            keywordIndexFilter,
        } = params;
        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchPageHit>({
                // this value can be passed as a parameter in the function call in the future to give more flexibility
                indexRef: ALGOLIA_INDEXES.WP_POSTS_PAGE_ALGOLIAKEYWORD_ASC,
                offset: start,
                length: limit,
                facets: {
                    taxonomies: {
                        pageType: pageTypeFilter,
                        pageKeyword: keywordFilter,
                        keywordIndex: keywordIndexFilter
                    }
                }
            });
            const {isSuccess, value} = response.getResultProps();
            if (isSuccess) {
                const {items, offset, length, total} = value as TAlgoliaSearchIndexResponse<TAlgoliaSearchPageHit>;
                const mappedItems = mapped ? _mapIndexHitsToDto(items, reduced) : items;
                return new Success({
                    total,
                    start: offset,
                    limit: length,
                    items: mappedItems
                });
			}

			return new Failure({
                total: 0,
                items: [],
                start: start,
				limit: length,
            });
		} catch (error: any) {
			console.error(`
                Error on PagesService
                Method: getIndexedPagesPaginated
                Trace: ${error?.message ?? error}
            `);

			return new Failure({
                total: 0,
                items: [],
                start: start,
				limit: length,
            });
		}
    };

    const getAllIndexedPages = async(
        params: TSearchIndexedPagesParams,
		algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ): TGetAllIndexedPagesResponse => {
        try {
            let start = 0;
            let limit = 1000;
            let total = 1;
            const pages: Array<TReducedPageDto | TExtendedPageDto | TAlgoliaSearchPageHit> = [];
            
            do {
                const response = await getIndexedPagesPaginated(
                    { ...params, limit, start },
                    algoliaHandler,
                    mapped,
                    reduced
                );
                const { value } = response.getResultProps();
                const { items, total: totalPages, limit: paginationLimitIndex } = value;
                total = totalPages ?? 0;
                limit = paginationLimitIndex ?? 0;
                start += items.length;
                if (items?.length) pages.push(...items);
            } while (pages.length < total);

            return new Success({ total: pages?.[0] ? total : 0, items: pages });
        } catch (error: any) {
			console.error(`
                Error on PagesService
                Method: getAllIndexedPages
                Trace: ${error?.message ?? error}
            `);

			return new Failure({
				total: 0,
				items: []
			});
		}
    };

    const getPageBySlug = async (slug: string): TGetPageBySlugServiceResponse => {
        try {
            const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                body: JSON.stringify({
                    query: ` query GetPageBySlug ($slug: String) {
                        pages(where: {name: $slug}) {
                            nodes {
                                title
                                content
                                slug
                                databaseId
                                link
                                author {
                                  node {
                                    id
                                    email
                                  }
                                }
                                isContentNode
                                contentTypeName
                                featuredImage {
                                  node {
                                    mediaItemUrl
                                    altText
                                    caption
                                  }
                                }
                                seo {
                                    canonical
                                    metaDesc
                                    metaKeywords
                                    title
                                    focuskw
                                    schema {
                                        raw
                                    }
                                    breadcrumbs {
                                        text
                                        url
                                    }
                                }
                                pageCustomFields{
                                    pageType
                                    algoliaSearchKeyword
                                    subtitle
                                }
                            }
                        }
                    }
                    `,
                    variables: {
                        slug
                    }
                })
            });
            if(!fetchedResult.ok)
                throw new Error(`Request to WP GraphQL-API as failed with response status: ${fetchedResult.status}`);
			// Check if the content type is JSON
            const contentType = fetchedResult.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json'))
                throw new TypeError(`The Content-Type returned from WP GraphQL-API is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);
			// Extract data
            const { data: { pages: { nodes: pages } } } = await fetchedResult.json();
            const page = (pages as TPageFromGraphQL[])[0];
            if (page) return new Success(_mapGqlToDto([page])[0]);

            return new Failure(new NotFoundError({ slug }));
        } catch (error: any) {
            const caughtError = new InternalError(error, error?.message ?? error);
			const errorMessage = caughtError.getMessage();
			// @TODO: Make LoggerService a mandatory parameter to pass for all the PagesService's consumers
            loggerService?.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: errorMessage,
                    scope: {
                        component: 'PagesService',
                        action: 'getPageBySlug'
                    }
                } as LC
            );
			// @TODO: Printing on Console should be an option to execute as part of the LoggerService procedure
			console.error(`
				Error in PagesService
				Method: getPageBySlug
				Trace: ${errorMessage}
			`);

			return new Failure(caughtError);
        }
    }

    return {
        getPageBySlug,
        getIndexedPagesPaginated,
        getAllIndexedPages
    };
}