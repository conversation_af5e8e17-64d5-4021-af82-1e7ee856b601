import type { QueryPageBySlugQuery } from "$lib/shared/api-dtos/external-graphql/generated";
import type { TExtendedPageDto, TReducedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import type { IBaseControlledError, IResult } from "$lib/shared/controlled-response/_types-interfaces";
import type { TAlgoliaPageHit, TAlgoliaService } from "../algolia/_types-interfaces";

export type TAlgoliaSearchPageHit = TAlgoliaPageHit;

export type TPageFromGraphQL = NonNullable<QueryPageBySlugQuery['pages']>['nodes'][0];

export type TGetPageBySlugServiceResponse = Promise<IResult<TReducedPageDto | TExtendedPageDto | IBaseControlledError<any>>>;

export type TSearchIndexedPagesParams = {
    start?: number;
    limit?: number;
    pageTypeFilter?: string;
    keywordFilter?: string;
    keywordIndexFilter?: string;
};

export type TGetAllIndexedPagesResponse = Promise<IResult<{
    total: number;
    items: Array<TReducedPageDto | TExtendedPageDto | TAlgoliaSearchPageHit>
}>>

export type TGetIndexedPagesPaginatedResponse = Promise<IResult<{
    total: number;
    items: TReducedPageDto[] | TExtendedPageDto[] | TAlgoliaSearchPageHit[];
    start: number;
    limit: number;
}>>;

export type TGetKeywordsContentPageResponse = Promise<IResult<TExtendedPageDto[] | IBaseControlledError<any>>>;

export type TPageService = {
    getPageBySlug: (slug: string) => TGetPageBySlugServiceResponse;
    getAllIndexedPages: (
        params: TSearchIndexedPagesParams,
		algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ) => TGetAllIndexedPagesResponse;
    getIndexedPagesPaginated: (
        params: TSearchIndexedPagesParams,
		algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ) => TGetIndexedPagesPaginatedResponse;
}