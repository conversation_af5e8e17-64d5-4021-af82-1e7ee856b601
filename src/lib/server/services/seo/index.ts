import { wpGraphQlApiEndpoint } from '$lib/utils/endpoints';
import type { T<PERSON><PERSON>, TSeo<PERSON>readcrumb, TSeoRedirectDto } from "$lib/shared/api-dtos/internal-rest/base-dtos";
import type { IBaseControlledError, IResult } from "$lib/shared/controlled-response/_types-interfaces";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import NotFoundError from "$lib/shared/controlled-response/errors/not-found-error";
import Failure from "$lib/shared/controlled-response/result/failure";
import Success from "$lib/shared/controlled-response/result/success";
import { LOGGER_ERROR_LEVEL } from "../logger/_constants";
import type { ILogger, ILoggerContext } from "../logger/_types-interfaces";
import { REDIRECT_CODES_MAP } from "./_constants";
import type {TSeoFromGraphQL, TSEORedirectsFromGraphQL, TSeoService} from './_types-interfaces';

const SeoService = <LC extends ILoggerContext>(logger: ILogger<LC>): TSeoService => {
    logger.setScope({component: 'SeoService'});

    const _mapRedirectsGqlToDto = (redirects: TSEORedirectsFromGraphQL[]): TSeoRedirectDto[] => {
        return redirects.reduce<TSeoRedirectDto[]>((acc, redirect) => {
            const {target, origin, type} = redirect;
            const redirectDto: TSeoRedirectDto = {
                origin,
                target,
                status: type ? REDIRECT_CODES_MAP.get(type) : null,
            }
            acc.push(redirectDto);

            return acc;
        }, []);
    }

    const removeUrlTrailingSlash = (url: string): string => {
        let _url = url;
        const re = /^(?:https?):\/\/(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z0-9]+(?:\/[\w-./?%&=]*)?$/;
        if(re.test(url) && url.endsWith('/')) _url = url.replace(/\/$/, '');

        return _url;
    };

    const getTitleWithPrefix = (seoTitle: string, titlePrefix: string): string => {
        const titleSegments = seoTitle.split('-');
        const titleFirstElement = `${titleSegments.shift()} ${titlePrefix}`;
        return `${titleFirstElement} - ${titleSegments.toString()}`;
    }

    const mapSeoGqlToDto = (seo: TSeoFromGraphQL, url: string, titlePrefix?: string): TSeo => {
        return {
            title: titlePrefix ? getTitleWithPrefix(seo?.title ?? '', titlePrefix) : seo?.title,
            description: seo?.metaDesc,
            url: removeUrlTrailingSlash(url),
            focuskw: seo?.focuskw,
            canonicalUrl: removeUrlTrailingSlash(seo?.canonical ?? ""),
            schema: seo?.schema?.raw,
            breadcrumbs: seo?.breadcrumbs as TSeoBreadcrumb[] ?? []
        }
    }

    const getRedirects = async(mapped?: boolean): Promise<IResult<TSeoRedirectDto[] | TSEORedirectsFromGraphQL[]>> => {
        try {
            const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                body: JSON.stringify({
                    query: `query Redirects {
                        seo {
                            redirects {
                                format
                                origin
                                target
                                type
                            }
                        }
                    }
                    `
                })
            });
            if(!fetchedResult.ok)
                throw new Error(`Request to WP GraphQL-API as failed with response status: ${fetchedResult.status}`);
            // Check if the content type is JSON
            const contentType = fetchedResult.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json'))
                throw new TypeError(`The Content-Type is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);

            const {data: {seo}} = await fetchedResult.json();
            const unmappedRedirects = seo?.redirects as TSEORedirectsFromGraphQL[];
            const redirects = mapped ? _mapRedirectsGqlToDto(unmappedRedirects) : unmappedRedirects;
            
            return new Success(redirects);
        } catch(error: any) {
            const caughtError = new InternalError(error, error?.message ?? error);
            logger.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: caughtError.getMessage(),
                    scope: {
                        ...logger.getScope(),
                        action: 'getRedirects'
                    }					
                } as LC,
            );
            return new Failure([]);
        }
    };

    const findRedirect = async(params: {originUrl: string, status: number}): Promise<IResult<TSeoRedirectDto | IBaseControlledError<unknown>>> => {
        try {
            const resRedirects = await getRedirects();
            const {isSuccess: successRedirects, value: valueRedirects} = resRedirects.getResultProps();
            if (successRedirects && valueRedirects[0]) {
                const {originUrl, status} = params;
                const redirects = (valueRedirects as TSEORedirectsFromGraphQL[]).filter((red) => red.origin === originUrl && red.type === status);
                if(redirects[0]) return new Success(_mapRedirectsGqlToDto(redirects)[0]);
            }

            return new Failure(new NotFoundError({slug: params.originUrl}))
        } catch(error: any) {
            const caughtError = new InternalError(error, error?.message ?? error);
            logger.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: caughtError.getMessage(),
                    scope: {
                        ...logger.getScope(),
                        action: 'findRedirect'
                    }					
                } as LC,
            );
            return new Failure(new InternalError({slug: params.originUrl}));
        }
    };

    return {getRedirects, findRedirect, removeUrlTrailingSlash, mapSeoGqlToDto, getTitleWithPrefix};
};

export default SeoService;