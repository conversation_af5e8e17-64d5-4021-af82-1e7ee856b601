import type { PostT<PERSON><PERSON>eo, SeoRedirect } from "$lib/shared/api-dtos/external-graphql/generated";
import { TSeo, TSeoRedirectDto } from "$lib/shared/api-dtos/internal-rest/base-dtos";
import { IBaseControlledError, IResult } from "$lib/shared/controlled-response/_types-interfaces";

export type TSeoFromGraphQL = PostTypeSeo;
export type TSEORedirectsFromGraphQL = SeoRedirect;

export type TSeoService = {
    mapSeoGqlToDto: (seo: TSeoFromGraphQL, url: string) => TSeo;
    getRedirects: (mapped?: boolean) => Promise<IResult<TSeoRedirectDto[] | TSEORedirectsFromGraphQL[]>>;
    findRedirect: (params: {originUrl: string, status: number}) => Promise<IResult<TSeoRedirectDto | IBaseControlledError<unknown>>>;
    removeUrlTrailingSlash: (url: string) => string;
    getTitleWithPrefix: (seoTitle: string, titlePrefix: string) => string;
}