import type {
	TBecomeExpertFormFieldsDto,
	TRequestExpertFormFieldsDto,
	TSubmitArticleFormFieldsDto,
	TBecomeAuthorFormFieldsDto
} from '$lib/shared/api-dtos/internal-rest/contact-dto';

export type TSubmitFormFields =
	| TRequestExpertFormFieldsDto
	| TBecomeExpertFormFieldsDto
	| TSubmitArticleFormFieldsDto
	| TBecomeAuthorFormFieldsDto;

export type TGravityFormSubmissionsResponse = {
	is_valid: boolean;
	confirmation_message?: string;
	confirmation_type?: string;
	validation_messages?: { [key: string]: string };
};
