import {
	PRIVATE_GRAVITYFORM_CONSUMER_KEY,
	PRIVATE_GRAVITYFORM_CONSUMER_SECRET,
} from '$env/static/private';
import * as Buffer from 'buffer';
import { wpGraphQlGravityFormsApiEndpoint } from '$lib/utils/endpoints';
import type {
	TRequestExpertFormFieldsDto,
	TSubmitFormErrorsDto,
	TSubmitFormSuccessDto
} from '$lib/shared/api-dtos/internal-rest/contact-dto';
import { GRAVITYFORMS_REF } from '$lib/shared/api-dtos/internal-rest/contact-dto';
import type {
	IBaseControlledError,
	IResult
} from '$lib/shared/controlled-response/_types-interfaces';
import BadRequestError from '$lib/shared/controlled-response/errors/bad-request-error';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import Failure from '$lib/shared/controlled-response/result/failure';
import Success from '$lib/shared/controlled-response/result/success';
import { LOGGER_ERROR_LEVEL } from '../logger/_constants';
import type { ILogger, ILoggerContext } from '../logger/_types-interfaces';
import { gravityFormIdsMap } from './_constants';
import type { TGravityFormSubmissionsResponse, TSubmitFormFields } from './_types-interfaces';

export const ContactFormService = <LC extends ILoggerContext>(logger: ILogger<LC>) => {
	logger.setScope({ component: 'ContactFormService' });
	const gravityBasicAuth = Buffer.Buffer.from(
		`${PRIVATE_GRAVITYFORM_CONSUMER_KEY}:${PRIVATE_GRAVITYFORM_CONSUMER_SECRET}`
	).toString('base64');

	const _mapRequestToSource = async (formRef: GRAVITYFORMS_REF, fields: TSubmitFormFields) => {
		let requestMappedToSource = {};
		try {
			let fieldsMap = {};
			switch (formRef) {
				case GRAVITYFORMS_REF.BECOME_EXPERT:
					fieldsMap = (await import('./gravity/become-expert-fields.map.json')).default;
					break;
				case GRAVITYFORMS_REF.BECOME_AUTHOR:
					fieldsMap = (await import('./gravity/become-author-fields.map.json')).default;
					break;
				case GRAVITYFORMS_REF.CONTACT_US:
					fieldsMap = (await import('./gravity/contact-us-fields.map.json')).default;
					break;
				case GRAVITYFORMS_REF.SUBMIT_ARTICLE:
					fieldsMap = (await import('./gravity/submit-article-fields.map.json')).default;
					break;
				default: // REQUEST_AN_EXPERT || REQUEST_THIS_EXPERT
					fieldsMap = (await import('./gravity/request-expert-fields.map.json')).default;
			}

			if (fieldsMap) {
				(Object.keys(fieldsMap) as (keyof typeof fieldsMap)[]).forEach((key) => {
					const formProperty = fieldsMap[key] as keyof typeof fields;
					const formValue = fields[formProperty];

					requestMappedToSource = { ...requestMappedToSource, [key]: formValue };
				});
			}

			return requestMappedToSource;
		} catch (error: any) {
			console.error(`
                Error on ContactService
                Method: _mapRequestToSource
                Trace: ${error?.message ?? error}
            `);

			return requestMappedToSource;
		}
	};

	const _mapSourceToRequest = async (
		formRef: GRAVITYFORMS_REF,
		source: { [key: string]: string }
	) => {
		let sourceMappedToRequest = {};
		try {
			let fieldsMap: any;
			switch (formRef) {
				case GRAVITYFORMS_REF.BECOME_EXPERT:
					fieldsMap = (await import('./gravity/become-expert-fields.map.json')).default;
					break;
				case GRAVITYFORMS_REF.BECOME_AUTHOR:
					fieldsMap = (await import('./gravity/become-author-fields.map.json')).default;
					break;
				case GRAVITYFORMS_REF.CONTACT_US:
					fieldsMap = (await import('./gravity/contact-us-fields.map.json')).default;
					break;
				case GRAVITYFORMS_REF.SUBMIT_ARTICLE:
					fieldsMap = (await import('./gravity/submit-article-fields.map.json')).default;
					break;
				default: // REQUEST_AN_EXPERT || REQUEST_THIS_EXPERT
					fieldsMap = (await import('./gravity/request-expert-fields.map.json')).default;
			}

			if (fieldsMap) {
				(Object.keys(source) as (keyof typeof source)[]).forEach((key) => {
					const sourceProperty = `input_${key}`;
					const requestProperty = fieldsMap[sourceProperty] as keyof TRequestExpertFormFieldsDto;
					const sourceValue = source[key];
					sourceMappedToRequest = { ...sourceMappedToRequest, [requestProperty]: sourceValue };
				});
			}

			return sourceMappedToRequest;
		} catch (error: any) {
			console.error(`
                Error on ContactService
                Method: _mapSourceToRequest
                Trace: ${error?.message ?? error}
            `);

			return sourceMappedToRequest;
		}
	};

	const submitForm = async (
		formRef: GRAVITYFORMS_REF,
		fields: TSubmitFormFields
	): Promise<
		IResult<TSubmitFormSuccessDto | IBaseControlledError<TSubmitFormErrorsDto | unknown>>
	> => {
		try {
			const formData = await _mapRequestToSource(formRef, fields);
			const formId = gravityFormIdsMap.get(formRef) ?? 0;
			const endpointUrl = wpGraphQlGravityFormsApiEndpoint(formId);
			const response = await fetch(endpointUrl, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json',
					Authorization: `Basic ${gravityBasicAuth}`
				},
				body: JSON.stringify(formData)
			});
			const res = (await response.json()) as TGravityFormSubmissionsResponse;

			if (res?.is_valid) {
				return new Success({
					message:
						res.confirmation_type === 'message' && res.confirmation_message
							? res.confirmation_message
							: 'Your Contact request was sent correctly',
					formFields: fields
				});
			}

			if (res?.validation_messages) {
				throw new BadRequestError({
					isAValidationError: true,
					errorMessage: await _mapSourceToRequest(formRef, res.validation_messages ?? {}),
					formFields: fields
				});
			} else {
				throw new InternalError(res);
			}
		} catch (error: any) {
			let caughtError = new InternalError(error, error?.message);
			let errorLevel = LOGGER_ERROR_LEVEL.ERROR;
			if (error instanceof BadRequestError) {
				caughtError = error;
				errorLevel = LOGGER_ERROR_LEVEL.WARNING;
			}
			logger.log(caughtError, errorLevel, {
				data: {
					caughtError: caughtError.getControlledErrorReference() as unknown,
					...error
				},
				message: caughtError.getMessage(),
				scope: {
					...logger.getScope(),
					action: 'submitForm'
				}
			} as LC);

			return new Failure(caughtError);
		}
	};

	return { submitForm };
};
