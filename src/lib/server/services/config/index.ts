import Failure from "$lib/shared/controlled-response/result/failure";
import Success from "$lib/shared/controlled-response/result/success";
import { acfRestEndpoint } from "$lib/utils/endpoints";
import { TConfigService } from "./_types-interfaces";
import type {ILogger, ILoggerContext} from './../logger/_types-interfaces';
import { IResult } from "$lib/shared/controlled-response/_types-interfaces";
import { TGlobalSettingsDto, THomepageSettingsDto } from "$lib/shared/api-dtos/internal-rest/settings-dtos";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import { LOGGER_ERROR_LEVEL } from "../logger/_constants";

const ConfigService = <LC extends ILoggerContext>(loggerService: ILogger<LC>): TConfigService => {

    const getGlobalSettings = async(): Promise<IResult<TGlobalSettingsDto>> => {
        try {
            const response = await fetch(acfRestEndpoint, {
                method: "GET",
                headers: {
                    'Content-Type': 'application/json',
					Accept: 'application/json',
                }
            });
            if(!response.ok)
                throw new Error(`Request to WP Rest-API as failed with response status: ${response.status}`);
            // Check if the content type is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json'))
                throw new TypeError(`The Content-Type is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);

            const globalSettingsParsed = (await response.json()) as TGlobalSettingsDto;
            const {companyInfo, footerLinks, headerLinks, socials} = globalSettingsParsed;
            
            return new Success({companyInfo, footerLinks, headerLinks, socials});                    
        } catch (e: any) {// @TODO: Refactor this.
            const caughtError = new InternalError(e, e?.message ?? e);
            loggerService.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: caughtError.getMessage(),
                    scope: {
                        component: 'ConfigService',
                        action: 'getGlobalSettings'
                    }
                } as LC
            );

            return new Failure({});
        }
    };

    const getHomepageSettings = async(): Promise<IResult<THomepageSettingsDto>> => {
        try {
            const response = await fetch(acfRestEndpoint, {
                method: "GET",
                headers: {
                    'Content-Type': 'application/json',
					Accept: 'application/json',
                }
            });
            if(!response.ok)
                throw new Error(`Request to WP Rest-API as failed with response status: ${response.status}`);
            // Check if the content type is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json'))
                throw new TypeError(`The Content-Type is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);

            const homeSettingsParsed = (await response.json()) as THomepageSettingsDto;
            const {testimonials, categories} = homeSettingsParsed;

            return new Success({testimonials, categories});
        } catch (e: any) {// @TODO: Refactor this.
            const caughtError = new InternalError(e, e?.message ?? e);
            loggerService.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: caughtError.getMessage(),
                    scope: {
                        component: 'ConfigService',
                        action: 'getHomepageSettings'
                    }
                } as LC
            );

            return new Failure({});
        }
    };
    return {
        getGlobalSettings,
        getHomepageSettings
    };
};
export default ConfigService;