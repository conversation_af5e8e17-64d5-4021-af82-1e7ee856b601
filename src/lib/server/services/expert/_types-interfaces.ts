import type { QueryExpertsQuery } from "$lib/shared/api-dtos/external-graphql/generated";
import type { TExtendedExpertDto, TReducedExpertDto } from "$lib/shared/api-dtos/internal-rest/expert-dtos";
import type { IBaseControlledError, IR<PERSON>ult } from "$lib/shared/controlled-response/_types-interfaces";
import type { TAlgoliaExpertHit } from "../algolia/_types-interfaces";


export type TAlgoliaSearchExpertHit = TAlgoliaExpertHit;

export type TSearchExpertServiceParams = {
    start?: number;
    limit?: number;
    criteriaFilter?: string | null;
    categoryFilter?: string[] | null;
    countryFilter?: string | null;
    regionFilter?: string[] | null;
    stateFilter?: string[] | null;
    permalinkFilter?: string | null;
    expertNumberFilter?: string | null;
    queryFilter?: string;
    exactMatch?: boolean;
    includeInactive?: boolean;
    useSorted?: boolean
};

export type TSearchExpertServiceResponse = {
    total: number;
    items: TReducedExpertDto[] | TExtendedExpertDto[];
    start: number;
    limit: number;
};

export type TGetIndexedExpertsPaginatedResponse = {
    total: number;
    items: TReducedExpertDto[] | TExtendedExpertDto[] | TAlgoliaSearchExpertHit[];
    start: number;
    limit: number;
};

export type TExpertFromGraphQL = NonNullable<QueryExpertsQuery['experts']>['nodes'][0];

export type TPageInfoFromGraphQL = NonNullable<QueryExpertsQuery['experts']>['pageInfo'];

export type TGetExpertServiceResponse = Promise<IResult<TReducedExpertDto | TExtendedExpertDto | IBaseControlledError<any>>>