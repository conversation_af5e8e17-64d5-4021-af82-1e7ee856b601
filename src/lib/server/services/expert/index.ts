import { wpGraphQlApiEndpoint } from '$lib/utils/endpoints';
import Failure from "$lib/shared/controlled-response/result/failure";
import Success from "$lib/shared/controlled-response/result/success";
import type { IResult } from "$lib/shared/controlled-response/_types-interfaces";
import { ALGOLIA_INDEXES } from "../algolia/_constants";
import type { TAlgoliaSearchIndexResponse, TAlgoliaService, TAlgoliaSaveIndexResponse } from "../algolia/_types-interfaces";
import type { TAlgoliaSearchExpertHit, TSearchExpertServiceResponse, TSearchExpertServiceParams, TExpertFromGraphQL, TGetExpertServiceResponse, TGetIndexedExpertsPaginatedResponse, TPageInfoFromGraphQL } from "./_types-interfaces";
import type { TExtendedExpertDto, TReducedExpertDto } from "$lib/shared/api-dtos/internal-rest/expert-dtos";
import NotFoundError from "$lib/shared/controlled-response/errors/not-found-error";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import { PUBLIC_SITE_URL } from "$env/static/public";
import type { TSeoFromGraphQL, TSeoService } from "../seo/_types-interfaces";
import { TReducedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';

export const ExpertService = (seoService: TSeoService) => {
    // internal definitions
    //TODO: Remove this way of getting the ExpertID once the essential data is flowed into Algolia Indexes (it will require a custom script in WP).
    const mapPermalinkToExpertId = (permalink: string) => {
        let expertId = null;
        const permalinkSegments = permalink.split('expert/');
        if (permalinkSegments[1]) {
            const slugSegments = permalinkSegments[1].split('-');
            const expertIdSegment = slugSegments[(slugSegments.length - 1)].split('/')[0];
            if (expertIdSegment) expertId = expertIdSegment;
        }

        return expertId;
    };

    const _mapIndexHitsToDto = (experts: TAlgoliaSearchExpertHit[], reduced?: boolean): TReducedExpertDto[] => {
        return experts.reduce<TReducedExpertDto[]>((acc, expert) => {
            const {post_title, post_excerpt, taxonomies, permalink, expert_id, slug } = expert;
            const country = taxonomies?.['expert-country']?.[0] ?? '--';
            const region = taxonomies?.['expert-region']?.[0] ? taxonomies?.['expert-region']?.toString() : '--';
            const state = taxonomies?.['expert-state']?.[0] ?? '--';
            const permalinkFullSlugSegment = slug ?? permalink.split('/expert/')[1];
            const reducedExpertDto: TReducedExpertDto = {
                title: post_title,
                id: expert_id ?? String(mapPermalinkToExpertId(permalink) ?? '--'),
                country,
                region,
                state,
                summary: post_excerpt,
                permalink,
                permalinkSlugSegment: permalinkFullSlugSegment
            };
            if (reduced) acc.push(reducedExpertDto);
            else {
                const {objectID, inactive_expert} = expert;
                const extendedExpertDto: TExtendedExpertDto = {
                    ...reducedExpertDto,
                    algoliaId: objectID,
                    inactiveExpert: inactive_expert,
                }
                acc.push(extendedExpertDto);
            }

            return acc;
        }, [])
    };

    const _mapGqlToDto = (experts: TExpertFromGraphQL[], reduced?: boolean): Array<TReducedExpertDto | TExtendedExpertDto> => {
        return experts.reduce<Array<TReducedExpertDto | TExtendedExpertDto>>((acc, expert) => {
            const {
                slug,
                title,
                excerpt,
                link,
                uri,
                expertCountries,
                expertRegions,
                expertStates,
                featuredImage,
                expertCustomFields,
                expertCategories,
                seo,
            } = expert;

            const reducedExpert: TReducedExpertDto = {
                id: expertCustomFields?.expertId ?? mapPermalinkToExpertId(uri ?? "") ?? "",
                title: title ?? "",
                summary: excerpt ?? "",
                permalink: link ?? "",
                permalinkSlugSegment: slug ?? "",
                country: expertCountries?.nodes.reduce((acc, value) => `${acc}${acc !== '' ? ' | ' : ''}${value.name}`, '') ?? "",
                region: expertRegions?.nodes.reduce((acc, value) => `${acc}${acc !== '' ? ' | ' : ''}${value.name}`, '') ?? "",
                state: expertStates?.nodes.reduce((acc, value) => `${acc}${acc !== '' ? ' | ' : ''}${value.name}`, '') ?? "",
                inactiveExpert: expertCustomFields?.inactiveExpert,
            };

            if (reduced) acc.push(reducedExpert);
            else {
                const {content, databaseId} = expert;
                const extendedExpert: TExtendedExpertDto = {
                    ...reducedExpert,
                    description: content ?? "",
                    imageUrl: featuredImage?.node.sourceUrl,
                    categories: expertCategories?.nodes?.map(category => { return {slug: category.slug!, title: category.name!};}) ?? [],
                    excerpt: expert?.excerpt ?? "",
                    seo: seoService.mapSeoGqlToDto(
                        {
                            ...seo,
                            title: seo?.title ?? `${reducedExpert.title}`,
                            metaDesc:  seo?.metaDesc ?? `${reducedExpert.title} provides opinion and testimony to attorneys`,
                        } as TSeoFromGraphQL,
                        (link ?? `${PUBLIC_SITE_URL}expert/${slug}`)
                    ),
                    dbId: databaseId,
                    featureFlag: expertCustomFields?.featureFlag,
                    topicClusters: expertCustomFields?.topicClusters?.map(topic => ({
                        slug: topic?.slug || '',
                        title: topic?.title || '',
                        link: topic?.uri || ''
                    } as TReducedPageDto)).filter(topic => topic.slug && topic.title && topic?.link) ?? [],
                }
                acc.push(extendedExpert);
            }

            return acc;
        }, []);
    };

    const _checkGetExpertsResponseTerms = (
        params: Pick<TSearchExpertServiceParams, 'expertNumberFilter' | 'start' | 'limit'> & {
            experts: TAlgoliaSearchExpertHit[],
            totalExperts: number,
            mapped?: boolean,
            reduced?:boolean
        },
    ): IResult<TSearchExpertServiceResponse> => {
        const {expertNumberFilter, start, limit, experts, totalExperts, reduced} = params;
        let isResponseValid = true;
        let _total = totalExperts;
        let _items: TReducedExpertDto[] = [];

        // Checking Response Terms for searching unique Expert by Expert Number
        if (expertNumberFilter) {
            isResponseValid = experts[0]?.permalink.includes(expertNumberFilter);
            _total = isResponseValid ? 1 : 0;
            _items = isResponseValid ? _mapIndexHitsToDto([experts[0]], reduced) : [];
        }
        else if (!_items[0] && experts) _items = _mapIndexHitsToDto(experts, reduced);

        return new Success({
            total: _total,
            items: _items,
            start: start ?? 0,
            limit: limit ?? 1
        });
    };

    // exposed methods
    const getIndexedExpertsPaginated = async(
        params: TSearchExpertServiceParams,
		algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ): Promise<IResult<TGetIndexedExpertsPaginatedResponse>> => {
        const {
            limit = 1000,
            start = 0,
            criteriaFilter,
            categoryFilter,
            countryFilter,
            regionFilter,
            stateFilter,
            permalinkFilter,
            expertNumberFilter,
            queryFilter,
            exactMatch,
            includeInactive,
            useSorted
        } = params;
        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchExpertHit>({
                indexRef: useSorted ? ALGOLIA_INDEXES.WP_POSTS_EXPERT_TILE_ASC : ALGOLIA_INDEXES.WP_POSTS_EXPERT,
                offset: expertNumberFilter ? 0 : start,
                length: expertNumberFilter ? 1 : limit,
                query: expertNumberFilter ?? criteriaFilter,
                facets: {
                    taxonomies: {
                        category: categoryFilter,
                        country: countryFilter,
                        region: regionFilter,
                        state: stateFilter,
                    },
                    links: {
                        permalink: permalinkFilter
                    },
                    inactive: includeInactive
                },
                filters: queryFilter,
                exactMatch
            });
            const {isSuccess, value} = response.getResultProps();
            if (isSuccess) {
                const {items, offset, length, total} = value as TAlgoliaSearchIndexResponse<TAlgoliaSearchExpertHit>;
                return mapped ? _checkGetExpertsResponseTerms({
                            experts: items,
                            start: offset,
                            limit: length,
                            expertNumberFilter,
                            totalExperts: total,
                            reduced
                        }) : new Success({
                            total,
                            items,
                            start: offset,
                            limit: length,
                        });
			}

			return new Failure({
                total: 0,
                items: [],
                start: start,
				limit: length,
            });
		} catch (error: any) {
			console.error(`
                Error on CategoryService
                Method: getIndexedExpertsPaginated
                Trace: ${error?.message ?? error}
            `);

			return new Failure({
                total: 0,
                items: [],
                start: start,
				limit: length,
            });
		}
    };

    const getAllIndexedExperts = async(
        params: TSearchExpertServiceParams,
		algoliaHandler: TAlgoliaService,
        mapped?: boolean,
        reduced?: boolean
    ): Promise<IResult<{
		total: number;
		items: Array<TReducedExpertDto | TExtendedExpertDto | TAlgoliaSearchExpertHit>
	}>> => {
        try {
			let start = 0;
			let limit = 1000;
			let total = 1;
			const experts: Array<TReducedExpertDto | TExtendedExpertDto | TAlgoliaSearchExpertHit> = [];

			do {
				const response = await getIndexedExpertsPaginated({...params, limit, start}, algoliaHandler, mapped, reduced);
				const { value } = response.getResultProps();
				const {
					items,
					total: totalExperts,
					limit: paginationLimitIndex,
				} = value;
				total = totalExperts ?? 0;
				limit = paginationLimitIndex ?? 0;
				start+= items.length;
				if (items?.length) experts.push(...items);

			} while (experts.length < total);

			return new Success({total, items: experts});
		} catch (error: any) {
			console.error(`
                Error on CategoryService
                Method: getAllIndexedExperts
                Trace: ${error?.message ?? error}
            `);

			return new Failure({
				total: 0,
				items: []
			});
		}
    };

    const getIndexedExperts = async(
        params: TSearchExpertServiceParams,
        algoliaHandler: TAlgoliaService,
        reduced?: boolean
    ): Promise<IResult<TSearchExpertServiceResponse>> => {
        const {
            start = 0,
            limit = 20,
            criteriaFilter,
            categoryFilter,
            countryFilter,
            regionFilter,
            stateFilter,
            permalinkFilter,
            expertNumberFilter
        } = params;

        try {
            const response = await algoliaHandler.searchIndex<TAlgoliaSearchExpertHit>({
                indexRef: ALGOLIA_INDEXES.WP_POSTS_EXPERT,
                offset: expertNumberFilter ? 0 : start,
                length: expertNumberFilter ? 1 : limit,
                query: expertNumberFilter ?? criteriaFilter,
                facets: {
                    taxonomies: {
                        category: categoryFilter,
                        country: countryFilter,
                        region: regionFilter,
                        state: stateFilter,
                    },
                    links: {
                        permalink: permalinkFilter
                    }
                }
            });
            const {isSuccess, value} = response.getResultProps();

            if (isSuccess) {
                const {items, offset, length, ...rest } = value as TAlgoliaSearchIndexResponse<TAlgoliaSearchExpertHit>;
                return _checkGetExpertsResponseTerms({
                    experts: items,
                    start: offset,
                    limit: length,
                    expertNumberFilter,
                    totalExperts: rest.total
                });
            }

            return new Failure({
                total: 0,
                items: [],
                start,
                limit
            });
        } catch(error: any) {
            console.error(`
                Error on ExpertService
                Method: getIndexedExperts
                Trace: ${error?.message ?? error}
            `);

            return new Failure({
                total: 0,
                items: [],
                start,
                limit
            });
        }
    };

    /**
     * !Note: This method hits a GraphQL API using Fetch built-in solution and no a GraphQL Client such as Apollo or Urql.
     * !      It should leverages the existing Documents (*.gql documents for 'query | mutations | subscription | fragments')
     * !      and the Types generated by CodeGen for getting the Query as an object, but for now we are creating the object
     * !      passing the query string directly.
     * TODO: Remove query string and use 'loadDocuments' from '@graphql-tools/load'
     */
    const getExpert = async(params: any, reduced?: boolean): TGetExpertServiceResponse => {
        const {slug} = params;
        try {
            const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                body: JSON.stringify({
                    query: ` query GetExperts ($slug: String) {
                        experts(where: {name: $slug}) {
                            nodes {
                                id
                                databaseId
                                title
                                excerpt
                                content
                                status
                                link
                                slug
                                uri
                                expertCountries {
                                    nodes {
                                        id
                                        databaseId
                                        name
                                        slug
                                        uri
                                    }
                                }
                                expertRegions {
                                    nodes {
                                        id
                                        databaseId
                                        name
                                        slug
                                        uri
                                    }
                                }
                                expertStates {
                                    nodes {
                                        id
                                        databaseId
                                        name
                                        slug
                                        uri
                                    }
                                }
                                featuredImage {
                                    node {
                                        id
                                        sourceUrl
                                    }
                                }
                                expertCustomFields{
                                    expertId
                                    inactiveExpert
                                    featureFlag
                                    topicClusters {
                                        ... on Page {
                                            title
                                            slug
                                            uri
                                            link
                                        }
                                    }
                                }
                                expertCategories {
                                    nodes {
                                      id
                                      databaseId
                                      name
                                      slug
                                    }
                                }
                                seo {
                                    title
                                    metaDesc
                                    canonical
                                    focuskw
                                    schema {
                                        raw
                                    }
                                }
                            }
                        }
                    }
                    `,
                    variables: {
                        slug
                    }
                })
            });
            if(!fetchedResult.ok)
                throw new Error(`Request to WP Rest-API as failed with response status: ${fetchedResult.status}`);
            // Check if the content type is JSON
            const contentType = fetchedResult.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json'))
                throw new TypeError(`The Content-Type is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);

            const {data: {experts:{nodes: experts}}} = await fetchedResult.json();
            const expert = (experts as TExpertFromGraphQL[])[0];
            if (expert) return new Success(_mapGqlToDto([expert])[0]);

            return new Failure(new NotFoundError({slug}));
        } catch(error: any) {
            console.error(`
                Error on ExpertService
                Method: getExpert
                Trace: ${error?.message ?? error}
            `);

            return new Failure(new InternalError({slug}));
        }
    };

    //TODO: Provides Types definition for Input and the Output.
    const getPaginatedExperts = async(params: any, reduced?: boolean) => {
        const {limit, afterCursor } = params;
        try {
            const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                body: JSON.stringify({
                    query: ` query GetExperts($first: Int, $afterCursor: String) {
                        experts(first: $first, after: $afterCursor) {
                            pageInfo {
                                endCursor
                                hasNextPage
                            }
                            nodes {
                                id
                                databaseId
                                title
                                excerpt
                                content
                                status
                                link
                                slug
                                uri
                                expertCountries {
                                    nodes {
                                        id
                                        databaseId
                                        name
                                        slug
                                        uri
                                    }
                                }
                                expertRegions {
                                    nodes {
                                        id
                                        databaseId
                                        name
                                        slug
                                        uri
                                    }
                                }
                                expertStates {
                                    nodes {
                                        id
                                        databaseId
                                        name
                                        slug
                                        uri
                                    }
                                }
                                featuredImage {
                                    node {
                                        id
                                        sourceUrl
                                    }
                                }
                                expertCustomFields{
                                    expertId
                                    inactiveExpert
                                }
                                expertCategories {
                                    nodes {
                                      id
                                      databaseId
                                      name
                                      slug
                                    }
                                }
                                seo {
                                    title
                                    metaDesc
                                    canonical
                                    focuskw
                                }
                            }
                        }
                    }
                    `,
                    variables: {
                        first: limit,
                        afterCursor,
                    }
                })
            });
            const {data: {experts:{nodes: experts, pageInfo}}} = await fetchedResult.json();
            const transactionalExperts = experts as TExpertFromGraphQL[];
            const {hasNextPage, endCursor} = pageInfo as TPageInfoFromGraphQL;
            const items = transactionalExperts[0] ? _mapGqlToDto(transactionalExperts) : [];

            return new Success({
                items,
                hasNextPage,
                endCursor
            });
        } catch(error: any) {
            console.error(`
                Error on ExpertService
                Method: getPaginatedExperts
                Trace: ${error?.message ?? error}
            `);

            return new Failure({
                items: [],
                hasNextPage: false,
                endCursor: null,
            });
        }
    };

    //TODO: Provides Types definition for the Output.
    const synchronizeAlgoliaExperts = async(algoliaHandler: TAlgoliaService): Promise<IResult<{indexes: TAlgoliaSearchExpertHit[]}>> => {
        try {
            // Get Algolia Indexed Experts
            let start = 0;
			let limit = 1000;
			let total = 1;
			const algoliaExperts: TAlgoliaSearchExpertHit[] = [];
			do {
				const response = await getIndexedExpertsPaginated({
                        limit,
                        start,
                    },
                    algoliaHandler,
                    false
                );
				const { value } = response.getResultProps();
				const {
					items,
					total: totalExperts,
					limit: paginationLimitIndex,
				} = value;
				total = totalExperts ?? 0;
				limit = paginationLimitIndex ?? 0;
				start+= items.length;
				if (items?.length) algoliaExperts.push(...items as TAlgoliaSearchExpertHit[]);

			} while (algoliaExperts.length < total);

            // Get WP Experts
            let hasNextPage = true;
            let afterCursor = null;
            const transactionalExperts: TExtendedExpertDto[] = [];
            do {
                const response = await getPaginatedExperts({limit: 10000, afterCursor});
                const { value } = response.getResultProps();
                const {
                    items,
                    hasNextPage: hasMorePages,
                    endCursor
                } = value;
                hasNextPage = hasMorePages;
                afterCursor = endCursor;
                if (items[0]) transactionalExperts.push(...items as TExtendedExpertDto[]);
            } while (hasNextPage);

            // Get the Updates for Algolia Indexes
            if(algoliaExperts[0] && transactionalExperts[0]) {
                const algoliaExpertsMap = algoliaExperts.reduce((acc, value) => {
                    acc.set(value.post_id!, value);

                    return acc;
                }, new Map<number, TAlgoliaSearchExpertHit>());

                const synchronizedIndexes = transactionalExperts.reduce<Array<TAlgoliaSearchExpertHit>>((acc, value) => {
                    const {dbId, inactiveExpert, id} = value;
                    const oldIndex = algoliaExpertsMap.get(dbId!);
                    if (oldIndex) {
                        const indexUpdate = {
                            ...oldIndex,
                            inactive_expert: inactiveExpert ?? false,
                            expert_id: id
                        } as TAlgoliaSearchExpertHit;
                        acc.push(indexUpdate);
                    }

                    return acc;
                }, []);

                const synchRes =  await algoliaHandler.saveIndex<TAlgoliaSearchExpertHit>({
                    indexRef: ALGOLIA_INDEXES.WP_POSTS_EXPERT,
                    indexes: synchronizedIndexes
                });
                const {isSuccess, value} = synchRes.getResultProps();
                if (isSuccess) return new Success({
                        indexes: (value as TAlgoliaSaveIndexResponse<TAlgoliaSearchExpertHit>).indexes
                    });
            }

            return new Failure({indexes: []});
		} catch (error: any) {
			console.error(`
                Error on CategoryService
                Method: synchronizeAlgoliaExperts
                Trace: ${error?.message ?? error}
            `);

            return new Failure({indexes: []});
		}
    };

    return {
        getIndexedExperts,
        getAllIndexedExperts,
        getIndexedExpertsPaginated,
        mapPermalinkToExpertId,
        synchronizeAlgoliaExperts,
        getExpert
    };
};