import Failure from '$lib/shared/controlled-response/result/failure';
import Success from '$lib/shared/controlled-response/result/success';
import type {
	IBaseControlledError,
	IResult
} from '$lib/shared/controlled-response/_types-interfaces';
import { ALGOLIA_FACETS_SIMPLE_REFS, ALGOLIA_INDEXES } from '../algolia/_constants';
import type { TAlgoliaSearchFacetValuesResponse, TAlgoliaSearchIndexResponse, TAlgoliaService } from '../algolia/_types-interfaces';
import type {
	TAlgoliaSearchCategoryHit,
	TAlgoliaSearchHierarchicalCategoryHit,
	TBaseExpertCategory,
	TCategoryService,
	TExtendedExpertCategory,
	TGetIndexedCategoriesPaginatedResponse,
	TSearchCategoriesServiceParams
} from './_types-interfaces';
import type {
	TExtendedCategoryDto,
	TReducedCategoryDto
} from '$lib/shared/api-dtos/internal-rest/category-dtos';
import { wpGraphQlApiEndpoint } from '$lib/utils/endpoints';
import type {
	QueryExpertCategoryBySlugQuery,
} from '$lib/shared/api-dtos/external-graphql/generated';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import type { TSeoFromGraphQL, TSeoService } from '../seo/_types-interfaces';
import { PUBLIC_SITE_URL } from '$env/static/public';
import type { TNestedUrlRef } from '../_shared/_types-interfaces';
import { CATEGORY_HIERARCHICAL_LEVEL_INDEX_MAP } from './_constants';
import type { TSeoBreadcrumb } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import type { ILogger, ILoggerContext } from '../logger/_types-interfaces';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import { LOGGER_ERROR_LEVEL } from '../logger/_constants';

export const CategoryService = <LC extends ILoggerContext>(algoliaHandler: TAlgoliaService, seoService: TSeoService, loggerService?: ILogger<LC>): TCategoryService => {
	// internal definitions
	const _mapIndexHitsToDto = (
		categories: TAlgoliaSearchCategoryHit[],
		reducedVersion?: boolean
	): TReducedCategoryDto[] | TExtendedCategoryDto[] => {
		return categories.reduce<TReducedCategoryDto[]>((acc, category) => {
			const { name, slug, permalink } = category;
			let mappedHit = {
				title: name,
				slug,
				link: permalink ?? ''
			} as TReducedCategoryDto;
			if(!reducedVersion) {
				const {description} = category;
				mappedHit = {
					...mappedHit,
					description
				} as TExtendedCategoryDto;
			}
			acc.push(mappedHit);

			return acc;
		}, []);
	};

	const _mapFacetHitsToDto = (
		facetValues: TAlgoliaSearchHierarchicalCategoryHit[],
		levelIfHierarchical?: number
	): TReducedCategoryDto[] => {
		return facetValues.reduce<TReducedCategoryDto[]>((acc, facet) => {
			const { value } = facet;
			let _value = value;
			if(value.includes('>')) {
				const valueSegments = value.split(' > ');
				_value = valueSegments[levelIfHierarchical ?? 0];
			}
			acc.push({
				title: _value,
				slug: _value
			});

			return acc;
		}, []);
	};

	const _mapBreadcrumbs = (breadcrumbs: TSeoBreadcrumb[] = []): TSeoBreadcrumb[] => {
		return breadcrumbs.map((breadcrumb) => {
			const { url } = breadcrumb;
			return {
				...breadcrumb,
				url: url.replace('/expert-category/', '/split-here//categories/').split('/split-here/')[1] ?? '/'
			};
		});
	}

	const _mapGqlToDto = (
		categories: TExtendedExpertCategory[] | TBaseExpertCategory[],
		reduced?: boolean,
		nestedUrlRef?: TNestedUrlRef
	): TReducedCategoryDto[] | TExtendedCategoryDto[] => {
		return categories.reduce<Array<TReducedCategoryDto | TExtendedCategoryDto>>((acc, value) => {
			const { name, slug, link, expertCategoryCustomSettings: baseExpertCategoryCustomSettings } = value as TBaseExpertCategory;
			const {parentPathName, searchParamsString} = nestedUrlRef ?? {parentPathName: '', searchParamsString: ''};
			const url = parentPathName ? `/${parentPathName}/${slug}` :
						`/categories/${slug}`;
			const urlWithSearchParams = `${url}${searchParamsString ?? ''}`;
			const imageUrl =  baseExpertCategoryCustomSettings?.ecCustomFieldsGroup?.ecCustomFeaturedImage?.sourceUrl ?? '';
			const hierarchicalLevel = baseExpertCategoryCustomSettings?.ecCustomFieldsGroup?.ecCustomHierarchicalLevel;

			const reducedCategory: TReducedCategoryDto = {
				title: name ?? '',
				slug: slug ?? '',
				link: urlWithSearchParams,
				image: imageUrl,
				hierarchicalLevel: hierarchicalLevel ? Number(hierarchicalLevel) : -1
			};
			if (reduced) acc.push(reducedCategory);
			else {
				const {
					description,
					children,
					expertCategoryCustomSettings: extendedExpertCategoryCustomSettings,
					seo
				} = value as TExtendedExpertCategory;
				// SEO is read from Custom Fields, not from default SEO field(see issue with WP All Import in Wordpress).
				const mappedSeo = seoService.mapSeoGqlToDto(
					{
						title: extendedExpertCategoryCustomSettings?.ecCustomFieldsGroup?.ecCustomSeoTitle,
						metaDesc: extendedExpertCategoryCustomSettings?.ecCustomFieldsGroup?.ecCustomSeoMetadescription ?? 
								  `${reducedCategory.title} as an expert witness category where experts can provide opinion and testify for your legal case.`,
						focuskw: extendedExpertCategoryCustomSettings?.ecCustomFieldsGroup?.ecCustomSeoFocusKeyphrase,
						canonical: extendedExpertCategoryCustomSettings?.ecCustomFieldsGroup?.ecCustomSeoCanonicalUrl,
						schema: seo?.schema,
						breadcrumbs: seo?.breadcrumbs
					} as TSeoFromGraphQL,
					urlWithSearchParams
				);
				const extendedCategory: TExtendedCategoryDto = {
					...reducedCategory,
					description: description ?? '',
					seo: {...mappedSeo},
					subCategories: children?.nodes?.[0] ?
									_mapGqlToDto(
										children.nodes,
										false,
										{parentPathName: `${PUBLIC_SITE_URL}${parentPathName}/${slug}`}
									) : [],
				};
				acc.push(extendedCategory);
			}
			return acc;
		}, []);
	};

	// exposed methods
	const getCategories = async (
		params: TSearchCategoriesServiceParams,
		reducedVersion?: boolean
	): Promise<IResult<TReducedCategoryDto[] | TExtendedCategoryDto[]>> => {
		try {
			const { limit, start } = params;
			const response = await algoliaHandler.searchIndex<TAlgoliaSearchCategoryHit>({
				indexRef: ALGOLIA_INDEXES.WP_TERMS_EXPERT_CATEGORY,
				offset: start ?? 0,
				length: limit ?? 1000
			});
			const { isSuccess, value } = response.getResultProps();

			if (isSuccess) {
				const { items, offset, ...rest } =
					value as TAlgoliaSearchIndexResponse<TAlgoliaSearchCategoryHit>;
				return new Success(_mapIndexHitsToDto(items, reducedVersion));
			}

			return new Failure([]);
		} catch (error: any) {
			console.error(`
                Error on CategoryService
                Method: getCategories
                Trace: ${error?.message ?? error}
            `);

			return new Failure([]);
		}
	};

	const getIndexedCategoriesPaginated = async (
		params: TSearchCategoriesServiceParams,
		reducedVersion?: boolean,
		mappedVersion?: boolean
	): Promise<IResult<TGetIndexedCategoriesPaginatedResponse>> => {
		const { limit = 1000, start = 0, nameFilter } = params;
		try {
			const response = await algoliaHandler.searchIndex<TAlgoliaSearchCategoryHit>({
				indexRef: ALGOLIA_INDEXES.WP_TERMS_EXPERT_CATEGORY,
				offset: start,
				length: limit,
				facets: {
					terms: {
						name: nameFilter
					}
				}
			});
			const { isSuccess, value } = response.getResultProps();

			if (isSuccess) {
				const { items, offset, length, total } =
					value as TAlgoliaSearchIndexResponse<TAlgoliaSearchCategoryHit>;
				return new Success({
					start: offset,
					limit: length,
					total,
					items: mappedVersion ? _mapIndexHitsToDto(items, reducedVersion): items
				});
			}

			return new Failure({
				total: 0,
				items: [],
				start: start,
				limit: length
			});
		} catch (error: any) {
			console.error(`
                Error on CategoryService
                Method: getIndexedCategoriesPaginated
                Trace: ${error?.message ?? error}
            `);

			return new Failure({
				total: 0,
				items: [],
				start: start,
				limit: length
			});
		}
	};

	const getAllIndexedCategories = async (
		params: TSearchCategoriesServiceParams,
		reducedVersion?: boolean
	): Promise<
		IResult<{
			total: number;
			items: TReducedCategoryDto[] | TExtendedCategoryDto[];
		}>
	> => {
		try {
			let start = 0;
			let limit = 1000;
			let total = 1;
			const categories: TReducedCategoryDto[] | TExtendedCategoryDto[] = [];

			do {
				const response = await getIndexedCategoriesPaginated({...params, limit, start}, reducedVersion, true);
				const { value } = response.getResultProps();
				const items = !reducedVersion ? value.items as TExtendedCategoryDto[]:  value.items as TReducedCategoryDto[];
				const { total: totalCategories, limit: paginationLimitIndex } = value;
				total = totalCategories ?? 0;
				limit = paginationLimitIndex ?? 0;
				start += items.length;
				if (items?.length) categories.push(...items);
			} while (categories.length < total);

			// sorted alphabetically by default
			const sortedCategories = categories.sort((a, b) => a.title.localeCompare(b.title));

			return new Success({ total: sortedCategories?.[0] ? total : 0, items: sortedCategories });
		} catch (error: any) {
			console.error(`
                Error on CategoryService
                Method: getAllIndexedCategories
                Trace: ${error?.message ?? error}
            `);

			return new Failure({
				total: 0,
				items: []
			});
		}
	};

	const getAllIndexedCategoriesAsFacet = async(
		hierarchicalLevel: ALGOLIA_FACETS_SIMPLE_REFS.CATEGORY_LVL0 | ALGOLIA_FACETS_SIMPLE_REFS.CATEGORY_LVL1 | ALGOLIA_FACETS_SIMPLE_REFS.CATEGORY_LVL2
	): Promise<
	IResult<{
		total: number;
		items: TReducedCategoryDto[] | TExtendedCategoryDto[];
	}>
> => {
		try {
			const response = await algoliaHandler.searchFacetValues<TAlgoliaSearchHierarchicalCategoryHit>({
				indexRef: ALGOLIA_INDEXES.WP_POSTS_EXPERT,
				facet: hierarchicalLevel
			});
			const { isSuccess, value } = response.getResultProps();
			if(isSuccess) {
				const {total, items} = value as TAlgoliaSearchFacetValuesResponse<TAlgoliaSearchHierarchicalCategoryHit>;
				return new Success({
					total,
					items: _mapFacetHitsToDto(items, CATEGORY_HIERARCHICAL_LEVEL_INDEX_MAP.get(hierarchicalLevel) ?? 0)
				})
			}
		} catch (error: any) {
			console.error(`
                Error on CategoryService
                Method: getAllIndexedCategoriesAsFacet
                Trace: ${error?.message ?? error}
            `);
		}
		
		return new Failure({
			total: 0,
			items: []
		})
	}

	const getRootCategories = async(): Promise<IResult<TReducedCategoryDto[]>> => {
		try {
			const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify({
					query: `
						query QueryRootExpertCategories {
							expertCategories(where: { parent: 0, hideEmpty: true}) {
								nodes {
									id
									name
									slug
									expertCategoryCustomSettings {
										ecCustomFieldsGroup {
											ecCustomPermalink {
												url
											}
											ecCustomFeaturedImage {
												uri
												mediaItemUrl
												sourceUrl
											}
										}
									}
								}
							}
						}
                    `
				})
			});
			if(!fetchedResult.ok)
                throw new Error(`Request to WP GraphQL-API as failed with response status: ${fetchedResult.status}`);
			// Check if the content type is JSON
			const contentType = fetchedResult.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json'))
                throw new TypeError(`The Content-Type returned from WP GraphQL-API is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);
			// Extract data
			const result = await fetchedResult.json();
			const categories = result.data?.expertCategories?.nodes ?? [];

			return new Success(_mapGqlToDto(categories, true));
		} catch (error: any) {
			const caughtError = new InternalError(error, error?.message ?? error);
			const errorMessage = caughtError.getMessage();
			// @TODO: Make LoggerService a mandatory parameter to pass for all the CategoryService's consumers
            loggerService?.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: errorMessage,
                    scope: {
                        component: 'CategoryService',
                        action: 'getRootCategories'
                    }
                } as LC
            );
			// @TODO: Printing on Console should be an option to execute as part of the LoggerService procedure
			console.error(`
				Error in CategoryService
				Method: getRootCategories
				Trace: ${errorMessage}
			`);

			return new Failure([]);
		}
	};

	const getCategoryBySlug = async (
		slug: string
	): Promise<
		IResult<TReducedCategoryDto | TExtendedCategoryDto | IBaseControlledError<string>>
	> => {
		try {
			const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify({
					query: `
					query QueryExpertCategoryBySlug($slug: [String]) {
						expertCategories(where: { slug: $slug }) {
							nodes {
								id
								name
								uri
								description
								slug
								link
								expertCategoryCustomSettings {
									ecCustomFieldsGroup {
										ecCustomPermalink {
											url
										}
										ecCustomFeaturedImage {
											uri
											mediaItemUrl
											sourceUrl
										}
										ecCustomSeoTitle
										ecCustomSeoSlug
										ecCustomSeoMetadescription
										ecCustomSeoFocusKeyphrase
										ecCustomSeoCanonicalUrl
									}
								}
								children(first:0, last: 100) {
									nodes {
										id
										name
										uri
										description
										slug
										link
										expertCategoryCustomSettings {
											ecCustomFieldsGroup {
												ecCustomPermalink {
													url
												}
												ecCustomFeaturedImage {
													uri
													mediaItemUrl
													sourceUrl
												}
												ecCustomSeoTitle
												ecCustomSeoSlug
												ecCustomSeoMetadescription
												ecCustomSeoFocusKeyphrase
												ecCustomSeoCanonicalUrl
											}
										}
									}
								}
							}
						}
					}
					`,
					variables: {
						slug: [slug]
					}
				})
			});

			const result = (((await fetchedResult.json()).data as QueryExpertCategoryBySlugQuery)
				.expertCategories?.nodes ?? []) as TExtendedExpertCategory[];

			return result[0]
				? new Success(_mapGqlToDto(result)[0])
				: new Failure(new NotFoundError(slug));
		} catch (error: any) {
			console.error(`
				Error on CategoryService
				Method: getCategoryBySlug
				Trace: ${error?.message ?? error}
			`);
			return new Failure({} as TExtendedCategoryDto);
		}
	};

	//@tTODO: Refactor this by splitting into two separated queries(Category and Subcategory)
	const getCategoryByPageNumber = async (
		slug: string,
		pageNumber = 1,
		pageSize = 10,
		nestedUrlRef?: TNestedUrlRef
	): Promise<
		IResult<TReducedCategoryDto[] | TExtendedCategoryDto | IBaseControlledError<string>>
	> => {
		try {
			// 1- Get the category data
			const fetchedResult = await fetch(wpGraphQlApiEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify({
					query: `
					query QueryExpertCategoryBySlug($slug: [String]) {
						expertCategories(where: { slug: $slug }) {
							nodes {
								id
								name
								description
								slug
								link
								expertCategoryCustomSettings {
									ecCustomFieldsGroup {
										ecCustomPermalink {
											url
										}
										ecCustomFeaturedImage {
											uri
											mediaItemUrl
											sourceUrl
										}
										ecCustomHierarchicalLevel
										ecCustomSeoTitle
										ecCustomSeoSlug
										ecCustomSeoMetadescription
										ecCustomSeoFocusKeyphrase
										ecCustomSeoCanonicalUrl
									}
								}
								seo {
									schema {
										raw
									}
									breadcrumbs {
										text
										url
									}
								}
								parent {
									node {
										id
										name
										slug
										link
										expertCategoryCustomSettings {
											ecCustomFieldsGroup {
												ecCustomHierarchicalLevel
											}
										}
									}
								}	
							}
						}
					}
					`,
					variables: {
						slug: [slug]
					}
				})
			});
			if(!fetchedResult.ok)
                throw new Error(`Request to WP GraphQL-API as failed with response status: ${fetchedResult.status}`);
			// Check if the content type is JSON
            const contentType = fetchedResult.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json'))
                throw new TypeError(`The Content-Type returned from WP GraphQL-API is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);
			// Extract data
			const result = await fetchedResult.json();
			const _category = (result.data?.expertCategories?.nodes as TBaseExpertCategory[])?.[0];
			if (!_category) {
				return new Failure(new NotFoundError(slug));
			}

			// 2- Get all category's children recursively
			let allChildren: any[] = [];
			let hasNextPage = true;
			let endCursor = null;
			// fetch children with GraphQL
			const fetchBatch = async (cursor: string | null) => {
				const response = await fetch(wpGraphQlApiEndpoint, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Accept: 'application/json'
					},
					body: JSON.stringify({
						query: `
						query QueryExpertCategoryBySlug($slug: [String], $first: Int, $after: String) {
							expertCategories(where: { slug: $slug }) {
								nodes {
									children(first: $first, after: $after) {
										nodes {
											id
											name
											slug
											link
											expertCategoryCustomSettings {
												ecCustomFieldsGroup {
													ecCustomPermalink {
														url
													}
													ecCustomFeaturedImage {
														uri
														mediaItemUrl
														sourceUrl
													}
												}
											}
										}
										pageInfo {
											hasNextPage
											endCursor
										}
									}
								}
							}
						}
						`,
						variables: {
							slug: [slug],
							first: 80, // Fetch in batches of 80
							after: cursor
						}
					})
				});
				if(!response.ok)
					throw new Error(`Request to WP GraphQL-API as failed with response status: ${response.status}`);
				// Check if the content type is JSON
				const contentType = response.headers.get('content-type');
				if (!contentType || !contentType.includes('application/json'))
					throw new TypeError(`The Content-Type returned from WP GraphQL-API is not the expected one. Got '${contentType}' instead of 'application/json'(JSON)!`);
				// Extract data
				const result = await response.json();
				const categoryNodes = result.data?.expertCategories?.nodes ?? [];
				const children = categoryNodes[0]?.children?.nodes ?? [];
				const pageInfo = categoryNodes[0]?.children?.pageInfo;

				allChildren = [...allChildren, ...children];
				hasNextPage = pageInfo?.hasNextPage;
				endCursor = pageInfo?.endCursor;
			};
			// call to fetch all children iteratively
			while (hasNextPage) {
				await fetchBatch(endCursor);
			}
			// paginate the results
			const paginatedChildren = allChildren.slice(
				(pageNumber - 1) * pageSize,
				pageNumber * pageSize
			);

			// 3- Maps to DTO the results
			const {parent, ...category} = _category;
			const mappedCategory = _mapGqlToDto([category], false, nestedUrlRef)[0] as TExtendedCategoryDto;
			const mappedSubCategories = _mapGqlToDto(
				paginatedChildren, true
			);
			const mappedParent = parent?.node ? _mapGqlToDto([parent.node], true)[0] : null;
			const enableNextPage = allChildren.length > pageNumber * pageSize;
			const enablePreviousPage = pageNumber > 1;

			return new Success({
				...mappedCategory,
				subCategories: mappedSubCategories,
				parent: mappedParent,
				pageInfo: {
					hasNextPage: enableNextPage,
					hasPreviousPage: enablePreviousPage,
					endCursor: endCursor
				}
			});
		} catch (error: any) {
			const caughtError = new InternalError(error, error?.message ?? error);
			const errorMessage = caughtError.getMessage();
			// @TODO: Make LoggerService a mandatory parameter to pass for all the CategoryService's consumers
            loggerService?.log(
                caughtError,
                LOGGER_ERROR_LEVEL.ERROR,
                {
                    data: caughtError.getControlledErrorReference() as unknown,
                    message: errorMessage,
                    scope: {
                        component: 'CategoryService',
                        action: 'getCategoryByPageNumber'
                    }
                } as LC
            );
			// @TODO: Printing on Console should be an option to execute as part of the LoggerService procedure
			console.error(`
				Error in CategoryService
				Method: getCategoryByPageNumber
				Trace: ${errorMessage}
			`);

			return new Failure(caughtError);
		}
	};

	return {
		getCategories,
		getRootCategories,
		getCategoryBySlug,
		getCategoryByPageNumber,
		getAllIndexedCategories,
		getAllIndexedCategoriesAsFacet
	};
};
