import type {
	BaseExpertCategoryFragment,
	ExtendedExpertCategoryFragment
} from '$lib/shared/api-dtos/external-graphql/generated';
import type {
	TExtendedCategoryDto,
	TReducedCategoryDto
} from '$lib/shared/api-dtos/internal-rest/category-dtos';
import type {
	IBaseControlledError,
	IResult
} from '$lib/shared/controlled-response/_types-interfaces';
import { TNestedUrlRef } from '../_shared/_types-interfaces';
import { ALGOLIA_FACETS_SIMPLE_REFS } from '../algolia/_constants';

export type TAlgoliaSearchCategoryHit = {
	readonly term_id: number;
	readonly taxonomy: string;
	readonly name: string;
	readonly description: string;
	readonly slug: string;
	readonly permalink: string;
};

export type TAlgoliaSearchHierarchicalCategoryHit = {
	readonly value: string;
};

export type TSearchCategoriesServiceParams = {
	limit?: number;
	start?: number;
	nameFilter?: string;
};

export type TGetIndexedCategoriesPaginatedResponse = {
	total: number;
	items: TReducedCategoryDto[] | TExtendedCategoryDto[] | TAlgoliaSearchCategoryHit[];
	start: number;
	limit: number;
};

export type TCategoryService = {
	getCategories: (
		params: TSearchCategoriesServiceParams,
		reducedVersion?: boolean
	) => Promise<IResult<TReducedCategoryDto[] | TExtendedCategoryDto[]>>;
	getAllIndexedCategories: (params: TSearchCategoriesServiceParams, reducedVersion?: boolean) => Promise<
		IResult<{
			total: number;
			items: TReducedCategoryDto[] | TExtendedCategoryDto[];
		}>
	>;
	getRootCategories: () => Promise<IResult<TReducedCategoryDto[]>>;
	getCategoryBySlug: (
		slug: string
	) => Promise<IResult<TReducedCategoryDto | TExtendedCategoryDto | IBaseControlledError<string>>>;
	getCategoryByPageNumber: (
		slug: string,
		pageNumber: number,
		pageSize?: number,
		nestedUrlRefs?: TNestedUrlRef
	) => Promise<
		IResult<TReducedCategoryDto[] | TExtendedCategoryDto | IBaseControlledError<string>>
	>;
	getAllIndexedCategoriesAsFacet: (
		hierarchicalLevel: ALGOLIA_FACETS_SIMPLE_REFS.CATEGORY_LVL0 | ALGOLIA_FACETS_SIMPLE_REFS.CATEGORY_LVL1 | ALGOLIA_FACETS_SIMPLE_REFS.CATEGORY_LVL2,
		reducedVersion?: boolean
	) => Promise<
	IResult<{
		total: number;
		items: TReducedCategoryDto[] | TExtendedCategoryDto[];
	}>
>;
};

export type TBaseExpertCategory = BaseExpertCategoryFragment;
export type TExtendedExpertCategory = ExtendedExpertCategoryFragment;
