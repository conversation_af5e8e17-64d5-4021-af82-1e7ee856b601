# BASE WP ELEMENT FRAGMENT
# Media Base Fragments
fragment baseFeatureImage on MediaItem {
	id
	uri
	mediaItemUrl
	sourceUrl
}

# Custom Fields Base Fragments
fragment baseExpertCustomFields on Expert_Expertcustomfields {
	expertId
	inactiveExpert
	featureFlag
	topicClusters {
		... on Page {
			title
			slug
			uri
			link
		}
	}
}

fragment baseAcfCustomLink on AcfLink {
	target
	title
	url
}

fragment baseExpertCategoryCustomFieldsGroup on ExpertCategory_Expertcategorycustomsettings_EcCustomFieldsGroup {
	ecCustomPermalink {
		...baseAcfCustomLink
	},
	ecCustomFeaturedImage {
		...baseFeatureImage
	}
	ecCustomHierarchicalLevel
}

# Seo Base Fragments
fragment baseSeoPostTypeFragment on PostTypeSEO {
	title
	metaDesc
	canonical
	focuskw
}

fragment baseSeoRedirectFragment on SEORedirect {
	origin
	target
	type
}

fragment baseSeoTaxTypeFragment on TaxonomySEO {
	title
	metaDesc
	canonical
	focuskw
}

# Expert Base Fragments - TODO: Remove this fragment and replace it with baseExpertCategory
fragment baseExpertCategoryFragment on ExpertCategory {
	id
	databaseId
	name
	slug
	uri
}

fragment baseExpertGroupFragment on ExpertGroup {
	id
	databaseId
	name
	slug
	uri
}

fragment baseExpertCountryFragment on ExpertCountry {
	id
	databaseId
	name
	slug
	uri
	link
}

fragment baseExpertRegionFragment on ExpertRegion {
	id
	databaseId
	name
	slug
	uri
	link
}

fragment baseExpertStateFragment on ExpertState {
	id
	databaseId
	name
	slug
	uri
	link
}

fragment baseExpertFragment on Expert {
	id
	title
	excerpt
	status
	link
	slug
}

fragment baseExpertCategory on ExpertCategory {
	id
	name
	description
	slug
	link
	expertCategoryCustomSettings {
		ecCustomFieldsGroup {
			...baseExpertCategoryCustomFieldsGroup
		}
	}
	parent {
		node {
			id
			name
			slug
			link
			expertCategoryCustomSettings {
				ecCustomFieldsGroup {
					ecCustomHierarchicalLevel
				}
			}
		}
	}
}

# Page Base Fragments
fragment basePageFragment on Page {
	id
	databaseId
	title
	content
	slug
}

# Pagination Base Fragments
fragment basePageInfoFragment on PageInfo {
	endCursor
	hasNextPage
}

# EXTENDED FRAGMENTS
# Custom Fields Base Fragments
fragment extendedExpertCategoryCustomFieldsGroup on ExpertCategory_Expertcategorycustomsettings_EcCustomFieldsGroup {
	...baseExpertCategoryCustomFieldsGroup
	ecCustomSeoTitle
	ecCustomSeoSlug
	ecCustomSeoMetadescription
	ecCustomSeoFocusKeyphrase
	ecCustomSeoCanonicalUrl
}

# Seo Extended Fragments
fragment extendedSeoFragment on PostTypeSEO {
	...baseSeoPostTypeFragment
	metaKeywords
	schema {
		raw
	}
}

fragment extendedSeoTaxTypeFragment on TaxonomySEO {
	...baseSeoTaxTypeFragment
	metaKeywords
	schema {
		raw
	}
	breadcrumbs {
		text
		url
	}
}

# Expert Extended Fragments
fragment extendedExpertFragment on Expert {
	...baseExpertFragment
	databaseId
	content
	uri
	link
	excerpt
	expertCountries {
		nodes {
			...baseExpertCountryFragment
		}
	}
	expertRegions {
		nodes {
			...baseExpertRegionFragment
		}
	}
	expertStates {
		nodes {
			...baseExpertStateFragment
		}
	}
	featuredImage {
		node {
			...baseFeatureImage
		}
	}
	expertCustomFields {
		...baseExpertCustomFields
	}
	expertCategories {
		nodes {
			...baseExpertCategoryFragment
		}
	}
	seo {
		...extendedSeoFragment
	}
}

# Page Extended Fragments
fragment extendedPageFragment on Page {
	...basePageFragment
	link
	author {
		node {
			id
			email
		}
	}
	contentTypeName
	featuredImage {
		node {
			mediaItemUrl
			altText
			caption
		}
	}
	seo {
		...extendedSeoFragment
		breadcrumbs {
			text
			url
		}
	}
	pageCustomFields {
		pageType
		algoliaSearchKeyword
		subtitle
	}
}

fragment homePageFragment on Page {
	seo {
		canonical
		metaDesc
		metaKeywords
		title
		focuskw
	}
	homepageCustomFields {
		components {
			... on Page_Homepagecustomfields_Components_Banner {
				__typename
				title
				subtitle
				description
				backgroundImage {
					title
					sourceUrl
				}
			}
			... on Page_Homepagecustomfields_Components_LogoSlider {
				__typename
				title
				logoList {
					logo {
						title
						altText
						sourceUrl
					}
				}
			}
			... on Page_Homepagecustomfields_Components_FindResults {
				__typename
				description
				title
				searchOnlineText
				caseSupported {
					caseCopy
					caseCount
				}
				qualifiedExperts {
					expertsCopy
					expertsCount
				}
				searchOnlineImage {
					title
					sourceUrl
				}
			}
			... on Page_Homepagecustomfields_Components_Categories {
				title
				cta {
					url
					title
				}
			}
			... on Page_Homepagecustomfields_Components_Testimonial {
				title
			}
			... on Page_Homepagecustomfields_Components_WinningSecret {
				title
				features {
					description
					title
					icon {
						sourceUrl
						title
					}
				}
			}
			... on Page_Homepagecustomfields_Components_FindWitnessFree {
				title
				findSteps {
					description
					title
					icon {
						sourceUrl
						title
					}
				}
			}
			... on Page_Homepagecustomfields_Components_HowReferralsWork {
				title
				referralSteps {
					description
				}
				media {
					mediaType
					title
					sourceUrl
					mediaDetails {
						height
						width
					}
				}
			}
			... on Page_Homepagecustomfields_Components_FeatureStrip {
				description
				title
				cta {
					title
					url
				}
				media {
					mediaType
					title
					sourceUrl
					mediaDetails {
						height
						width
					}
				}
			}
			... on Page_Homepagecustomfields_Components_RequestExpertForm {
				description
				cta {
					title
					url
				}
				title
				infoCopy
			}
		}
	}
}

fragment blogPageFragment on Page {
	link
	seo {
		...extendedSeoFragment
	}
	blogPageCustomFields {
		subheading
		description
		components {
			... on Page_Blogpagecustomfields_Components_FeaturedBlogs {
				blogs {
					... on Post {
						...CorePost
					}
				}
			}
		}
	}
}

# Expert Category Extended Fragment
fragment extendedExpertCategory on ExpertCategory {
	...baseExpertCategory
	expertCategoryCustomSettings {
		ecCustomFieldsGroup {
			...extendedExpertCategoryCustomFieldsGroup
		}
	}
	# TODO: Remove SEO fields. SEO it beings read from Custom Fields
	seo {
		...baseSeoTaxTypeFragment
		schema {
          raw
        }
		breadcrumbs {
          text
          url
        }
	}
	children {
		nodes {
			...baseExpertCategory
			expertCategoryCustomSettings {
				ecCustomFieldsGroup {
					...extendedExpertCategoryCustomFieldsGroup
				}
			}
			# TODO: Remove SEO fields. SEO it beings read from Custom Fields
			seo {
				...baseSeoTaxTypeFragment
			}
		}
		pageInfo {
			hasPreviousPage
			hasNextPage
			endCursor
		}
	}
}

# Expert Location Extended Fragment (the same for ExpertCountry, ExpertRegion and ExpertStates)
fragment extendedExpertCountry on ExpertCountry {
	...baseExpertCountryFragment,
	description,
	seo {
		...extendedSeoTaxTypeFragment
	},
	taxonomyName
}

fragment extendedExpertRegion on ExpertRegion {
	...baseExpertRegionFragment,
	description,
	seo {
		...extendedSeoTaxTypeFragment
	},
	taxonomyName
}

fragment extendedExpertState on ExpertState {
	...baseExpertStateFragment,
	description,
	seo {
		...extendedSeoTaxTypeFragment
	},
	taxonomyName
}


fragment SeoCore on PostTypeSEO {
	...extendedSeoFragment
	opengraphImage {
		id
		sourceUrl
		description
	}
}

fragment LimitedAuthor on User {
	slug
	name
	databaseId
	firstName
	lastName
	avatar {
		url
	}
}

fragment CorePost on Post {
	content
	isPreview
	slug
	excerpt
	date
	link
	seo {
		...SeoCore
	}
	title
	featuredImage {
		node {
			sourceUrl
			description
			id
		}
	}
	categories(first: 10) {
		nodes {
			...CoreCategory
		}
	}
	tags(first: 10) {
		nodes {
			name
			slug
		}
	}
	author {
		node {
			...LimitedAuthor
			authorCustomFields {
				... on User_Authorcustomfields {
					bio
				}
			}
		}
	}
}

fragment CoreCategory on Category {
	description
	name
	slug
	id
	link
	seo {
		title
		metaDesc
		canonical
		focuskw
	}
}

fragment CorePageInfo on PageInfo {
	startCursor
	endCursor
	hasNextPage
	hasPreviousPage
}

fragment CoreSubdirectoryPage on Page {
	id
	slug
	title
	pageCustomFields {
		pageType
		algoliaSearchKeyword
	}
	author {
		node {
			email
		}
	}
	featuredImage {
		node {
			sourceUrl
		}
	}
	seo {
		...SeoCore
	}
}
