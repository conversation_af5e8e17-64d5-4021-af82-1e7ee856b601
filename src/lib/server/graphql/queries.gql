# import "./fragments.gql"

# EXPERTS
# TODO: This query can be modified to accept/return pagination references.
query QueryExperts(
	$expertCPId: Int
	$slug: String
	$expertId: String
	$limit: Int
	$afterCursor: String
) {
	experts(first: $limit, after: $afterCursor, where: { name: $slug }) {
		pageInfo {
			...basePageInfoFragment
		}
		nodes {
			...extendedExpertFragment
		}
	}
}

query QueryUniqueExpert($id: ID!) {
	expert(id: $id) {
		...extendedExpertFragment
	}
}

# EXPERT CATEGORIES
query QueryRootExpertCategories {
	expertCategories(where: { parent: 0, hideEmpty: true}) {
		nodes {
			id
			name
			slug
			expertCategoryCustomSettings {
				ecCustomFieldsGroup {
					...baseExpertCategoryCustomFieldsGroup
				}
			}
		}
	}
}

query QueryExpertCategoryBySlug($slug: [String]) {
	expertCategories(where: { slug: $slug }) {
		nodes {
			...extendedExpertCategory
		}
	}
}

# PAGES
query QueryPageBySlug($slug: String) {
	pages(where: { name: $slug }) {
		nodes {
			...extendedPageFragment
		}
	}
}

query QueryBlogPage($slug: String) {
	pages(where: { name: $slug }) {
		nodes {
			...blogPageFragment
		}
	}
}

query AllPages($after: String, $before: String, $first: Int, $last: Int) {
	pages(after: $after, before: $before, first: $first, last: $last) {
		nodes {
			...CoreSubdirectoryPage
		}
		pageInfo {
			...CorePageInfo
		}
	}
}

# HOME PAGE
query QueryHomePageBySlug($slug: String) {
	pages(where: { name: $slug }) {
		nodes {
			...homePageFragment
		}
	}
}

# SEO
query QuerySEOData {
	seo {
		redirects {
			...baseSeoRedirectFragment
		}
	}
}

# BLOG POSTS
query GetPosts($afterCursor: String, $first: Int, $last: Int, $beforeCursor: String) {
	posts(first: $first, last: $last, after: $afterCursor, before: $beforeCursor) {
		pageInfo {
			...CorePageInfo
		}
		nodes {
			...CorePost
		}
	}
}

query GetPost($slug: ID!, $preview: Boolean) {
	post(id: $slug, idType: SLUG, asPreview: $preview) {
		...CorePost
	}
}

# USERS
query GetUserPosts($first: Int, $author: Int) {
	posts(first: $first, where: { author: $author, orderby: { field: DATE, order: DESC } }) {
		pageInfo {
			...CorePageInfo
		}
		nodes {
			...CorePost
		}
	}
}

query GetUser($slug: String) {
	users(where: { search: $slug }, first: 1) {
		nodes {
			...LimitedAuthor
			seo {
				canonical
				title
				metaDesc
			}
			authorCustomFields {
				... on User_Authorcustomfields {
					bio
					companyName
					companyLink
					expertise {
						... on User_Authorcustomfields_expertise {
							detail
						}
					}
					education {
						... on User_Authorcustomfields_education {
							detail
						}
					}
					featuredArticles {
						... on Post {
							title
							slug
						}
					}
					articleTopics {
						... on User_Authorcustomfields_articleTopics {
							topic {
								slug
								name
							}
						}
					}
					authorLinks {
						... on User_Authorcustomfields_authorLinks {
							name
							link
						}
					}
					authorFeatured {
						... on User_Authorcustomfields_authorFeatured {
							name
							link
						}
					}
					socials {
						... on User_Authorcustomfields_socials {
							icon
							profileUrl
						}
					}
				}
			}
		}
	}
}

query SearchUser($email: String) {
	users(where: { search: $email }, first: 1) {
		nodes {
			...LimitedAuthor
		}
	}
}

# POST CATEGORY
query GetCategoryFeaturedPosts($first: Int, $category: String, $tag: String) {
	posts(first: $first, where: { categoryName: $category, tag: $tag }) {
		nodes {
			...CorePost
		}
	}
}

query GetCategoryLatestPosts($first: Int, $category: String) {
	posts(first: $first, where: { categoryName: $category, orderby: { field: DATE, order: DESC } }) {
		nodes {
			...CorePost
		}
	}
}

query GetCategoryPosts(
	$afterCursor: String
	$first: Int
	$last: Int
	$beforeCursor: String
	$category: String
) {
	posts(
		after: $afterCursor
		before: $beforeCursor
		first: $first
		last: $last
		where: { categoryName: $category }
	) {
		pageInfo {
			...CorePageInfo
		}
		nodes {
			...CorePost
		}
	}
}

query GetCategory($slug: [String]) {
	categories(where: { slug: $slug }, first: 1) {
		nodes {
			...CoreCategory
			databaseId
			blogPageCustomFields {
				subheading
				description
			}
		}
	}
}

query GetCategories($afterCursor: String, $first: Int, $last: Int, $beforeCursor: String) {
	categories(after: $afterCursor, before: $beforeCursor, first: $first, last: $last) {
		pageInfo {
			...CorePageInfo
		}
		nodes {
			...CoreCategory
		}
	}
}

# POST TAGS
query GetTagPosts(
	$afterCursor: String
	$first: Int
	$last: Int
	$beforeCursor: String
	$tag: String
) {
	posts(
		after: $afterCursor
		before: $beforeCursor
		first: $first
		last: $last
		where: { tag: $tag }
	) {
		pageInfo {
			...CorePageInfo
		}
		nodes {
			...CorePost
		}
	}
}

# EXPERT LOCATION
query GetExpertCountry($slug: [String]) {
	expertCountries(where: { slug: $slug }) {
		nodes {
			...extendedExpertCountry
    	}
  	}
}

query GetExpertRegion($slug: [String]) {
	expertRegions(where: { slug: $slug }) {
		nodes {
			...extendedExpertRegion
    	}
  	}
}

query GetExpertState($slug: [String]) {
	expertStates(where: { slug: $slug }) {
		nodes {
			...extendedExpertState
    	}
  	}
}
