import type { TGetKeywordContentResponse } from "$lib/shared/api-dtos/internal-rest/schema";

export type TContentServiceClient = {
    getSearchPageUnifiedContent: (keyword?: string | null, category?: string | null) => Promise<string | null>;
}

export const ContentServiceClient = (): TContentServiceClient => {
    const endpointUrl = '/api/content';

    const getSearchPageUnifiedContent = async(keyword?: string | null, category?: string | null): Promise<string | null> => {
        let content = null;
        try {
            const response = await fetch(`${endpointUrl}`, {
                method: "POST",
                body: JSON.stringify({keyword, category}),
                headers: {
                    contentType: "application/json"
                }
            });
            const decodedResponse = await response.json() as TGetKeywordContentResponse;
            const {values} =  decodedResponse;

            content = values;
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             * */
        }

        return content;
    }

    return {
        getSearchPageUnifiedContent
    };
}