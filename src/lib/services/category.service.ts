import type { TReducedCategoryDto } from "$lib/shared/api-dtos/internal-rest/category-dtos";
import type { TGetCategoriesResponse } from "$lib/shared/api-dtos/internal-rest/schema";


export type TCategoryServiceClient = {
    getCategories: () => Promise<TReducedCategoryDto[]>;
    getCategoriesByLevel: (level: 'lvl0' | 'lvl1' | 'lvl2') => Promise<TReducedCategoryDto[]>;
}

export const CategoryServiceClient = () => {

    const getCategories = async(): Promise<TReducedCategoryDto[]> => {
        try {
            const response = await fetch('/api/category');
            const decodedResponse = await response.json() as TGetCategoriesResponse;
            const {values} =  decodedResponse;
            
            return values as TReducedCategoryDto[];
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             */

            return [];
        }
    };

    const getCategoriesByLevel = async(level: 'lvl0' | 'lvl1' | 'lvl2') => {
        try {
            const response = await fetch(`/api/facets/expert-category?level=${level}`);
            const decodedResponse = await response.json() as TGetCategoriesResponse;
            const {values} =  decodedResponse;
            return values as TReducedCategoryDto[];
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             */

            return [];
        }
    }

    return {
        getCategories,
        getCategoriesByLevel
    };
}