import type { TReducedExpertDto, TExtendedExpertDto } from "$lib/shared/api-dtos/internal-rest/expert-dtos";
import type { TSearchExpertResponse } from "$lib/shared/api-dtos/internal-rest/schema";

export type TSearchExpertParams = {
    start: number;
    limit: number;
    criteria?: string | null;
    category?: string[] | null; 
    country?: string[] | null;
    region?: string[] | null;  
    state?: string[] | null;
    expertNumber?: string | null;
    reduced?: boolean | null;
    exactMatch?: boolean | null;
    includeInactive?: boolean;
};

export type TSearchExpertsResponse = {
    total: number;
    items: TReducedExpertDto[] | TExtendedExpertDto[];
}

export type TExpertServiceClient = {
    searchExperts: (params: TSearchExpertParams) => Promise<TSearchExpertsResponse>;
}

export const ExpertServiceClient = () => {
    const endpointUrl = '/api/experts';

    const searchExperts = async(params: TSearchExpertParams): Promise<TSearchExpertsResponse> => {
        try {
            const response = await fetch(`${endpointUrl}`, {
                method: "POST",
                body: JSON.stringify(params),
                headers: {
                    contentType: "application/json"
                }
            });
            const decodedResponse = await response.json() as TSearchExpertResponse;
            const {values: {items, total}} =  decodedResponse;

            return { items, total};
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             */

            return {items: [], total: 0};
        }
    };

    return {
        searchExperts
    };
}