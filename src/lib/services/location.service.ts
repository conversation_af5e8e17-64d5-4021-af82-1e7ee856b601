import type { TExtendedCountryDto, TExtendedRegionDto, TExtendedStateDto, TReducedCountryDto, TReducedRegionDto, TReducedStateDto } from "$lib/shared/api-dtos/internal-rest/location-dtos";
import type { TGetCountriesResponse, TGetRegionsResponse, TGetStatesResponse } from "$lib/shared/api-dtos/internal-rest/schema";


export type TLocationServiceClient = {
    getCountries: () => Promise<TReducedCountryDto[]>;
    getRegions: () => Promise<TReducedRegionDto[]>;
    getStates: () => Promise<TReducedStateDto[]>;
}

export const LocationServiceClient = (): TLocationServiceClient => {

    const getCountries = async(reduced = false): Promise<TReducedCountryDto[]> => {
        try {
            const response = await fetch(`/api/location?type=countries&reduced=${reduced}`);
            const decodedResponse = await response.json() as TGetCountriesResponse;
            const {values} =  decodedResponse;
            
            return (reduced ? values as TReducedCountryDto[] 
                            :  values as TExtendedCountryDto[]);
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             */

            return [];
        }
    };

    const getRegions = async(reduced = false): Promise<TReducedRegionDto[]> => {
        try {
            const response = await fetch(`/api/location?type=regions&reduced=${reduced}`);
            const decodedResponse = await response.json() as TGetRegionsResponse;
            const {values} =  decodedResponse;
            
            return (reduced ? values as TReducedRegionDto[] 
                            :  values as TExtendedRegionDto[]);
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             */

            return [];
        }
    };

    const getStates = async(reduced = false): Promise<TReducedStateDto[]> => {
        try {
            const response = await fetch(`/api/location?type=states&reduced=${reduced}`);
            const decodedResponse = await response.json() as TGetStatesResponse;
            const {values} =  decodedResponse;
            
            return (reduced ? values as TReducedStateDto[] 
                            :  values as TExtendedStateDto[]);
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             */

            return [];
        }
    };

    return {
        getCountries,
        getRegions,
        getStates
    };
}