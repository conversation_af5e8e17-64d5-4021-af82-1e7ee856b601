import type { TReducedPostDto, TExtendedPostDto } from "$lib/shared/api-dtos/internal-rest/post-dtos";
import type { TSearchExpertResponse } from "$lib/shared/api-dtos/internal-rest/schema";

export type TSearchPostParams = {
    start: number;
    limit: number;
    criteria?: string | null;
    category?: string[] | null;
    reduced?: boolean | null;
    exactMatch?: boolean | null;
};

export type TSearchPostsResponse = {
    total: number;
    items: TReducedPostDto[] | TExtendedPostDto[];
}

export type TPostServiceClient = {
    searchPosts: (params: TSearchPostParams) => Promise<TSearchPostsResponse>;
}

export const PostServiceClient = () => {
    const endpointUrl = '/api/posts';

    const searchPosts = async (params: TSearchPostParams): Promise<TSearchPostsResponse> => {
        try {
            const response = await fetch(`${endpointUrl}`, {
                method: "POST",
                body: JSON.stringify(params),
                headers: {
                    contentType: "application/json"
                }
            });
            const decodedResponse = await response.json() as TSearchExpertResponse;
            const { values: { items, total } } = decodedResponse;

            return { items, total };
        } catch (error) {
            /**
             * TODO: Log error here and handle it accordingly.
             */

            return { items: [], total: 0 };
        }
    };

    return {
        searchPosts
    };
}