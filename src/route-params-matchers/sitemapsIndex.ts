import type { ParamMatcher } from '@sveltejs/kit';

export const match = ((param: string): param is (
	'experts' | 
	'expert-categories' | 
	'keywords' | 
	'blog-posts' | 
	'blog-categories' | 
	'expert-locations' |
	'static'
) => {
	return param === 'experts' || param === 'expert-categories' || param === 'keywords' ||
		   param === 'blog-posts' || param === 'blog-categories' || param === 'expert-locations' ||
		   param === 'static' ;
}) satisfies ParamMatcher;