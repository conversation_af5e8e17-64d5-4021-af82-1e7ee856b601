import type { Actions } from '@sveltejs/kit';
import { fail } from '@sveltejs/kit';

import type { TBasePageDataSSR } from '../+page.server';
import { ContactFormService } from '$lib/server/services/contact';
import {
	GRAVITYFORMS_REF,
	type TSubmitFormErrorsDto,
	type TSubmitFormSuccessDto
} from '$lib/shared/api-dtos/internal-rest/contact-dto';
import type { TSubmitFormFields } from '$lib/server/services/contact/_types-interfaces';
import type { IBaseControlledError } from '$lib/shared/controlled-response/_types-interfaces';
import { CONTROLLED_ERROR_RESPONSE_CODES } from '$lib/shared/controlled-response/_constants';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import type { TGlobalSettingsDto } from '$lib/shared/api-dtos/internal-rest/settings-dtos';
import SubmitArticleFormSchema from '$lib/shared/validation/schemas/contact-forms/submit-article.schema';
import { PagesService } from '$lib/server/services/pages';
import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
import { sdk } from '$lib/server/services/sdk';
import { acfRestEndpoint } from '$lib/utils/endpoints';
import SeoService from '$lib/server/services/seo';

export type TSubmitArticlePageDataSSR = TBasePageDataSSR & {
	seo: TSeo;
};

async function searchUser(options: { email?: string }) {
	const userData = await sdk.SearchUser(options);
	return {
		user: userData?.users?.nodes[0] ?? null
	};
}

// PAGE SSR LOAD
export const load = async ({ params: { slug } }: { params: { slug: string } }) => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);

	try {
		const pageHandler = PagesService(seoService);
		const response = await pageHandler.getPageBySlug(slug);
		const { isSuccess, value } = response.getResultProps();

		if (isSuccess) {
			const page = value as TExtendedPageDto;

			return {
				page,
				seo: page.seo
			};
		}

		throw value;
	} catch (error: any) {
		const caughtError = new InternalError(error, error?.message ?? error);
		loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
			data: caughtError.getControlledErrorReference() as unknown,
			message: caughtError.getMessage(),
			scope: {
				component: 'Submit And Publish An Article Page - SSR',
				action: 'load'
			}
		} as THoneyBadgerContext);

		return {
			page: {
				title: '',
				slug: 'submit-post-blog-article'
			}
		};
	}
};

// FORM ACTIONS
export const actions: Actions = {
	article: async ({ request }) => {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		try {
			// Form Validation
			const data = await request.formData();
			const formData = Object.fromEntries(data);
			const formDataParsed = SubmitArticleFormSchema.safeParse(formData);

			if (!formDataParsed.success) {
				let formErrors = {};
				formDataParsed.error.issues.forEach((issue) => {
					formErrors = { ...formErrors, [issue.path[0]]: issue.message };
				});

				return fail(400, {
					success: false,
					isAValidationError: true,
					errors: formErrors
				});
			}

			const userData = await searchUser({ email: (formData?.email as string) ?? '' });

			if (!userData.user) {
				return {
					success: false,
					isUserError: true,
					message: 'The user is not approved'
				};
			}

			// Submit the form data through a service
			const contactService = ContactFormService(loggerService);
			const response = await contactService.submitForm(
				GRAVITYFORMS_REF.SUBMIT_ARTICLE,
				formData as TSubmitFormFields
			);
			const { isSuccess, ...rest } = response.getResultProps();

			if (!isSuccess) {
				const errorCode = (rest.value as IBaseControlledError<any>).getErrorCode();
				const errorReference = (
					rest.value as IBaseControlledError<any>
				).getControlledErrorReference();
				const isAValidationError = errorCode === CONTROLLED_ERROR_RESPONSE_CODES.BAD_REQUEST;
				return fail(isAValidationError ? 400 : 500, {
					success: false,
					isAValidationError,
					errors: isAValidationError ? (errorReference as TSubmitFormErrorsDto).errorMessage : {}
				});
			}

			return {
				success: true,
				message: (rest.value as TSubmitFormSuccessDto).message
			};
		} catch (error: any) {
			const caughtError = new InternalError(error, error?.message ?? error);
			loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Submit Article Page - SSR',
					action: 'actions.default'
				}
			} as THoneyBadgerContext);

			return fail(500, {
				success: false
			});
		}
	},
	verify: async ({ request }) => {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		try {
			// Form Validation
			const data = await request.formData();
			const formData = Object.fromEntries(data);

			const userData = await searchUser({ email: (formData?.email as string) ?? '' });

			if (!userData.user) {
				return {
					success: false,
					isUserError: true,
					message: 'The user is not approved'
				};
			}

			return {
				success: true
			};
		} catch (error: any) {
			const caughtError = new InternalError(error, error?.message ?? error);
			loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Submit Article Page - SSR',
					action: 'actions.default'
				}
			} as THoneyBadgerContext);

			return fail(500, {
				success: false
			});
		}
	}
};
