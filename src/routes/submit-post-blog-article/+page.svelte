<script lang="ts">
	import SubmitArticlePage from '$lib/ui/components/pages/submit-post-blog-article/SubmitArticlePage.svelte';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import type { PageData } from '../submit-post-blog-article/$types';
	import type { TSubmitArticlePageDataSSR } from './+page.server';

	export let data: PageData & TSubmitArticlePageDataSSR;
	$: ({ page, seo } = data);
</script>

<Seo data={seo} />
<meta NAME="robots" CONTENT="noindex,nofollow" />

<SubmitArticlePage {page} />
