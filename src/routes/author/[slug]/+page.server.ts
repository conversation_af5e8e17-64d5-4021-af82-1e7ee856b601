import { sdk } from '$lib/server/services/sdk';
import { acfRestEndpoint } from '$lib/utils/endpoints';

export async function load({
	params
}: {
	url: URL;
	params: { slug: string; pageRootSlug: string };
}) {
	const author = params.slug + '';

	async function getUser(options: { slug?: string }) {
		const userData = await sdk.GetUser(options);
		return {
			user: userData?.users?.nodes[0] ?? null
		};
	}

	const userData = await getUser({ slug: author });

	const postOptions = { author: userData?.user?.databaseId ?? 0, first: 6 } as {
		first?: number;
		author: number;
	};

	async function getPosts(options: { first?: number; author: number }) {
		const postsData = await sdk.GetUserPosts(options);
		return {
			posts: postsData?.posts?.nodes ?? [],
			pageInfo: postsData?.posts?.pageInfo
		};
	}

	return {
		postData: getPosts(postOptions),
		userData
	};
}
