<script lang="ts">
	import AuthorProfilePage from './../../../lib/ui/components/pages/author/AuthorProfilePage.svelte';
	import type { PageServerData } from './$types';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import { env } from '$env/dynamic/public';

	export let data: PageServerData;
</script>

<Seo
	data={{
		title: (data?.userData?.user?.name ?? '') + ' Author | Free Referral Blog',
		url: env.PUBLIC_SITE_URL + 'author/' + data.userData.user?.slug ?? '',
		description:
			data?.userData?.user?.seo?.metaDesc ??
			'Landing Page For Blogs By user: ' + data.userData.user?.name,
		isCanonical: data?.userData?.user?.seo?.canonical ? true : false,
		canonicalUrl: data?.userData?.user?.seo?.canonical ?? ''
	}}
/>
<AuthorProfilePage {data} />
