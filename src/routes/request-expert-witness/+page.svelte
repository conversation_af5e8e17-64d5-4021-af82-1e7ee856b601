<script lang="ts">
	import RequestExpertPage from '$lib/ui/components/pages/request-expert/RequestExpertPage.svelte';
	import type { PageData } from './$types';
	import type { TRequestExpertPageDataSSR } from './+page.server';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';

	export let data: PageData & TRequestExpertPageDataSSR;
	$: ({ defaultValues, seo } = data);
</script>

<Seo data={seo} />
<RequestExpertPage pageDefaultValues={defaultValues} />
