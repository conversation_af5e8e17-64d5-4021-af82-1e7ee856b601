import { PUBLIC_SITE_URL } from "$env/static/public";
import type { Actions } from "@sveltejs/kit";
import { fail } from "@sveltejs/kit";
import type { TBasePageDataSSR } from "../+page.server";
import { ContactFormService } from "$lib/server/services/contact";
import { GRAVITYFORMS_REF, type TRequestExpertFormFieldsDto, type TSubmitFormErrorsDto, type TSubmitFormSuccessDto } from "$lib/shared/api-dtos/internal-rest/contact-dto";
import type { TSubmitFormFields } from "$lib/server/services/contact/_types-interfaces";
import type { IBaseControlledError } from "$lib/shared/controlled-response/_types-interfaces";
import { CONTROLLED_ERROR_RESPONSE_CODES } from "$lib/shared/controlled-response/_constants";
import LoggerService from "$lib/server/services/logger";
import { HoneyBadgerAdapter, type THoneyBadgerContext } from "$lib/server/services/logger/honeybadger/adapter";
import { LOGGER_ERROR_LEVEL } from "$lib/server/services/logger/_constants";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import {RequestExpertModelSchema} from "$lib/shared/validation/schemas/contact-forms/request-expert.schema";

export type TRequestExpertPageDataDefaultValues = {
	requesterFirstName?: string;
	requesterLastName?: string;
	requesterEmail?: string;
	requestedExpertSlug?: string;
	requestedExpertId?: string;
	requestedExpertLink?: string;
	description?: string;
	requesterPhoneNumber?: string;
	requesterCompany?: string;
	requesterPosition?: string;
	requesterAddress?: string;
	requesterCity?: string;
	requesterState?: string;
	requesterZipCode?: string;
	typeOfCase?: string;
	requesterTimeFrame?: string;
};

export type TRequestExpertPageDataSSR = TBasePageDataSSR & {
	defaultValues: TRequestExpertPageDataDefaultValues;
};

// PAGE SSR LOAD
export const load = async ({ url }: { url: URL }) => {
	try {
		const requesterFirstName = url.searchParams.get('firstName');
		const requesterLastName = url.searchParams.get('lastName');
		const requesterEmail = url.searchParams.get('email');
		const requestedExpertSlug = url.searchParams.get('slug');
		const requestedExpertLink = requestedExpertSlug ? `${PUBLIC_SITE_URL}expert/${requestedExpertSlug}` : null;
		const searchParamsString = url.searchParams.toString() ? `?${url.searchParams.toString()}` : '';
		return {
			defaultValues: {
				requesterFirstName,
				requesterLastName,
				requesterEmail,
				requestedExpertSlug,
				requestedExpertLink
			},
			seo: {
				url: `${PUBLIC_SITE_URL}request-expert-witness${searchParamsString}`,
				title: 'Request an Expert Witness  - Free Referral by Consolidated Consultants',
				description: 'Request an Expert according your needs. We’ll save you time and search or recruit for you.',
				metaRobotsNoIndex: 'noindex'
			}
		};
	} catch (error: any) {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		const caughtError = new InternalError(error, error?.message ?? error);
		loggerService.log(
			caughtError,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Request Expert Witness Page - SSR',
					action: 'load'
				}
			} as THoneyBadgerContext
		);

		return {};
	}
}

// FORM ACTIONS
export const actions: Actions = {
	default: async ({ request }) => {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		try {
			// Form Validation
			const data = await request.formData();
			const formData = Object.fromEntries(data);
			const formDataParsed = RequestExpertModelSchema.safeParse(formData);
			if (!formDataParsed.success) {
				let formErrors = {};
				formDataParsed.error.issues.forEach(issue => {
					formErrors = { ...formErrors, [issue.path[0]]: issue.message };
				});

				return fail(400, {
					success: false,
					isAValidationError: true,
					errors: formErrors
				})
			}

			// TODO: Call here to collect form data on Hubspot (asynchronously)

			// Submit the form data through a service
			const contactService = ContactFormService(loggerService);
			const formDataSubmitFields = formData as TRequestExpertFormFieldsDto;
			const response = await contactService.submitForm(
				formDataSubmitFields.expertLink ? GRAVITYFORMS_REF.REQUEST_THIS_EXPERT : GRAVITYFORMS_REF.REQUEST_AN_EXPERT,
				formData as TSubmitFormFields
			);
			const { isSuccess, ...rest } = response.getResultProps();
			if (!isSuccess) {
				const errorCode = (rest.value as IBaseControlledError<any>).getErrorCode();
				const errorReference = (rest.value as IBaseControlledError<any>).getControlledErrorReference();
				const isAValidationError = errorCode === CONTROLLED_ERROR_RESPONSE_CODES.BAD_REQUEST;
				return fail(isAValidationError ? 400 : 500, {
					success: false,
					isAValidationError,
					errors: isAValidationError ? (errorReference as TSubmitFormErrorsDto).errorMessage : {}
				})
			}

			return {
				success: true,
				message: (rest.value as TSubmitFormSuccessDto).message,
			};
		} catch (error: any) {
			const caughtError = new InternalError(error, error?.message ?? error)
			loggerService.log(
				caughtError,
				LOGGER_ERROR_LEVEL.ERROR,
				{
					data: caughtError.getControlledErrorReference() as unknown,
					message: caughtError.getMessage(),
					scope: {
						component: 'Request Expert Witness Page - SSR',
						action: 'actions.default'
					}
				} as THoneyBadgerContext
			);

			return fail(500, {
				success: false
			});
		}
	}
};
