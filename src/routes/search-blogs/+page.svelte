<script lang="ts">
	import SearchBlogsPage from '$lib/ui/components/pages/search-blogs/SearchBlogsPage.svelte';
	import type { PageData } from './$types';
	import type { TSearchPageDataSSR } from './+page.server';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';

	export let data: PageData & TSearchPageDataSSR;
	$: ({
		defaultFilterValues: { criteria, category },
		seo,
	} = data);
</script>

<Seo data={seo} />
<SearchBlogsPage criteriaFilterValue={criteria} categoryFilterValue={category} />
