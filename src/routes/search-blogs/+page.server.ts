import { PUBLIC_SITE_URL } from "$env/static/public";
import type { TSeo } from "$lib/shared/api-dtos/internal-rest/base-dtos";
import type { TGlobalSettingsDto } from "$lib/shared/api-dtos/internal-rest/settings-dtos";
import type { TBasePageDataSSR } from "../+page.server";
import type { PageServerLoad } from "./$types";
import { acfRestEndpoint } from '$lib/utils/endpoints';

export type TSearchPageDataSSR = TBasePageDataSSR & {
  defaultFilterValues: {
    criteria: string | null;
    category: string | null;
  }
  seo?: TSeo;

};

export const load: PageServerLoad = async ({ url }) => {
  try {
    const criteriaSearchParam = url.searchParams.get('criteria');
    const categorySearchParam = url.searchParams.get('category');

    return {
      defaultFilterValues: {
        criteria: criteriaSearchParam,
        category: categorySearchParam,
      },
      seo: {
        title: `Search Blogs  - Expert Witness Referrals`,
        url: `${PUBLIC_SITE_URL}search`
      }
    }
  } catch (error) {
    console.log('Error fetching reports:', error);
  }
};
