import { ExpertService } from '$lib/server/services/expert';
import { CategoryService } from '$lib/server/services/category';
import { AlgoliaService } from '$lib/server/services/algolia';
import type { TExtendedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
import { sdk } from '$lib/server/services/sdk';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import SeoService from '$lib/server/services/seo';
import { TAlgoliaSearchExpertHit, TGetIndexedExpertsPaginatedResponse } from '$lib/server/services/expert/_types-interfaces';
import { LocationService } from '$lib/server/services/location';
import { TBaseLocation } from '$lib/shared/api-dtos/internal-rest/location-dtos';
import { TLocationTypeInput } from '$lib/server/services/location/_types-interfaces';
import { PagesService } from '$lib/server/services/pages';
import { TReducedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
import { PUBLIC_SITE_URL } from '$env/static/public';
import { TSitemapIndex } from '../_types-interfaces';
import { ILogger, ILoggerContext } from '$lib/server/services/logger/_types-interfaces';
import { LOCATIONS } from '../../[locationIndex=locationIndexes]/_constants';
import { KEYWORD_INDEXES } from '../../subdirectory/_constants';

const _sitemapGenerator = <LC extends ILoggerContext>(_loggerService: ILogger<LC>) => {
  const algoliaService = AlgoliaService();
  const loggerService = _loggerService;
  const seoService = SeoService(loggerService);


  const _getExpertPages = async() => {
    const expertsHandler = ExpertService(seoService);
    const {isSuccess, value} = (await expertsHandler.getAllIndexedExperts(
      {
        includeInactive: false
      },
      algoliaService,
      false,
    )).getResultProps();
    const algoliaExpertsRef = value as TGetIndexedExpertsPaginatedResponse;
    if(isSuccess && algoliaExpertsRef.total) {
      return algoliaExpertsRef.items.reduce((acc: string, expert) => {
        const _expert = expert as TAlgoliaSearchExpertHit;
        if(_expert.permalink || _expert.slug) {
          const expertUrl = _expert.permalink ?? `https://www.freereferral.com/expert/${_expert.slug}`;
          return `${acc}
            <url>
              <loc>${expertUrl}</loc>
              <priority>1.0</priority>
            </url>
          `;
        }
        return acc;
      }, '');
    }

    return '';
  };


  const _getExpertCategoriesPages = async() => {
    const categoriesHandler = CategoryService(algoliaService, seoService);
    const categoriesRes = await categoriesHandler.getAllIndexedCategories({});
    const categoriesValues = categoriesRes.getValue().items as TExtendedCategoryDto[];

    return categoriesValues.reduce<string>((acc: string, category, index) => {
      if(category.link || category.slug) {
        const categoryUrl = category.link ?? `https://www.freereferral.com/categories/${category.slug}`
        return `${acc}
          <url>
            <loc>${categoryUrl}</loc>
            <priority>0.9</priority>
          </url>
        `;
      }
      return acc;
    }, '');
  };


  const _getKeywordsPages = async() => {
    let urls = '';
    const pageHandler = PagesService(seoService);
    const response = await pageHandler.getAllIndexedPages(
      {
        pageTypeFilter: 'keyword',
      },
      algoliaService,
      true,
      true
    );
    const {isSuccess, value} = response.getResultProps();
    if(isSuccess) {
      const {items} = value;
      urls = (items as TReducedPageDto[]).reduce((acc, {link, slug}) => {
          // TODO: Use the "link" property by default, otherwise a composed url
          //       once the issue with the permalink is fixed. 
          //       const keywordUrl = link ?? `https://www.freereferral.com/${slug}`;
          const keywordUrl = `${PUBLIC_SITE_URL}${slug}`;

          return `
            ${acc}
            <url>
              <loc>${keywordUrl}</loc>
              <priority>0.8</priority>
            </url>
          `;
      }, '');
    }

    return urls;
  };


  const _getBlogPostPages = async(data?: {endCursor?: string | null; postStrings?: string;}) => {
    const {endCursor, postStrings = ''} = data || {};
    const response = await sdk.GetPosts({ afterCursor: endCursor, first: 100 });

    const nodes = response?.posts?.nodes;
    const pageInfo = response?.posts?.pageInfo;

    if (!nodes) {
      return postStrings;
    }

    const blogPostsUrls = nodes.reduce((acc: string, post) => {
      if (post?.link ?? post?.slug) {
        const postUrl = post.link || `https://www.freereferral.com/${post.slug}`;
        return `${acc}
          <url>
            <loc>${postUrl}</loc>
            <priority>0.7</priority>
          </url>
        `;
      }
      return acc;
    }, postStrings);

    if (!!pageInfo?.hasNextPage && !!pageInfo?.endCursor) {
      return await _getBlogPostPages({ endCursor: pageInfo.endCursor, postStrings: blogPostsUrls });
    }

    return blogPostsUrls;
  }


  const _getBlogCategoriesPages = async(data?: {endCursor?: string | null; categoriesString?: string;}) => {
    const {endCursor, categoriesString = ''} = data || {};
    const response = await sdk.GetCategories({ afterCursor: endCursor, first: 100 });

    const nodes = response?.categories?.nodes;
    const pageInfo = response?.categories?.pageInfo;
    if (!nodes) {
      return categoriesString;
    }

    const newPostsCategories = nodes.reduce((acc: string, category) => {
      if(category?.link || category?.slug) {
        const categoryUrl = category?.link ?? `https://www.freereferral.com/blog/category/${category.slug}`;
        return `${acc}
          <url>
            <loc>${categoryUrl}</loc>
            <priority>0.6</priority>
          </url>
        `;
      }
      return acc;
    }, categoriesString);

    if (!!pageInfo?.hasNextPage && !!pageInfo?.endCursor) {
      return await _getBlogCategoriesPages({
        endCursor: pageInfo.endCursor,
        categoriesString: newPostsCategories
      });
    }

    return newPostsCategories;
  }


  const _getLocationPages = async() => {
    let locationsUrlsMap = ``;

    // Countries, Regions and States
    const locationService = LocationService(algoliaService, seoService);
    for (const location of LOCATIONS) {
      const {isSuccess, value} = (await locationService.getIndexedLocations(location as TLocationTypeInput, true)).getResultProps();
      if(isSuccess) {
        const _locations = value as TBaseLocation[];
        for (const _location of _locations)  {
          // Location URLs should point to the standalone page, and also to the search page
          locationsUrlsMap = `${locationsUrlsMap}
            <url>
              <loc>${_location.link}</loc>
              <priority>0.5</priority>
            </url>
          `;
        }
      }
    }

    locationsUrlsMap = `${locationsUrlsMap}
      <url>
        <loc>https://www.freereferral.com/countries</loc>
        <priority>0.5</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/regions</loc>
        <priority>0.5</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/states</loc>
        <priority>0.5</priority>
      </url>
    `;
    
    return locationsUrlsMap;
  };


  const _getStaticPages = () => {
    // Get static Subdirectory Index Pages
    const staticSubdirectoryIndexPages = KEYWORD_INDEXES.reduce((acc: string, alphabetLetter: string) => {
      return `${acc}
        <url>
          <loc>https://www.freereferral.com/subdirectory/${alphabetLetter.toLowerCase()}</loc>
          <priority>0.4</priority>
        </url>
      `;
    }, '');

    // Return all static pages
    return `${staticSubdirectoryIndexPages}
      <url>
        <loc>https://www.freereferral.com/about-us</loc>
        <priority>0.4</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/privacy</loc>
        <priority>0.3</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/categories</loc>
        <priority>0.7</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/blog</loc>
        <priority>0.7</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/become-expert-witness</loc>
        <priority>0.4</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/search</loc>
        <priority>0.5</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com/expert</loc>
        <priority>0.5</priority>
      </url>
      <url>
        <loc>https://www.freereferral.com</loc>
        <priority>0.5</priority>
      </url>
    `;
  };

  const _SITEMAPS_GENERATOR_MAPPING = {
    'experts': _getExpertPages,
    'expert-categories': _getExpertCategoriesPages,
    'keywords': _getKeywordsPages,
    'blog-posts': _getBlogPostPages, 
    'blog-categories': _getBlogCategoriesPages, 
    'expert-locations': _getLocationPages,
    'static': _getStaticPages
  };

  const generate = async(sitemapIndex: TSitemapIndex): Promise<string> => {
    const generatorCallback = _SITEMAPS_GENERATOR_MAPPING[(sitemapIndex as keyof typeof _SITEMAPS_GENERATOR_MAPPING)];
    return await generatorCallback();
  };

  return {
    generate
  }
};

export async function GET({ params: { sitemap }}) {
  const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
  let sitemapSchema = 
    `
      <?xml version="1.0" encoding="UTF-8" ?>
      <urlset xmlns="https://www.sitemaps.org/schemas/sitemap/0.9"
      >
    `;

	try {
    const sitemapGenerator = _sitemapGenerator(loggerService);
    sitemapSchema = `${sitemapSchema}
      ${await sitemapGenerator.generate(sitemap)}
    `;
	} catch (error: any) {
		const caughtError = new InternalError(error, error?.message ?? error);
    loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
      data: {
        sitemap,
        error: caughtError.getControlledErrorReference() as unknown
      },
      message: caughtError.getMessage(),
      scope: {
        component: 'Specific Sitemap generator',
        action: 'GET'
      }
    } as THoneyBadgerContext);
	};

  // Closing the SiteMap XML schema
  sitemapSchema = `${sitemapSchema}
    </urlset>
  `.trim();

  return new Response(
    sitemapSchema,
    {
      headers: {
        'Content-Type': 'application/xml'
      }
    }
  );
};