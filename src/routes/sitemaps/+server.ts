import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import { PUBLIC_SITE_URL } from '$env/static/public';
import { SITEMAPS_INDEX } from './_constants';

export async function GET() {
  const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
  let sitemapsIndexSchema = 
    `
      <?xml version="1.0" encoding="UTF-8" ?>
      <sitemapindex xmlns="https://www.sitemaps.org/schemas/sitemap/0.9">
    `;
  try {
    for (const sitemap of SITEMAPS_INDEX) {
      sitemapsIndexSchema = `${sitemapsIndexSchema}
        <sitemap>
          <loc>${PUBLIC_SITE_URL}sitemaps/${sitemap}</loc>
          <lastmod>2025-05-15</lastmod>
        </sitemap>
      `;
    }
	} catch (error: any) {
		const caughtError = new InternalError(error, error?.message ?? error);
    loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
      data: caughtError.getControlledErrorReference() as unknown,
      message: caughtError.getMessage(),
      scope: {
        component: 'Sitemaps Index',
        action: 'GET'
      }
    } as THoneyBadgerContext);
	};
  // Closing the SiteMap XML schema
  sitemapsIndexSchema = `${sitemapsIndexSchema}
    </sitemapindex>
  `.trim();

  return new Response(
    sitemapsIndexSchema,
    {
      headers: {
        'Content-Type': 'application/xml'
      }
    }
  );
};