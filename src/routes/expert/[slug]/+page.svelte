<script lang="ts">
	import ExpertPage from '$lib/ui/components/pages/expert/ExpertPage.svelte';
	import type { TExpertPageDataSSR } from './+page.server';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import ExpertPageContentEnhanced from '$lib/ui/components/pages/expert/ExpertPageContentEnhanced.svelte';

	export let data: TExpertPageDataSSR;
	$: ({ expert, seo } = data);
	$: ({ schema, ...restOfSeoData } = seo);
</script>

<Seo data={restOfSeoData} />
{#if schema}
	<SchemaMarkup {schema} />
{/if}

{#if expert?.featureFlag === 'ai-content-enhanced'}
	<ExpertPageContentEnhanced expert={expert ?? null} />
{:else}
	<ExpertPage expert={expert ?? null} />
{/if}
