import { error } from '@sveltejs/kit';
import type { RouteParams } from './$types';

import type { TExtendedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import { ExpertService } from '$lib/server/services/expert';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import type { TBasePageDataSSR } from '../../+page.server';
import type { TSeo, TSeoRedirectDto } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import SeoService from '$lib/server/services/seo';
import { REDIRECT_ELEMENTS } from '$lib/server/services/seo/_constants';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { TPageError } from '../../_types-interfaces';

export type TExpertPageDataSSR = TBasePageDataSSR & {
	expert: TExtendedExpertDto | null;
	seo: TSeo | null;
};

export const load = async ({ params: { slug } }: { params: RouteParams }) => {
	let expert: TExtendedExpertDto | null = null;
	let seo: TSeo | undefined | null = null;
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);

	try {
		const expertsHandler = ExpertService(seoService);
		const res = await expertsHandler.getExpert({ slug });
		const { isSuccess, value } = res.getResultProps();

		if (!isSuccess) throw value;
		const {seo: _seo, ..._expert} = value as TExtendedExpertDto;
		seo = _seo ?? null;
		expert = _expert;
	
	} catch (e) {
		let isNotFound = false;

		if (e instanceof NotFoundError) {
			isNotFound = true;
		}
		// check redirects if it was a failure
		if (isNotFound) {
			const seoService = SeoService(loggerService);
			const redirectRes = await seoService.findRedirect({
				originUrl: `${REDIRECT_ELEMENTS.EXPERT}/${slug}`,
				status: 301
			});
			const { isSuccess, value } = redirectRes.getResultProps();
			let redirectTarget = '';
			if (isSuccess) {
				const target = (value as TSeoRedirectDto).target;
				redirectTarget = target ?? '';
			}
			throw error(404, { 
				message: `The content you are trying to reach was moved to a new URL`,
				data: {
					redirectUrl: redirectTarget && redirectTarget !== 'search' ? `/${redirectTarget}` : null
				}
			} as TPageError);
		}
	}

	return { expert, seo}
};
