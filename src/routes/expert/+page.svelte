<script lang="ts">
	import ExpertPage from '$lib/ui/components/pages/expert/ExpertPage.svelte';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import type { TExpertsListPageDataSSR } from './_types-interfaces';
	import ExpertsListPage from '$lib/ui/components/pages/expert/ExpertsListPage.svelte';

	export let data: TExpertsListPageDataSSR;
	let experts: TExpertsListPageDataSSR['experts'] = [];
	let page: TExpertsListPageDataSSR['page'];
	let schema: TSeo['schema'];
	let breadcrumbs: TSeo['breadcrumbs'] = [];
	let restOfSeoData: Omit<TSeo, 'breadcrumbs' | 'schema'>;
	$: {
		const {seo, ...restOfData} = data;
		if(restOfData) {
			({ page, experts } = restOfData);
		}
		if(seo) {
			({ schema, breadcrumbs, ...restOfSeoData } = seo);
		}
	}
</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<ExpertsListPage {experts} {page} {breadcrumbs} />
