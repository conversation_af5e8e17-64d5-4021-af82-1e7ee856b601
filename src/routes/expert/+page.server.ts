import { error } from '@sveltejs/kit';

import type { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import type { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import { ExpertService } from '$lib/server/services/expert';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import SeoService from '$lib/server/services/seo';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { AlgoliaService } from '$lib/server/services/algolia';
import { PagesService } from '$lib/server/services/pages';
import { TExpertsListPageDataSSR } from './_types-interfaces';

export const load = async (): Promise<TExpertsListPageDataSSR> => {
	const pageRootSlug = 'expert';
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	
	try {
		const seoService = SeoService(loggerService);

		// Get Page content
		const pageService = PagesService(seoService, loggerService);
		const response = await pageService.getPageBySlug(pageRootSlug);
		const { isSuccess: getPageSuccess, value: getPageValue } = response.getResultProps();
		if (getPageSuccess) {
			let experts: TReducedExpertDto[] = [];
			// Get Experts
			const algoliaService = AlgoliaService();
			const expertsHandler = ExpertService(seoService);
			const {isSuccess: getExpertsSuccess, value: getExpertsValue} = (await expertsHandler.getAllIndexedExperts(
				{
					includeInactive: false,
					limit: 2000,
					useSorted: true,
				},
				algoliaService,
				true,
				true
			)).getResultProps();
			if(getExpertsSuccess && getExpertsValue.total) 
				experts = getExpertsValue.items as TReducedExpertDto[];
			
			const {seo, ...pageData} = getPageValue as TExtendedPageDto;
			return {
				page: pageData,
				experts,
				seo: seo as TSeo
			}
		}
		throw getPageValue;
		
	} catch (e) {
		throw error(e instanceof NotFoundError ? 404 : 500);
	}
};
