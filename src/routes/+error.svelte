<script lang="ts">
	import { page } from '$app/stores';
	import type { TPageError } from './_types-interfaces';
	import H1 from '$lib/ui/components/system-design/headings/H1.svelte';
	import H2 from '$lib/ui/components/system-design/headings/H2.svelte';
	import H3 from '$lib/ui/components/system-design/headings/H3.svelte';
	import PageLayout from '$lib/ui/components/layouts/PageLayout.svelte';
	import ExpertSearchBox from '$lib/ui/components/search/ExpertSearchBox.svelte';
	import MainLayout from '$lib/ui/components/layouts/MainLayout.svelte';

	$: isNotFoundPage = $page?.status === 404;
	$: redirectUrl = ($page?.error as TPageError)?.data?.redirectUrl;
</script>

<!-- We should not use the Seo component as a regular page because we don't want to confuse search engines with URL and Canonical-URL for genuine Not Found page. -->
<svelte:head>
	{#if isNotFoundPage}
		<title>Page Not Found  - Free Referral by Consolidated Consultants</title>
	{:else}
		<title>Internal Error  - Free Referral by Consolidated Consultants</title>
	{/if}

	{#if isNotFoundPage}
		<meta name="description" content="The page you are looking for can not be found. Try searching for an expert for your legal case or visit our homepage." />
	{:else}
		<meta name="description" content="Something happened resolving the requested page. Try searching for an expert for your legal case or visit our homepage." />
	{/if}
	
	<!-- No URL/canonical URL tags -->

	<meta name="author" content="Consolidated Consultants" />
    <meta name="copyright" content="Consolidated Consultants" />
    <meta name="application-name" content="Expert Witness Referrals by Consolidated Consultants" />
    <meta name="publisher" content="Consolidated Consultants" />
</svelte:head>

<PageLayout class="GenericContentErrorPage flex flex-col justify-center items-center text-center h-screen">
	<MainLayout>
		<H1 class="uppercase">sorry</H1>
		<H2 class="lowercase">
			{#if isNotFoundPage && !redirectUrl}
				we couldn't find that page.
			{:else if isNotFoundPage && redirectUrl}
				the page has moved.
			{:else}
				an internal error happened.
			{/if}
		</H2>

		{#if isNotFoundPage && redirectUrl}
			<H3 class="normal-case font-normal">
				You can access the page you tried
				<a href="{redirectUrl}" class="text-blue-500 hover:underline">here</a>.
			</H3>
			<H3>
				Or,
			</H3>
		{:else if !isNotFoundPage}
			<H3 class="normal-case font-normal">
				You can try later
			</H3>
			<H3>
				Or,
			</H3>
		{/if}
		<H3 class="normal-case font-normal">
			Try searching for an expert for your legal case or visit our 
			<a href="/" class="text-blue-500 hover:underline cursor-pointer">homepage</a>.
		</H3>
		<ExpertSearchBox
			useCriteria
			criteriaCustomPlaceholder="What type of expert are you looking for?"
			useRequestExpertLink
		/>
	</MainLayout>
</PageLayout>