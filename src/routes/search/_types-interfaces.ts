import { z } from "zod";

import { TSeo } from "$lib/shared/api-dtos/internal-rest/base-dtos";
import { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import { TBasePageDataSSR } from "../+page.server";
import SearchExpertsModelSchema from "$lib/shared/validation/schemas/search-forms/base-expert.schema";
import { TGetIndexedExpertsPaginatedResponse } from "$lib/server/services/expert/_types-interfaces";
import { TReducedExpertDto } from "$lib/shared/api-dtos/internal-rest/expert-dtos";

export type TSearchPreloadedData = Omit<TGetIndexedExpertsPaginatedResponse, 'items'> & {
	items: TReducedExpertDto[]
};

export type TSearchExpertsQueryString =  z.infer<typeof SearchExpertsModelSchema>;

export type TSearchPageDataSSR = TBasePageDataSSR & {
	defaultFilterValues: TSearchExpertsQueryString;
	page: TExtendedPageDto;
	seo: TSeo;
	keywordContent?: string;
	searchPreloadedData?: TSearchPreloadedData
};
