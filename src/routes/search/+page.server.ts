import type { <PERSON><PERSON><PERSON> } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import type { PageServerLoad } from './$types';
import LoggerService from '$lib/server/services/logger';
import { Honey<PERSON><PERSON>gerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter';
import SeoService from '$lib/server/services/seo';
import { PagesService } from '$lib/server/services/pages';
import { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import type { TSearchExpertsQueryString, TSearchPageDataSSR } from './_types-interfaces';
import BaseControlledError from '$lib/shared/controlled-response/errors/base-controlled-error';
import { ContentService } from '$lib/server/services/content';
import { CategoryService } from '$lib/server/services/category';
import { AlgoliaService } from '$lib/server/services/algolia';
import SearchExpertsModelSchema from '$lib/shared/validation/schemas/search-forms/base-expert.schema';
import { ExpertService } from '$lib/server/services/expert';
import { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';

export const load: PageServerLoad = async ({
	url: { searchParams, pathname }
}: {
	url: URL;
}): Promise<TSearchPageDataSSR> => {
	// @TODO: Create a dictionary for static slugs.
	const defaultPageSlug = 'search';
	const defaultSeoUrl = `/${defaultPageSlug}`

	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	// Remove the trailing slash at the beginning of the URL path
	const _pathName = pathname.replace(/^\/+/, '');
	// Get the slug
	const slugSegments = _pathName.split('/');
	const pageSlug = slugSegments[0] ?? defaultPageSlug;
	const searchParamsString = searchParams.toString() ? `?${searchParams.toString()}` : '';
	// Get Query string parameters
	let queryStrings: TSearchExpertsQueryString = {
		criteria: searchParams.get('criteria'),
		categories: Array.from(searchParams.entries()).filter(([key]) => key === 'category').map(([, value]) => value),
		country: searchParams.get('country'),
		regions: Array.from(searchParams.entries()).filter(([key]) => key === 'region').map(([, value]) => value),
		states: Array.from(searchParams.entries()).filter(([key]) => key === 'state').map(([, value]) => value),
		exactMatch: searchParams.get('exactMatch') === 'true' || searchParams.get('exactMatch') === 'on' ? true : false,
		expertNumber: searchParams.get('expertNumber'),
	};
	let pageData: TExtendedPageDto = { title: '', slug: pageSlug };
	let seo: TSeo = { url: defaultSeoUrl };
	let keywordContent = '';
	let expertsSuccess = false;
	let expertsValue = null;

	try {
		// Run validations for the Query String (if it was passed)
		if(
			queryStrings.criteria || queryStrings.categories?.[0] || queryStrings.country || queryStrings.regions?.[0] ||
			queryStrings.states?.[0] || queryStrings.exactMatch
		) {
			const parsedQueryStrings = SearchExpertsModelSchema.safeParse(queryStrings);
            if (!parsedQueryStrings.success) {
				parsedQueryStrings.error.issues.forEach(issue => {
					queryStrings = {
						...queryStrings,
						[issue.path[0]]: issue.path[0] === 'categories' || issue.path[0] === 'regions' ||issue.path[0] === 'states' ?
										 [] : null
					}
                });

                throw new NotFoundError(
					queryStrings,
					'Invalid query string'
				)
            }
		}

		const seoService = SeoService(loggerService);
		const pageService = PagesService(seoService);
		// Get Search Page content
		const response = await pageService.getPageBySlug(pageSlug);
		const { isSuccess, value } = response.getResultProps();
		if(isSuccess) {
			const {seo: _seo, ...restOfData} = value as TExtendedPageDto;
			pageData = restOfData;
			// Get Search' related Experts from Algolia
			const algoliaService = AlgoliaService();
			const expertService = ExpertService(seoService);
			const expertsResponse = await expertService.getIndexedExpertsPaginated(
				{
					start: 0,
					limit: 20,
					includeInactive: false,
					criteriaFilter: queryStrings.criteria,
					exactMatch: queryStrings.exactMatch,
					categoryFilter: queryStrings.categories,
					countryFilter: queryStrings.country,
					regionFilter: queryStrings.regions,
					stateFilter: queryStrings.states,
				},
				algoliaService,
				true,
				true
			);
			const { isSuccess: _expertsSuccess, value: _expertsValue  } = expertsResponse.getResultProps();
			if(_expertsSuccess && _expertsValue) {
				expertsSuccess = _expertsSuccess;
				expertsValue = _expertsValue;
			}

			seo = _seo ?? seo;
			// If a Criteria o a Category were passed, then get a content copy if match with a Keyword or with an Expert Category
			if(queryStrings.criteria || queryStrings.categories?.[0]) {
				const categoryService = CategoryService(algoliaService, seoService);
				const contentService = ContentService(loggerService, pageService, categoryService, algoliaService);
				const response = await contentService.getKeywordContent(queryStrings.criteria || '', queryStrings.categories?.[0] || '');
				const { isSuccess, value } = response.getResultProps();
				if(isSuccess && value) keywordContent = value;
			}
		} else throw value;
	} catch (error: BaseControlledError<unknown> | any) {
		let caughtError = error;
		let isNotFound = false;
		if(!(error instanceof BaseControlledError))
			caughtError = new InternalError(error, error?.message ?? (typeof error === 'string' ? error : null));
		else if(error instanceof NotFoundError)
			isNotFound = true;
		
		loggerService.log(
			caughtError as any,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Search - SSR',
					action: 'load'
				}
			} as THoneyBadgerContext
		);
		if (isNotFound) throw error(404, { message: 'Not found' });
	}

	// return a non-disruptive/blocking response in the worst case (Internal Error)
	return {
		defaultFilterValues: queryStrings,
		page: pageData,
		seo: {
			...seo,
			url: `${seo?.url}${searchParamsString}`,
			canonicalUrl:  `${seo?.canonicalUrl}${searchParamsString}`,
		},
		keywordContent,
		searchPreloadedData: expertsSuccess && expertsValue ? {
			...expertsValue,
			items: expertsValue.items as TReducedExpertDto[]
		} : {
			total: 0,
			items: [],
			start: 0,
			limit: 20,
		} 
	};
};
