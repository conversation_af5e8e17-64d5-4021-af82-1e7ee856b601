<script lang="ts">
	import SearchPage from '$lib/ui/components/pages/search/SearchPage.svelte';
	import type { PageData } from './$types';
	import type { TSearchPageDataSSR } from './_types-interfaces';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';

	export let data: PageData & TSearchPageDataSSR;
	$: ({
		defaultFilterValues: { criteria, categories, country, regions, states, exactMatch },
		seo,
		page,
		keywordContent,
		searchPreloadedData
	} = data);
	$: ({schema, ...restOfSeoData} = seo);
</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<SearchPage
	criteriaFilterValue={criteria}
	categoryFilterValue={categories}
	countryFilterValue={country}
	regionFilterValue={regions}
	stateFilterValue={states}
	exactMatchFilterValue={exactMatch}
	{page}
	{keywordContent}
	{searchPreloadedData}
/>
