import { error } from '@sveltejs/kit';
import type { RouteParams } from './$types';

import type { TExtendedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import type { TBasePageDataSSR } from '../../+page.server';
import type { TSeo, TSeoRedirectDto } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import SeoService from '$lib/server/services/seo';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { TPageError } from '../../_types-interfaces';
import { AlgoliaService } from '$lib/server/services/algolia';
import { CategoryService } from '$lib/server/services/category';

export type TExpertPageDataSSR = TBasePageDataSSR & {
	expert: TExtendedExpertDto | null;
	seo: TSeo | null;
};

export const load = async ({ params: { slug } }: { params: RouteParams }) => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);

	try {
		const categoriesHandler = CategoryService(AlgoliaService(), seoService);
		const res = await categoriesHandler.getCategoryByPageNumber(slug,1,1);
		const { isSuccess, value } = res.getResultProps();
		if(isSuccess) {
			// return the custom 404 including the URL where the content as moved
			throw error(404, { 
				message: `The content you are trying to reach was moved to a new URL`,
				data: {
					redirectUrl: `/categories/${slug}`
				}
			} as TPageError);
		}

		throw value;
	} catch (e: any) {
		if(e?.status && e.status === 404) throw e;

		let isNotFound = false;
		if (e instanceof NotFoundError) {
			isNotFound = true;
		}
		// check redirects if it was a failure
		if (isNotFound) {
			const seoService = SeoService(loggerService);
			const redirectRes = await seoService.findRedirect({
				originUrl: `categories/${slug}`,
				status: 301
			});
			const { isSuccess, value } = redirectRes.getResultProps();
			let redirectTarget = '';
			if (isSuccess) {
				const target = (value as TSeoRedirectDto).target;
				redirectTarget = target ?? '';
			}
			throw error(404, {
					message: `The content you are trying to reach was moved to a new URL`,
					data: {
						redirectUrl: redirectTarget  && redirectTarget  !==  'search'  ?  `/${redirectTarget}`  : null
					}
				} as TPageError
			);
		}

		throw error(500, {message: `Something happened.`,} as TPageError
	);
	}
};
