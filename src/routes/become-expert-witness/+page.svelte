<script lang="ts">
	import BecomeExpertPage from '$lib/ui/components/pages/become-expert/BecomeExpertPage.svelte';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import type { TBecomeExpertPageDataSSR } from './+page.server';
	import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';

	export let data: TBecomeExpertPageDataSSR;
	let page: TBecomeExpertPageDataSSR['page'];
	let schema: TSeo['schema'];
	let breadcrumbs: TSeo['breadcrumbs'] = [];
	let restOfSeoData: Omit<TSeo, 'breadcrumbs' | 'schema'>;
	$: {
		const {seo, ...restOfData} = data;
		if(restOfData) {
			({ page } = restOfData);
		}
		if(seo) {
			({ schema, breadcrumbs, ...restOfSeoData } = seo);
		}
	}
</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<BecomeExpertPage {page} {breadcrumbs}/>
