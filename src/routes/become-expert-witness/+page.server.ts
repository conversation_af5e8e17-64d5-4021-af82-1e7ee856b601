import { PUBLIC_SITE_URL } from '$env/static/public';
import type { Actions } from '@sveltejs/kit';
import { fail } from '@sveltejs/kit';

import { ContactFormService } from '$lib/server/services/contact';
import {
	GRAVITYFORMS_REF,
	type TSubmitFormErrorsDto,
	type TSubmitFormSuccessDto
} from '$lib/shared/api-dtos/internal-rest/contact-dto';
import type { TSubmitFormFields } from '$lib/server/services/contact/_types-interfaces';
import type { IBaseControlledError } from '$lib/shared/controlled-response/_types-interfaces';
import { CONTROLLED_ERROR_RESPONSE_CODES } from '$lib/shared/controlled-response/_constants';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import {BecomeExpertModelSchema} from '$lib/shared/validation/schemas/contact-forms/become-expert.schema';
import SeoService from '$lib/server/services/seo';
import { PagesService } from '$lib/server/services/pages';
import { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import { TSeoRedirectDto } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import { TPageError } from '../_types-interfaces';
import { TGenericPageDataSSR } from '../[pageRootSlug]/_types-interfaces';

export type TBecomeExpertPageDataSSR = TGenericPageDataSSR & unknown;

// PAGE SSR LOAD
export const load = async () => {
	const pageSlug = 'become-expert-witness';
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);

	try {
		const genericContentHandler = PagesService(seoService, loggerService);
		const response = await genericContentHandler.getPageBySlug(pageSlug);
		const { isSuccess, value } = response.getResultProps();

		if (isSuccess) {
			const page = value as TExtendedPageDto;
			return {
				page,
				seo: page.seo as TSeo,
			};
		}
		
		throw value;
	} catch (error: any) {
		let isNotFound = false;

		if (error instanceof NotFoundError) {
			isNotFound = true;
		}
		// check redirects if it was a failure
		if (isNotFound) {
			const seoService = SeoService(loggerService);
			const redirectRes = await seoService.findRedirect({ originUrl: pageSlug, status: 301 });
			const { isSuccess, value } = redirectRes.getResultProps();
			let redirectTarget = '';
			if (isSuccess) {
				const target = (value as TSeoRedirectDto).target;
				redirectTarget = target ?? '';
			}
			throw error(404, { 
				message: `The content you are trying to reach was moved to a new URL`,
				data: {
					redirectUrl: redirectTarget && redirectTarget !== 'search' ? `/${redirectTarget}` : null
				}
			} as TPageError);
		}
		// Handle it as a temporary issue
		throw error(503)
	}
};

// FORM ACTIONS
export const actions: Actions = {
	default: async ({ request }) => {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		try {
			// Form Validation
			const data = await request.formData();
			const formData = Object.fromEntries(data);
			const formDataParsed = BecomeExpertModelSchema.safeParse(formData);
			if (!formDataParsed.success) {
				let formErrors = {};
				formDataParsed.error.issues.forEach((issue) => {
					formErrors = { ...formErrors, [issue.path[0]]: issue.message };
				});

				return fail(400, {
					success: false,
					isAValidationError: true,
					errors: formErrors
				});
			}

			// TODO: Call here to collect form data on Hubspot (asynchronously)

			// Submit the form data through a service
			const contactService = ContactFormService(loggerService);
			const response = await contactService.submitForm(
				GRAVITYFORMS_REF.BECOME_EXPERT,
				formData as TSubmitFormFields
			);
			const { isSuccess, value } = response.getResultProps();
			if (!isSuccess) {
				const errorCode = (value as IBaseControlledError<any>).getErrorCode();
				const errorReference = (
					value as IBaseControlledError<any>
				).getControlledErrorReference();
				const isAValidationError = errorCode === CONTROLLED_ERROR_RESPONSE_CODES.BAD_REQUEST;
				return fail(isAValidationError ? 400 : 500, {
					success: false,
					isAValidationError,
					errors: isAValidationError ? (errorReference as TSubmitFormErrorsDto).errorMessage : {}
				});
			}

			return {
				success: true,
				message: (value as TSubmitFormSuccessDto).message
			};
		} catch (error: any) {
			const caughtError = new InternalError(error, error?.message ?? error);
			loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Become Expert Witness Page - SSR',
					action: 'actions.default'
				}
			} as THoneyBadgerContext);

			return fail(500, {
				success: false
			});
		}
	}
};
