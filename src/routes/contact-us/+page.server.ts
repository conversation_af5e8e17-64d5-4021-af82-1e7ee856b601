import { PUBLIC_SITE_URL } from '$env/static/public';
import type { Actions } from '@sveltejs/kit';
import { fail } from '@sveltejs/kit';

import type { TBasePageDataSSR } from '../+page.server';
import { ContactFormService } from '$lib/server/services/contact';
import {
	GRAVITYFORMS_REF,
	type TSubmitFormErrorsDto,
	type TSubmitFormSuccessDto
} from '$lib/shared/api-dtos/internal-rest/contact-dto';
import type { TSubmitFormFields } from '$lib/server/services/contact/_types-interfaces';
import type { IBaseControlledError } from '$lib/shared/controlled-response/_types-interfaces';
import { CONTROLLED_ERROR_RESPONSE_CODES } from '$lib/shared/controlled-response/_constants';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import {ContactUsModelSchema} from '$lib/shared/validation/schemas/contact-forms/contact-us.schema';

export type TContactUsPageDataSSR = TBasePageDataSSR & {
	seo: TSeo;
};

// PAGE SSR LOAD
export const load = async () => {
	try {
		return {
			seo: {
				url: `${PUBLIC_SITE_URL}contact-us`,
				title: 'Contact Us - Expert Witness Referrals',
				description: 'Send us a message and we will contact you back in as little as 1 hour',
				metaRobotsNoIndex: 'noindex'
			}
		};
	} catch (error: any) {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		const caughtError = new InternalError(error, error?.message ?? error);
		loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
			data: caughtError.getControlledErrorReference() as unknown,
			message: caughtError.getMessage(),
			scope: {
				component: 'Contact Us Page - SSR',
				action: 'load'
			}
		} as THoneyBadgerContext);

		return {};
	}
};

// FORM ACTIONS
export const actions: Actions = {
	default: async ({ request }) => {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		try {
			// Form Validation
			const data = await request.formData();
			const formData = Object.fromEntries(data);
			const formDataParsed = ContactUsModelSchema.safeParse(formData);
			if (!formDataParsed.success) {
				let formErrors = {};
				formDataParsed.error.issues.forEach((issue) => {
					formErrors = { ...formErrors, [issue.path[0]]: issue.message };
				});

				return fail(400, {
					success: false,
					isAValidationError: true,
					errors: formErrors
				});
			}

			// TODO: Call here to collect form data on Hubspot (asynchronously)

			// Submit the form data through a service
			const contactService = ContactFormService(loggerService);
			const response = await contactService.submitForm(
				GRAVITYFORMS_REF.CONTACT_US,
				formData as TSubmitFormFields
			);
			const { isSuccess, ...rest } = response.getResultProps();
			if (!isSuccess) {
				const errorCode = (rest.value as IBaseControlledError<any>).getErrorCode();
				const errorReference = (
					rest.value as IBaseControlledError<any>
				).getControlledErrorReference();
				const isAValidationError = errorCode === CONTROLLED_ERROR_RESPONSE_CODES.BAD_REQUEST;
				return fail(isAValidationError ? 400 : 500, {
					success: false,
					isAValidationError,
					errors: isAValidationError ? (errorReference as TSubmitFormErrorsDto).errorMessage : {}
				});
			}

			return {
				success: true,
				message: (rest.value as TSubmitFormSuccessDto).message
			};
		} catch (error: any) {
			const caughtError = new InternalError(error, error?.message ?? error);
			loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Contact Us Page - SSR',
					action: 'actions.default'
				}
			} as THoneyBadgerContext);

			return fail(500, {
				success: false
			});
		}
	}
};
