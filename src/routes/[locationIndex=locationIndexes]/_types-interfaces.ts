import { TSeo } from "$lib/shared/api-dtos/internal-rest/base-dtos";
import { TExtendedLocationDto, TLocationsDto } from "$lib/shared/api-dtos/internal-rest/location-dtos";
import { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import { TBasePageDataSSR } from "../+page.server";
import { TSearchPreloadedData } from "../search/_types-interfaces";



export type TLocationsDtoPageDataSSR = TBasePageDataSSR & {
	locations?: TLocationsDto;
	seo?: TSeo;
	page?: TExtendedPageDto;
	locationsIndex?: string[] | undefined;
	locationIndexFiltered?: string | undefined;
	searchPreloadedData?: TSearchPreloadedData
};
export type TLocationPageDataSSR = TBasePageDataSSR & {
	location?: TExtendedLocationDto;
	seo?: TSeo;
	searchPreloadedData?: TSearchPreloadedData
};