<script lang="ts">
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import LocationIndexPage from '$lib/ui/components/pages/location/LocationIndexPage.svelte';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import type { TLocationsDtoPageDataSSR } from './_types-interfaces';

	export let data: TLocationsDtoPageDataSSR;
	$: ({ seo, locations, locationsIndex, locationIndexFiltered, page, searchPreloadedData } = data);
	$: ({schema, breadcrumbs, ...restOfSeoData} = (seo ?? {schema: '', url: '', breadcrumbs: []}));

</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<LocationIndexPage 
	{locations} 
	{locationsIndex} 
	{locationIndexFiltered} 
	{page}
	{breadcrumbs}
	{searchPreloadedData}
/>
