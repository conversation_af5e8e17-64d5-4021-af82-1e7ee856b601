<script lang="ts">
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import LocationPage from '$lib/ui/components/pages/location/LocationPage.svelte';
	import type { TLocationPageDataSSR } from '../_types-interfaces';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';

	export let data: TLocationPageDataSSR;
	$: ({ seo, location, searchPreloadedData } = data);
	$: ({schema, breadcrumbs, ...restOfSeoData} = (seo ?? {schema: '', url: '', breadcrumbs: []}));

</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<LocationPage
	{location}
	{breadcrumbs}
	{searchPreloadedData}
/>
