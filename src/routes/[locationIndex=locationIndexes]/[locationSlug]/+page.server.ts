import { AlgoliaService } from '$lib/server/services/algolia';
import { LocationService } from '$lib/server/services/location';
import { PagesService } from '$lib/server/services/pages/index.js';
import type {
	TBaseLocation,
	TExtendedLocationDto,
	TLocationsDto
} from '$lib/shared/api-dtos/internal-rest/location-dtos';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import { error } from '@sveltejs/kit';
import { PUBLIC_SITE_URL } from '$env/static/public';
import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos.js';
import LoggerService from '$lib/server/services/logger/index.js';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter.js';
import SeoService from '$lib/server/services/seo/index.js';
import { ExpertService } from '$lib/server/services/expert';
import { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import { TLocationPageDataSSR } from '../_types-interfaces';
import { TLocationTypeInput } from '$lib/server/services/location/_types-interfaces';

export const load = async ({ params: { locationSlug, locationIndex }}): Promise<TLocationPageDataSSR> => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);
	const algoliaService = AlgoliaService();

	try {
		// Get Location content from Wordpress
		const locationHandler = LocationService(algoliaService, seoService);
		const locationResponse = await locationHandler.getLocation(locationIndex as TLocationTypeInput, locationSlug);
		const { isSuccess: locationSucess, value: locationValue } = locationResponse.getResultProps();

		if (locationSucess) {
			const _locationValue = locationValue as TExtendedLocationDto;

			// Get locations' related Experts from Algolia
			const expertService = ExpertService(seoService);
			const expertsResponse = await expertService.getIndexedExpertsPaginated(
				{
					start: 0,
					limit: 20,
					includeInactive: false,
					countryFilter: locationIndex === 'countries' ? _locationValue.title : null,
					regionFilter: locationIndex === 'regions' ? [_locationValue.title] : null,
					stateFilter: locationIndex === 'states' ? [_locationValue.title] : null,
				},
				algoliaService,
				true,
				true
			);
			const { isSuccess: expertsSuccess, value: expertsValue  } = expertsResponse.getResultProps();

		
			const location = locationValue as TExtendedLocationDto;

			return {
				location: {
					...location,
					title: location.title.toLowerCase().includes('expert witnesses') ? location.title :
						   `${location.title} Expert Witnesses`
				},
				seo: {
					...location.seo,
					url: `${PUBLIC_SITE_URL}${locationIndex}/${locationSlug}`,
					//TODO: Remove this once all the location pages have assigned the correct Canonical URL.
					canonicalUrl: `${PUBLIC_SITE_URL}${locationIndex}/${locationSlug}`,
					breadcrumbs: [
						{
							text: 'Home',
							url: '/'
						},
						{
							text: `Expert Witnesses by ${locationIndex}`,
							url: `/${locationIndex}`
						},
						{
							text: location.title.toLowerCase().includes('expert witnesses') ? location.title :
								   `${location.title} Expert Witnesses`,
							url: `/${locationIndex}/${locationSlug}`
						}
					]
				},
				searchPreloadedData: expertsSuccess ? {
					...expertsValue,
					items: expertsValue.items as TReducedExpertDto[]
				} : {
					total: 0,
					items: [],
					start: 0,
					limit: 20,
				} 
			};
		}

		throw new NotFoundError<any>(locationSlug, `Location <${locationSlug}> not found`);
	} catch (e: any) {
		console.error(`
			Error on Location SSR
			Method: load
			Trace: ${e?.message ?? e}
		`);

		if (e instanceof NotFoundError) throw error(404);

		return {};
	}
};
