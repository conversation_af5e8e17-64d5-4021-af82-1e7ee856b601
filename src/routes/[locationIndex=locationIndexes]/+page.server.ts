import { AlgoliaService } from '$lib/server/services/algolia';
import { LocationService } from '$lib/server/services/location';
import { PagesService } from '$lib/server/services/pages/index.js';
import type {
	TBaseLocation,
	TLocationsDto
} from '$lib/shared/api-dtos/internal-rest/location-dtos';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import { error } from '@sveltejs/kit';
import { PUBLIC_SITE_URL } from '$env/static/public';
import type { TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos.js';
import LoggerService from '$lib/server/services/logger/index.js';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter.js';
import SeoService from '$lib/server/services/seo/index.js';
import { ExpertService } from '$lib/server/services/expert';
import { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import { TLocationsDtoPageDataSSR } from './_types-interfaces';
import { TLocationTypeInput } from '$lib/server/services/location/_types-interfaces';
import { LOCATIONS } from './_constants';

export const load = async ({ params: { locationIndex } }): Promise<TLocationsDtoPageDataSSR> => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);
	const locations: TLocationsDto = { countries: [], regions: [], states: [] };

	try {
		// Get locations accordingly (countries/regions/states) from Algolia
		const algoliaService = AlgoliaService();
		const locationService = LocationService(algoliaService, seoService);
		const response = await locationService.getIndexedLocations(locationIndex as TLocationTypeInput, true);
		const { isSuccess, value } = response.getResultProps();

		// Get Page content from Wordpress
		const pageHandler = PagesService(seoService);
		const pageResponse = await pageHandler.getPageBySlug('location');
		const { isSuccess: pageSucess, value: pageValue } = pageResponse.getResultProps();

		// Get locations' related Experts from Algolia
		const expertService = ExpertService(seoService);
		const expertsResponse = await expertService.getIndexedExpertsPaginated(
			{
				start: 0,
				limit: 20,
				includeInactive: false
			},
			algoliaService,
			true,
			true
		);
		const { isSuccess: expertsSuccess, value: expertsValue  } = expertsResponse.getResultProps();

		if (isSuccess && pageSucess) {
			locations[locationIndex as keyof TLocationsDto] = value as TBaseLocation[];
			const page = pageValue as TExtendedPageDto;

			return {
				page: {
					...page,
					title: page.title.includes('${location-index}') ? `${page.title.replace('${location-index}', locationIndex)}` : page.title,
					subtitle: page?.subtitle && page.subtitle.includes('${location-index}') ? `${page.subtitle.replace('${location-index}', locationIndex)}` : page.subtitle,
				},
				locations,
				locationsIndex: LOCATIONS,
				locationIndexFiltered: locationIndex.toLowerCase(),
				seo: {
					...page.seo,
					title: page.seo?.title && page.seo.title.includes('${location-index}') ? `${page.seo.title.replace('${location-index}', locationIndex)}` : '',
					description:  page.seo?.description && page.seo.description.includes('${location-index}') ? `${page.seo.description.replace('${location-index}', locationIndex)}` : '',
					url: `${PUBLIC_SITE_URL}${locationIndex}`,
					canonicalUrl: `${PUBLIC_SITE_URL}${locationIndex}`,
					breadcrumbs: [
						{
							text: 'Home',
							url: '/'
						},
						{
							text: `Expert Witnesses by ${locationIndex}`,
							url: `/${locationIndex}`
						}
					]
				},
				searchPreloadedData: expertsSuccess ? {
					...expertsValue,
					items: expertsValue.items as TReducedExpertDto[]
				} : {
					total: 0,
					items: [],
					start: 0,
					limit: 20,
				} 
			};
		}

		throw new NotFoundError<any>(locationIndex, `Location Index <${locationIndex}> not found`);
	} catch (e: any) {
		console.error(`
			Error on LocationIndex SSR
			Method: load
			Trace: ${e?.message ?? e}
		`);

		if (e instanceof NotFoundError) throw error(404);

		return {};
	}
};
