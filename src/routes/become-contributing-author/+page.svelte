<script lang="ts">
	import { onMount } from 'svelte';
	import BecomeAuthorPage from '$lib/ui/components/pages/become-contributing-author/BecomeAuthorPage.svelte';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import type { PageData } from '../become-contributing-author/$types';
	import type { TBecomeAuthorPageDataSSR } from './+page.server';
	import { globalInfo } from '$lib/ui/state-management/store/global';

	export let data: PageData & TBecomeAuthorPageDataSSR;
	$: ({ page, seo } = data);
</script>

<Seo data={seo} />
<BecomeAuthorPage {page} />
