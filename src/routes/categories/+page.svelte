<script lang="ts">
	import CategoriesPage from '$lib/ui/components/pages/category/CategoriesPage.svelte';
	import type { PageData } from './$types';
	import type { TCategoriesPageDataSSR } from "./_types-interfaces";
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';

	export let data: PageData & TCategoriesPageDataSSR;
	let categories: TCategoriesPageDataSSR['categories'];
	let categoryRoot: TCategoriesPageDataSSR['categoryRoot'];
	let searchPreloadedData: TCategoriesPageDataSSR['searchPreloadedData'];
	let schema: TSeo['schema'];
	let breadcrumbs: TSeo['breadcrumbs'] = [];
	let restOfSeoData: Omit<TSeo, 'breadcrumbs' | 'schema'>;
	$: {
		if(data) {
			({ categories, categoryRoot, searchPreloadedData} = data);
			({schema, breadcrumbs, ...restOfSeoData} = data?.seo)
		}
	}
</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<CategoriesPage {categories} {categoryRoot} {breadcrumbs} {searchPreloadedData}/>
