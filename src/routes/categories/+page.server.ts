import { AlgoliaService } from "$lib/server/services/algolia";
import { CategoryService } from "$lib/server/services/category";
import LoggerService from "$lib/server/services/logger";
import { LOGGER_ERROR_LEVEL } from "$lib/server/services/logger/_constants";
import { HoneyBadgerAdapter, type THoneyBadgerContext } from "$lib/server/services/logger/honeybadger/adapter";
import { PagesService } from "$lib/server/services/pages";
import type { TReducedCategoryDto } from "$lib/shared/api-dtos/internal-rest/category-dtos";
import type { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import NotFoundError from "$lib/shared/controlled-response/errors/not-found-error";
import { error } from "@sveltejs/kit";
import SeoService from "$lib/server/services/seo";
import type { TCategoriesPageDataSSR } from "./_types-interfaces";
import { ExpertService } from "$lib/server/services/expert";
import { TReducedExpertDto } from "$lib/shared/api-dtos/internal-rest/expert-dtos";


export const load = async (): Promise<TCategoriesPageDataSSR> => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);

	try {
		const slug = 'categories';
		// Root Categories to show in the page
		const categoriesHandler = CategoryService(AlgoliaService(), seoService, loggerService);
		const resCategories = await categoriesHandler.getRootCategories();
		const { isSuccess: isSuccessCategories, value: valueCategories } = resCategories.getResultProps();
		const categories = isSuccessCategories ? valueCategories as TReducedCategoryDto[] : [];

		// Page that holds the Root Categories list.
		const pageBaseCategories = PagesService(seoService);
		const resBaseCategoryPage = await pageBaseCategories.getPageBySlug(slug)
		const { isSuccess: isSuccessCategoryPage, value: valueCategoryPage } = resBaseCategoryPage.getResultProps();

		// Get category' related Experts from Algolia
		const algoliaService = AlgoliaService();
		const expertService = ExpertService(seoService);
		const expertsResponse = await expertService.getIndexedExpertsPaginated(
			{
				start: 0,
				limit: 20,
				includeInactive: false
			},
			algoliaService,
			true,
			true
		);
		const { isSuccess: expertsSuccess, value: expertsValue  } = expertsResponse.getResultProps();

		if (isSuccessCategoryPage && expertsSuccess) {
			const categoryRoot = valueCategoryPage as TExtendedPageDto;
			return {
				categoryRoot,
				categories,
				seo: {
					...(categoryRoot.seo ?? {url: '/categories', title: 'Expert Categories - Free Referral by Consolidated Consultants'}),
					breadcrumbs: [
						{
							text: 'Home',
							url: '/'
						},
						{
							text: 'Categories',
							url: '/categories'
						}
					]
				},
				searchPreloadedData: expertsSuccess ? {
					...expertsValue,
					items: expertsValue.items as TReducedExpertDto[]
				} : {
					total: 0,
					items: [],
					start: 0,
					limit: 20,
				} 
			}
		}

		throw valueCategoryPage;
	} catch (e: any) {
		let caughtError = new InternalError(e, e?.message ?? e);
		let isNotFound = false;
		if (e instanceof NotFoundError) {
			isNotFound = true;
			caughtError = e;
		}
		loggerService.log(
			caughtError,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Categories(Expert Categories) - SSR',
					action: 'load'
				}
			} as THoneyBadgerContext
		);
		if (isNotFound) throw error(404);

		// Handle it as a temporary issue
		throw error(503)
	}
}