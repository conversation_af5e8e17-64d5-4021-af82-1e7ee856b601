<script lang="ts">
	import CategoryPage from '$lib/ui/components/pages/category/CategoryPage.svelte';
	import type { PageData } from './$types';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import type{ TCategoriesSlugPageDataSSR } from '../_types-interfaces';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
	import { page } from '$app/stores';

	export let data: PageData & TCategoriesSlugPageDataSSR;
	let category: TCategoriesSlugPageDataSSR['category'];
	let searchPreloadedData: TCategoriesSlugPageDataSSR['searchPreloadedData'];
	let pageInfo: TCategoriesSlugPageDataSSR['pageInfo'];
	let schema: TSeo['schema'];
	let breadcrumbs: TSeo['breadcrumbs'] = [];
	let restOfSeoData: Omit<TSeo, 'breadcrumbs' | 'schema'>;
	let criteriaFilterValue: TCategoriesSlugPageDataSSR['defaultFilterValues']['criteria'] = null;
	let countryFilterValue: TCategoriesSlugPageDataSSR['defaultFilterValues']['country'] = null;
	let regionFilterValue: TCategoriesSlugPageDataSSR['defaultFilterValues']['regions'] = [];
	let stateFilterValue: TCategoriesSlugPageDataSSR['defaultFilterValues']['states'] = [];
	let exactMatchFilterValue: TCategoriesSlugPageDataSSR['defaultFilterValues']['exactMatch'] = false;
	
	$: {
		const {seo, ...restOfData} = data;
		if(restOfData) {
			({ category, pageInfo, searchPreloadedData } = restOfData);
			({
				criteria: criteriaFilterValue,
				country: countryFilterValue,
				regions: regionFilterValue,
				states: stateFilterValue,
				exactMatch: exactMatchFilterValue
			} = restOfData?.defaultFilterValues);
		}
		if(seo) {
			({schema, breadcrumbs, ...restOfSeoData} = data?.seo);
		}
	}
</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<CategoryPage 
	{category}
	{pageInfo}
	{breadcrumbs}
	{criteriaFilterValue}
	{countryFilterValue}
	{regionFilterValue}
	{stateFilterValue}
	{exactMatchFilterValue}
	{searchPreloadedData}
/>
