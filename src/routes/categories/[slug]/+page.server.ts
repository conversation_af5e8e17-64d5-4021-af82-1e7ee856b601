import { AlgoliaService } from '$lib/server/services/algolia';
import { CategoryService } from '$lib/server/services/category';
import type { TExtendedCategoryDto } from '$lib/shared/api-dtos/internal-rest/category-dtos';
import { error } from '@sveltejs/kit';
import type { T<PERSON><PERSON>, TSeoRedirectDto } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import SeoService from '$lib/server/services/seo';
import type { TCategoriesSlugPageDataSSR, TCategoryPageModel, TPagePaginationInfo } from '../_types-interfaces';
import ExpertCategoryPageModelSchema from '$lib/shared/validation/schemas/load-pages/expert-category.schema';
import BaseControlledError from '$lib/shared/controlled-response/errors/base-controlled-error';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import type { TPageError } from '../../_types-interfaces';
import { ExpertService } from '$lib/server/services/expert';
import { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';

export const load = async ({
	url: { searchParams },
	params: { slug}
}: {
	url: URL;
	params: {slug: string}
}): Promise<TCategoriesSlugPageDataSSR | unknown> => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);
	const categorySlug = slug;
	// Get Query string parameters
	const searchParamsString = searchParams.toString() ? `?${searchParams.toString()}` : '';
	let queryStrings: TCategoryPageModel = {
		criteria: searchParams.get('criteria'),
		categories: [],
		country: searchParams.get('country'),
		regions: Array.from(searchParams.entries()).filter(([key]) => key === 'region').map(([, value]) => value),
		states: Array.from(searchParams.entries()).filter(([key]) => key === 'state').map(([, value]) => value),
		exactMatch: searchParams.get('exactMatch') === 'true' || searchParams.get('exactMatch') === 'on' ? true : false,
		page: searchParams.get('page') && !isNaN(parseInt(searchParams.get('page')!)) ? parseInt(searchParams.get('page')!) : 1
	};

	let categoryData: TExtendedCategoryDto = { title: '', slug: categorySlug };
	let seo: TSeo = { url: `/categories/${categorySlug}` };
	let pageInfo: TPagePaginationInfo = {
		endCursor: '',
		hasNextPage: false,
		hasPreviousPage: false,
		nextPageUrl: '',
		previousPageUrl: ''
	}
	
	try {
		// Run validations for the Query String (if it was passed)
		if(
			queryStrings.criteria || queryStrings.country || queryStrings.regions?.[0] ||
			queryStrings.states?.[0] || queryStrings.exactMatch || queryStrings.page
		) {
			const parsedQueryStrings = ExpertCategoryPageModelSchema.safeParse(queryStrings);
            if (!parsedQueryStrings.success) {
				parsedQueryStrings.error.issues.forEach(issue => {
					queryStrings = {
						...queryStrings,
						[issue.path[0]]: issue.path[0] === 'regions' ||issue.path[0] === 'states' ?
										 [] : null
					}
                });

                throw new NotFoundError(
					queryStrings,
					'Invalid query string'
				)
            }
		}

		// Get the Category Page Content and it Subcategories
		const algoliaService = AlgoliaService();
		const categoriesHandler = CategoryService(algoliaService, seoService);
		const res = await categoriesHandler.getCategoryByPageNumber(
			categorySlug,
			queryStrings.page,
			8, // Default pageSize
		);
		const { isSuccess, value } = res.getResultProps();
		if (isSuccess) {
			const {seo: _seo, pageInfo: _pageInfo, ...restOfCategoryData} = (value as TExtendedCategoryDto);
			categoryData = restOfCategoryData;
			const defaultCriteriaFilter = categoryData.hierarchicalLevel === 2 ? categoryData.title.toLowerCase().split(' expert witnesses')[0] : queryStrings.criteria;
			const defaultCategoryFilter = categoryData.hierarchicalLevel === 2 ? (categoryData.parent?.title ?? '') : categoryData.title
			// Get category' related Experts from Algolia
			const expertService = ExpertService(seoService);
			const expertsResponse = await expertService.getIndexedExpertsPaginated(
				{
					start: 0,
					limit: 20,
					includeInactive: false,
					criteriaFilter: defaultCriteriaFilter,
					exactMatch: queryStrings.exactMatch,
					categoryFilter: [defaultCategoryFilter],
					countryFilter: queryStrings.country,
					regionFilter: queryStrings.regions,
					stateFilter: queryStrings.states,
				},
				algoliaService,
				true,
				true
			);
			const { isSuccess: expertsSuccess, value: expertsValue  } = expertsResponse.getResultProps();

			if(_seo) seo = _seo;
			// Define Next/Previous URLS according to the PageInfo data
			let nextPageUrl = '';
			let previousPageUrl = '';
			const queryStringIncludesPagination = searchParamsString.includes('page=');
			if(_pageInfo?.hasNextPage) {
				const nextPageSearchParamsString = searchParamsString;
				nextPageUrl = queryStringIncludesPagination ?
				nextPageSearchParamsString.replace(`page=${queryStrings.page}`, `page=${queryStrings.page + 1}`) :
								nextPageSearchParamsString ?  `${nextPageSearchParamsString}&page=${queryStrings.page + 1}` :
							  `?page=${queryStrings.page + 1}`;
			}
			if(_pageInfo?.hasPreviousPage) {
				const previousPageSearchParamsString = searchParamsString;
				previousPageUrl = queryStringIncludesPagination ?
								previousPageSearchParamsString.replace(`page=${queryStrings.page}`, (queryStrings.page > 2 ? `page=${queryStrings.page - 1}` : '')) :
								previousPageSearchParamsString ?  `${previousPageSearchParamsString}${queryStrings.page > 2 ? '&page='+(queryStrings.page - 1) : ''}` :
							  `?page=${queryStrings.page > 2 ? '&page='+(queryStrings.page - 1) : ''}`;
			}
			pageInfo = {
				endCursor: _pageInfo?.endCursor ?? '',
				hasNextPage: _pageInfo?.hasNextPage ?? false,
				hasPreviousPage: _pageInfo?.hasPreviousPage ?? false,
				nextPageUrl,
				previousPageUrl
			}

			return {
				defaultFilterValues: queryStrings,
				category: categoryData,
				seo: {
					...seo,
					// TODO: Find a better way to include pagination references in the SEO meta.
					title: seo?.title && seo.title.includes('- Free Referral') && queryStrings.page > 1 ? seo.title.replace('- Free Referral', `(Subcategories Page #${queryStrings.page}) - Free Referral`) : seo?.title,
					description: `${seo.description}. Browse Subcategories Page #${queryStrings.page}.`,
					url: `${seo?.url}${searchParamsString}`,
					canonicalUrl:  `${seo?.canonicalUrl}${searchParamsString}`,
				},
				pageInfo,
				searchPreloadedData: expertsSuccess ? {
					...expertsValue,
					items: expertsValue.items as TReducedExpertDto[]
				} : {
					total: 0,
					items: [],
					start: 0,
					limit: 20,
				} 
			} as TCategoriesSlugPageDataSSR

		} else throw value;
	} catch (e: any) {
		let caughtError = e;
		let isNotFound = false;
		if(!(e instanceof BaseControlledError))
			caughtError = new InternalError(e, e?.message ?? (typeof error === 'string' ? error : null));
		else if(e instanceof NotFoundError)
			isNotFound = true;
		
		loggerService.log(
			caughtError as any,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Search - SSR',
					action: 'load'
				}
			} as THoneyBadgerContext
		);
		if (isNotFound) {
			const redirectRes = await seoService.findRedirect({
				originUrl: `categories/${categorySlug}`,
				status: 301
			});
			const { isSuccess, value } = redirectRes.getResultProps();
			let redirectTarget = '';
			if (isSuccess) {
				const target = (value as TSeoRedirectDto).target;
				redirectTarget = target ?? '';
			}
			throw error(404, { 
				message: `The content you are trying to reach was moved to a new URL`,
				data: {
					redirectUrl: redirectTarget && redirectTarget !== 'search' ? `/${redirectTarget}` : null
				}
			} as TPageError);
		}

		// Handle it as a temporary issue
		throw error(503)
	}
};
