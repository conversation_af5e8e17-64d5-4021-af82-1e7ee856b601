import { z } from "zod";

import { TSeo } from "$lib/shared/api-dtos/internal-rest/base-dtos";
import { TExtendedCategoryDto, TReducedCategoryDto } from "$lib/shared/api-dtos/internal-rest/category-dtos";
import { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import ExpertCategoryPageModelSchema from "$lib/shared/validation/schemas/load-pages/expert-category.schema";
import { TBasePageDataSSR } from "../+page.server";
import { TSearchPreloadedData } from "../search/_types-interfaces";

export type TCategoryPageModel =  Omit<z.infer<typeof ExpertCategoryPageModelSchema>, 'expertNumber'>;

export type TCategoriesPageDataSSR = TBasePageDataSSR & {
	categories: TReducedCategoryDto[];
	categoryRoot: TExtendedPageDto;
	seo: TSeo;
	searchPreloadedData?: TSearchPreloadedData
};

export type TPagePaginationInfo = {
	endCursor: string;
	hasNextPage: boolean;
	nextPageUrl?: string;
	hasPreviousPage: boolean;
	previousPageUrl?: string;
}

export type TCategoriesSlugPageDataSSR = TBasePageDataSSR & {
	defaultFilterValues: TCategoryPageModel;
	category: TExtendedCategoryDto;
	seo: TSeo;
	pageInfo?: TPagePaginationInfo;
	searchPreloadedData?: TSearchPreloadedData
};