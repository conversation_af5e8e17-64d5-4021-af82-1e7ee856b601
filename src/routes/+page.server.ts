import { PUBLIC_SITE_URL } from '$env/static/public';
import ConfigService from '$lib/server/services/config';
import LoggerService from '$lib/server/services/logger';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter';
import { sdk } from '$lib/server/services/sdk';
import type { TImage, TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import type { TGlobalSettingsDto, THomepageSettingsDto } from '$lib/shared/api-dtos/internal-rest/settings-dtos';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';

export type TBasePageDataSSR = {
	seo?: TSeo;
	globalData?: TGlobalSettingsDto;
};


export type TBanner = {
	__typename: string;
	description: string;
	title: string;
	subtitle: string;
	backgroundImage: TImage;
	//
};

export type TLogoSlider = {
	__typename: string;
	title: string;
	logoList: Array<{
		logo: TImage;
	}>;
};

export type TFindResults = {
	__typename: string;
	title: string;
	description: string;
	searchOnlineText: string;
	qualifiedExperts: {
		expertsCopy: string;
		expertsCount: number;
	};
	caseSupported: {
		caseCopy: string;
		caseCount: number;
	};
	searchOnlineImage: TImage;
};

export type TCategoriesSection = {
	__typename: string;
	title?: string;
	cta?: {
		title: string;
		url: string;
	};
};

export type TWinningSecret = {
	__typename: string;
	title: string;
	features: Array<{
		description: string;
		title: string;
		icon: TImage;
	}>;
};

export type TTestimonialDtos = {
	__typename: string;
	title: string;
};

export type TFindWitnessFree = {
	__typename: string;
	title: string;
	findSteps: Array<{
		description: string;
		title: string;
		icon: TImage;
	}>;
};

export type THowReferralsWork = {
	__typename: string;
	title: string;
	referralSteps: Array<{
		description: string;
	}>;
	media: {
		mediaType: string;
		sourceUrl: string;
		title: string;
		mediaDetails: {
			width: number;
			height: number;
		};
	};
};

export type TFeatureStrip = {
	__typename: string;
	title: string;
	description: string;
	cta: {
		title: string;
		url: string;
	};
	media: {
		mediaType: string;
		sourceUrl: string;
		title: string;
		mediaDetails: {
			width: number;
			height: number;
		};
	};
};

export type TRequestExpertForm = {
	__typename: string;
	title: string;
	description: string;
	infoCopy: string;
	cta: {
		title: string;
		url: string;
	};
};

export type PageSection =
	| TBanner
	| TLogoSlider
	| TFindResults
	| TCategoriesSection
	| TWinningSecret
	| TFindWitnessFree
	| TFeatureStrip
	| THowReferralsWork
	| TRequestExpertForm
	| TTestimonialDtos;



export type THomePageDataSSR = TBasePageDataSSR & {
	homepageSettings: THomepageSettingsDto;
	pageSections: PageSection[];
};

export const load = async(): Promise<THomePageDataSSR> => {
	//@TODO: Create a valid DTO for types Array<any> below.
	let homepageSettings: THomepageSettingsDto = {testimonials: [], categories: []};
	let pageSections: PageSection[] = [];
	let seo: TSeo = {
		title: '',
		description: '',
		url: '',
		focuskw: ''
	};
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());

	async function getPageData() {
		const pageData = await sdk.QueryHomePageBySlug({ slug: 'homepage' });
		return pageData.pages?.nodes[0] ?? null;
	}

	try {
		const configService = ConfigService(loggerService);
		const {isSuccess, value} = (await configService.getHomepageSettings()).getResultProps();
		if(isSuccess)  homepageSettings = value;

		const pageData = await getPageData();
		pageSections = pageData?.homepageCustomFields?.components ? pageData.homepageCustomFields.components as  PageSection[] : [];
		seo = pageData?.seo ? 
			{
				title: pageData?.seo?.title,
				description: pageData?.seo?.metaDesc,
				url: `${PUBLIC_SITE_URL}`,
				focuskw: pageData?.seo?.focuskw
			} : seo;
	} catch (error: any) {//@TODO: Refactor this (use UNKNOWN type and fix references properly)
		let caughtError = new InternalError(error, error?.message ?? error);
		if (error instanceof NotFoundError)  caughtError = error;
		
		loggerService.log(
			caughtError,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'HomePage - SSR',
					action: 'load'
				}
			} as THoneyBadgerContext
		);
	}
	
	return {
		homepageSettings,
		pageSections,
		seo,
	} as THomePageDataSSR;
}
