import { PagesService } from '$lib/server/services/pages';
import type { T<PERSON>eo, TSeoRedirectDto } from '$lib/shared/api-dtos/internal-rest/base-dtos';
import { PAGE_TYPES, type TExtendedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
import { error } from '@sveltejs/kit';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import SeoService from '$lib/server/services/seo';
import { TGenericPageDataSSR } from './_types-interfaces';
import { TPageError } from '../_types-interfaces';
import { ExpertService } from '$lib/server/services/expert';
import { AlgoliaService } from '$lib/server/services/algolia';
import { TReducedExpertDto } from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import { PostsService } from '$lib/server/services/posts';
import { TGetIndexedPostsPaginatedResponse } from '$lib/server/services/posts/_types-interfaces';
import { TTopicClusterBlogPostItemsDto, TReducedPostDto } from '$lib/shared/api-dtos/internal-rest/post-dtos';

export const load = async ({
	params: { pageRootSlug }
}: {
	params: {
		pageRootSlug: string;
	};
}): Promise<TGenericPageDataSSR> => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);
	try {
		const genericContentHandler = PagesService(seoService, loggerService);
		const response = await genericContentHandler.getPageBySlug(pageRootSlug);
		const { isSuccess, value } = response.getResultProps();

		if (isSuccess) {
			const page = value as TExtendedPageDto;
			let expertsValue = null;
			let expertsSuccess = false;
			let topicClusterItems = null;
			if(page.pageType === PAGE_TYPES.KEYWORD) {
				// Get keyword' related Experts from Algolia
				const algoliaService = AlgoliaService();
				const expertService = ExpertService(seoService);
				const keywordAsCriteriaFilter = page.algoliaSearchKeyword ?? 
												(page.title.toLowerCase().includes('expert witnesses') ? 
												page.title.toLowerCase().split('expert witnesses')[0] : page.title);
				const expertsResponse = await expertService.getIndexedExpertsPaginated(
					{
						start: 0,
						limit: 20,
						includeInactive: false,
						criteriaFilter: keywordAsCriteriaFilter
					},
					algoliaService,
					true,
					true
				);
				const { isSuccess, value } = expertsResponse.getResultProps();
				expertsValue = value;
				expertsSuccess = isSuccess;

				// Get cluster topic (related blog posts) from Algolia
				const blogPostService = PostsService();
				const blogPostsResponse = await blogPostService.getIndexedPostsPaginated(
					{
						start: 0,
						limit: 4,
						topicFilter: keywordAsCriteriaFilter
					},
					algoliaService,
					true,
					true
				);
				const { isSuccess: blogPostsSuccess, value: blogPostsValue } = blogPostsResponse.getResultProps();
				if(blogPostsSuccess && (blogPostsValue as TGetIndexedPostsPaginatedResponse).total) {
					topicClusterItems = {
						type: 'post' as TTopicClusterBlogPostItemsDto['type'],
						items: (blogPostsValue as TGetIndexedPostsPaginatedResponse).items as TReducedPostDto[]
					}
				}
			}
			
			return {
				page,
				seo: page.seo as TSeo,
				searchPreloadedData: expertsSuccess && expertsValue ? {
					...expertsValue,
					items: expertsValue?.items as TReducedExpertDto[]
				} : {
					total: 0,
					items: [],
					start: 0,
					limit: 20,
				},
				topicClusterItems
			};
		}

		throw value;
	} catch (e) {
		let isNotFound = false;

		if (e instanceof NotFoundError) {
			isNotFound = true;
		}
		// check redirects if it was a failure
		if (isNotFound) {
			const seoService = SeoService(loggerService);
			const redirectRes = await seoService.findRedirect({ originUrl: pageRootSlug, status: 301 });
			const { isSuccess, value } = redirectRes.getResultProps();
			let redirectTarget = '';
			if (isSuccess) {
				const target = (value as TSeoRedirectDto).target;
				redirectTarget = target ?? '';
			}
			throw error(404, { 
				message: `The content you are trying to reach was moved to a new URL`,
				data: {
					redirectUrl: redirectTarget && redirectTarget !== 'search' ? `/${redirectTarget}` : null
				}
			} as TPageError);
		}
		// Handle it as a temporary issue
		throw error(503)
	}
};
