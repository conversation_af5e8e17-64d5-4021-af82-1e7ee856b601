<script lang="ts">
	import type { PageData } from './$types';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import GenericPage from '$lib/ui/components/pages/generic/GenericPage.svelte';
	import type { TGenericPageDataSSR } from './_types-interfaces';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';

	export let data: PageData & TGenericPageDataSSR;
	let page: TGenericPageDataSSR['page'];
	let searchPreloadedData: TGenericPageDataSSR['searchPreloadedData'];
	let topicClusterItems: TGenericPageDataSSR['topicClusterItems'];
	let schema: TSeo['schema'];
	let breadcrumbs: TSeo['breadcrumbs'] = [];
	let restOfSeoData: Omit<TSeo, 'breadcrumbs' | 'schema'>;
	$: {
		const {seo, ...restOfData} = data;
		if(restOfData) {
			({ page, searchPreloadedData, topicClusterItems } = restOfData);
		}
		if(seo) {
			({ schema, breadcrumbs, ...restOfSeoData } = seo);
		}
	}
</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<GenericPage {page} {breadcrumbs} {searchPreloadedData} {topicClusterItems}/>
