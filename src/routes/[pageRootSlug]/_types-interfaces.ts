import { TSeo } from "$lib/shared/api-dtos/internal-rest/base-dtos";
import { TExtendedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import { TTopicClusterBlogPostItemsDto } from "$lib/shared/api-dtos/internal-rest/post-dtos";
import { TBasePageDataSSR } from "../+page.server";
import { TSearchPreloadedData } from "../search/_types-interfaces";


export type TGenericPageDataSSR = TBasePageDataSSR & {
	page?: TExtendedPageDto;
	seo: TSeo;
	searchPreloadedData?: TSearchPreloadedData;
	topicClusterItems?: TTopicClusterBlogPostItemsDto | null;
};