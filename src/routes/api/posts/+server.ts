import type { RequestEvent } from './$types';
import { json } from '@sveltejs/kit';
import { AlgoliaService } from '$lib/server/services/algolia';
import type { TSearchExpertResponse } from '$lib/shared/api-dtos/internal-rest/schema';
import { CONTROLLED_ERROR_RESPONSE_CODES } from '$lib/shared/controlled-response/_constants';
import type {
	TGetExpertsInput,
	TPaginatedExpertCollection
} from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import { PostsService } from '$lib/server/services/posts';

export const POST = async (request: RequestEvent) => {
	const requestBodyParameters = await request.request.json();
	const {
		criteria,
		start,
		limit,
		category,
		reduced,
		exactMatch
	} = requestBodyParameters as TGetExpertsInput;

	try {
		// composition-root like
		const algoliaService = AlgoliaService();
		const postService = PostsService();

		const response = await postService.getIndexedPostsPaginated(
			{
				start,
				limit,
				criteriaFilter: criteria ?? null,
				categoryFilter: category ?? null,
				// commenting out as hot fix for attribute no longer being present
				// queryFilter: 'inactive_expert:false',
				exactMatch
			},
			algoliaService,
			reduced
		);

		const { isSuccess, value } = response.getResultProps();

		return json({
			status: {
				code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				success: isSuccess
			},
			values: value as TPaginatedExpertCollection
		} as TSearchExpertResponse);
	} catch (error: any) {
		console.error(`
            Error on Posts endpoint
            Method: POST
            Trace: ${error?.message ?? error}
        `);
		// return a non disruptive/blocking response for now
		return json({
			status: {
				code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				success: false
			},
			values: {
				items: [],
				total: 0,
				start,
				limit
			} as TPaginatedExpertCollection
		} as TSearchExpertResponse);
	}
};
