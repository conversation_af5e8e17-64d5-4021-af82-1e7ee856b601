import type { RequestEvent } from './$types';
import { json } from '@sveltejs/kit';
import { AlgoliaService } from '$lib/server/services/algolia';
import { ExpertService } from '$lib/server/services/expert';
import type { TSearchExpertResponse } from '$lib/shared/api-dtos/internal-rest/schema';
import { CONTROLLED_ERROR_RESPONSE_CODES } from '$lib/shared/controlled-response/_constants';
import type {
	TGetExpertsInput,
	TPaginatedExpertCollection
} from '$lib/shared/api-dtos/internal-rest/expert-dtos';
import LoggerService from '$lib/server/services/logger';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter';
import SeoService from '$lib/server/services/seo';

export const POST = async (request: RequestEvent) => {
	const requestBodyParameters = await request.request.json();
	const {
		criteria,
		start,
		limit,
		category,
		country,
		region,
		state,
		expertNumber,
		reduced,
		exactMatch,
		includeInactive
	} = requestBodyParameters as TGetExpertsInput;
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());

	try {
		// composition-root like
		const algoliaService = AlgoliaService();
		const seoService = SeoService(loggerService);
		const expertService = ExpertService(seoService);

		const response = await expertService.getIndexedExpertsPaginated(
			{
				start,
				limit,
				criteriaFilter: criteria ?? null,
				categoryFilter: category ?? null,
				countryFilter: country ?? null,
				regionFilter: region ?? null,
				stateFilter: state ?? null,
				expertNumberFilter: expertNumber ?? null,
				// commenting out as hot fix for attribute no longer being present
				// queryFilter: 'inactive_expert:false',
				exactMatch,
				includeInactive
			},
			algoliaService,
			reduced
		);

		const { isSuccess, value } = response.getResultProps();

		return json({
			status: {
				code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				success: isSuccess
			},
			values: value as TPaginatedExpertCollection
		} as TSearchExpertResponse);
	} catch (error: any) {
		console.error(`
            Error on Experts endpoint
            Method: POST
            Trace: ${error?.message ?? error}
        `);
		// return a non disruptive/blocking response for now
		return json({
			status: {
				code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				success: false
			},
			values: {
				items: [],
				total: 0,
				start,
				limit
			} as TPaginatedExpertCollection
		} as TSearchExpertResponse);
	}
};
