import type { RequestEvent } from "./$types";
import { json } from '@sveltejs/kit';
import { CONTROLLED_ERROR_RESPONSE_CODES } from "$lib/shared/controlled-response/_constants";
import type { TSubmitContactFormInput, TSubmitFormErrorsDto, TSubmitFormSuccessDto } from "$lib/shared/api-dtos/internal-rest/contact-dto";
import { ContactFormService } from "$lib/server/services/contact";
import type { IBaseControlledError } from "$lib/shared/controlled-response/_types-interfaces";
import LoggerService from "$lib/server/services/logger";
import { HoneyBadgerAdapter, type THoneyBadgerContext } from "$lib/server/services/logger/honeybadger/adapter";
import { LOGGER_ERROR_LEVEL } from "$lib/server/services/logger/_constants";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";

export const POST = async(request: RequestEvent) => {
    const requestBodyParameters = await request.request.json();
    const {formRef, fields} = requestBodyParameters as TSubmitContactFormInput;
    const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());

    try {    
        const contactFormService = ContactFormService(loggerService);
        const response = await contactFormService.submitForm(formRef, fields);
        const {isSuccess, value} = response.getResultProps();

        return json({
            status: {
                code: isSuccess ? CONTROLLED_ERROR_RESPONSE_CODES.OK : (value as IBaseControlledError<any>).getErrorCode(),
                message: isSuccess ? CONTROLLED_ERROR_RESPONSE_CODES.OK : (value as IBaseControlledError<any>).getMessage(),
                success: isSuccess
            },
            values: isSuccess ? value as TSubmitFormSuccessDto : (value as IBaseControlledError<TSubmitFormErrorsDto | unknown>).getControlledErrorReference(),

        });
    } catch (error: any) {
        const caughtError = new InternalError(error, error?.message ?? error);
        loggerService.log(
			caughtError,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Contact API endpoint',
					action: 'POST'
				}					
			} as THoneyBadgerContext
		);

        // return a non disruptive/blocking response for now
        return json({
            status: {
                code: CONTROLLED_ERROR_RESPONSE_CODES.INTERNAL_ERROR,
                message: CONTROLLED_ERROR_RESPONSE_CODES.INTERNAL_ERROR,
                success: false
            },
            values: null,
        });
    }
};