import type { RequestEvent } from './$types';
import { json } from '@sveltejs/kit';
import { CONTROLLED_ERROR_RESPONSE_CODES } from '$lib/shared/controlled-response/_constants';
import LoggerService from '$lib/server/services/logger';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter';
import { TGetKeywordContentInput } from '$lib/shared/api-dtos/internal-rest/content-dtos';
import { ContentService } from '$lib/server/services/content';
import { AlgoliaService } from '$lib/server/services/algolia';
import { PagesService } from '$lib/server/services/pages';
import SeoService from '$lib/server/services/seo';
import { CategoryService } from '$lib/server/services/category';

export const POST = async (request: RequestEvent) => {
	const requestBodyParameters = await request.request.json();
	const {
		keyword,
		category
	} = requestBodyParameters as TGetKeywordContentInput;
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());

	try {
		// composition-root like
		const algoliaService = AlgoliaService();
		const seoService = SeoService(loggerService);
		const pageService = PagesService(seoService);
		const categoryService = CategoryService(algoliaService, seoService);
		const contentService = ContentService(loggerService, pageService, categoryService, algoliaService);
		const response = await contentService.getKeywordContent(keyword || '', category || '');
		const { isSuccess, value } = response.getResultProps();

        return json({
			status: {
				code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				success: isSuccess
			},
			values: value as string
		});
	} catch (error: any) {
		console.error(`
            Error on Content endpoint
            Method: POST
            Trace: ${error?.message ?? error}
        `);
		// return a non disruptive/blocking response for now
		return json({
			status: {
				code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
				success: false
			},
			values: null
		});
	}
};
