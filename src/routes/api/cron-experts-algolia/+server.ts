import { json } from '@sveltejs/kit';
import { AlgoliaService } from "$lib/server/services/algolia";
import { ExpertService } from "$lib/server/services/expert";
import { CONTROLLED_ERROR_RESPONSE_CODES } from "$lib/shared/controlled-response/_constants";
import LoggerService from '$lib/server/services/logger';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter';
import SeoService from '$lib/server/services/seo';

export const GET = async() =>  {
    const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	
    try {       
        // composition-root like
        const algoliaService = AlgoliaService();
        const seoService = SeoService(loggerService);
        const expertService = ExpertService(seoService);

        const response = await expertService.synchronizeAlgoliaExperts(algoliaService);
        const {isSuccess, value} = response.getResultProps();

        return json({
            status: {
                code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                success: isSuccess
            },
            values: value,

        });
    } catch (error: any) {
        console.error(`
            Error on Chron Experts Algolia endpoint
            Method: GET
            Trace: ${error?.message ?? error}
        `);
        // return a non disruptive/blocking response for now
        return json({
            status: {
                code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                success: false
            },
            values: [],

        });
    }
};