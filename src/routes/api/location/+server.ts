import type { RequestEvent } from "./$types";
import { json } from '@sveltejs/kit';

import type { TGetCategoriesResponse } from "$lib/shared/api-dtos/internal-rest/schema";
import { CONTROLLED_ERROR_RESPONSE_CODES } from "$lib/shared/controlled-response/_constants";
import type { TReducedCategoryDto } from "$lib/shared/api-dtos/internal-rest/category-dtos";
import { CategoryService } from "$lib/server/services/category";
import { AlgoliaService } from "$lib/server/services/algolia";
import { LocationService } from "$lib/server/services/location";
import { LOCATION_TYPES } from "$lib/server/services/location/_constants";
import SeoService from "$lib/server/services/seo";
import LoggerService from "$lib/server/services/logger";
import { HoneyBadgerAdapter, type THoneyBadgerContext } from "$lib/server/services/logger/honeybadger/adapter";

export const GET = async(request: RequestEvent) => {
    const {url: {searchParams}} = request;
    const locationTypeFilterParams = searchParams.get('type') as LOCATION_TYPES ?? LOCATION_TYPES.COUNTRY;
    const getReducedDtoFilterParams = searchParams.get('reduced');
    const expectedReducedDto = getReducedDtoFilterParams ? Boolean(getReducedDtoFilterParams) : false;

    try {
        // composition-root like
        const algoliaService = AlgoliaService();
        const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	    const seoService = SeoService(loggerService);
        const locationService = LocationService(algoliaService, seoService);

        const response = await locationService.getIndexedLocations(locationTypeFilterParams, expectedReducedDto);
        const {isSuccess, value} = response.getResultProps();
        
        return json({
            status: {
                code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                success: true
            },
            values: value as TReducedCategoryDto[],
        } as TGetCategoriesResponse);
    } catch (error) {
        // return a non disruptive/blocking response for now

        return json({
            status: {
                code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                success: false
            },
            values: [] as TReducedCategoryDto[],

        } as TGetCategoriesResponse);
    }
};