import type { RequestEvent } from "./$types";
import { json } from '@sveltejs/kit';

import type { TGetCategoriesResponse } from "$lib/shared/api-dtos/internal-rest/schema";
import { CONTROLLED_ERROR_RESPONSE_CODES } from "$lib/shared/controlled-response/_constants";
import type { TReducedCategoryDto } from "$lib/shared/api-dtos/internal-rest/category-dtos";
import { CategoryService } from "$lib/server/services/category";
import { AlgoliaService } from "$lib/server/services/algolia";
import LoggerService from "$lib/server/services/logger";
import { HoneyBadgerAdapter, THoneyBadgerContext } from "$lib/server/services/logger/honeybadger/adapter";
import SeoService from "$lib/server/services/seo";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import { LOGGER_ERROR_LEVEL } from "$lib/server/services/logger/_constants";

export const GET = async(request: RequestEvent) => {
    const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
    const {url: {searchParams}} = request;
    const getReducedDtoFilterParams = searchParams.get('reduced');
    const reduced = getReducedDtoFilterParams ? Boolean(getReducedDtoFilterParams) : false;

    try {
     
        // composition-root like
        const algoliaService = AlgoliaService();
	    const seoService = SeoService(loggerService);
        const categoryService = CategoryService(algoliaService, seoService);

        const response = await categoryService.getAllIndexedCategories({}, false);
        
        const {isSuccess, value} = response.getResultProps();
        const {items} = value;


        return json({
            status: {
                code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                success: true
            },
            values: items as TReducedCategoryDto[],
        } as TGetCategoriesResponse);
    } catch (error: any) {
        const caughtError = new InternalError(error, error?.message ?? error);
        loggerService.log(
			caughtError,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Category API endpoint',
					action: 'GET'
				}					
			} as THoneyBadgerContext
		);

        return json({
            status: {
                code: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                message: CONTROLLED_ERROR_RESPONSE_CODES.OK,
                success: false
            },
            values: [] as TReducedCategoryDto[],

        } as TGetCategoriesResponse);
    }
};