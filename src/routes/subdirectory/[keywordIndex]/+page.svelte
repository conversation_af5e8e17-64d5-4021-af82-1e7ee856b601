<script lang="ts">
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import SubdirectoryPage from '$lib/ui/components/pages/subdirectory/SubdirectoryPage.svelte';
	import type { TSubdirectoryPageDataSSR } from '../_types-interfaces';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';

	export let data: TSubdirectoryPageDataSSR;
	let page: TSubdirectoryPageDataSSR['page'];
	let keywords: TSubdirectoryPageDataSSR['keywords'];
	let keywordIndexFiltered: TSubdirectoryPageDataSSR['keywordIndexFiltered'];
	let keywordsIndexes: TSubdirectoryPageDataSSR['keywordsIndexes'];
	let paginationInfo: TSubdirectoryPageDataSSR['paginationInfo'];
	let schema:TSeo['schema'];
	let breadcrumbs: TSeo['breadcrumbs'];
	let restOfSeoData = {url: ''};
	$: {
		({page, keywords, keywordIndexFiltered, keywordsIndexes, paginationInfo} = data);
		if(data.seo) {
			({schema, breadcrumbs, ...restOfSeoData} = data.seo);
		}
	}
</script>


{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<SubdirectoryPage
	subdirectoryPageContent={page}
	{keywords}
	{keywordIndexFiltered}
	{keywordsIndexes}
	{breadcrumbs}
	hasNextPage={paginationInfo.hasNextPage}
	hasPreviousPage={paginationInfo.hasPreviousPage}
	nextPageUrl={paginationInfo.nextPageUrl ?? ''}
	previousPageUrl={paginationInfo.previousPageUrl ?? ''}
	total={paginationInfo.total}
/>
