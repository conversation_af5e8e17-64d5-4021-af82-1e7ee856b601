import { PagesService } from '$lib/server/services/pages';
import type { TExtendedPageDto, TReducedPageDto } from '$lib/shared/api-dtos/internal-rest/page-dtos';
import { error } from '@sveltejs/kit';
import LoggerService from '$lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '$lib/server/services/logger/honeybadger/adapter';
import InternalError from '$lib/shared/controlled-response/errors/internal-error';
import NotFoundError from '$lib/shared/controlled-response/errors/not-found-error';
import { LOGGER_ERROR_LEVEL } from '$lib/server/services/logger/_constants';
import SeoService from '$lib/server/services/seo';
import type { TSubdirectoryPageDataSSR } from './../_types-interfaces';
import { KEYWORD_INDEXES } from './../_constants';
import type { RouteParams } from './$types';
import { PUBLIC_SITE_URL } from '$env/static/public';
import { AlgoliaService } from '$lib/server/services/algolia';

export const load = async ({ url: {searchParams}, params: { keywordIndex } }: { url: URL, params: RouteParams }): Promise<TSubdirectoryPageDataSSR> => {
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);

	try {
		// Get Query string parameters
		const queryStrings = {
			page: searchParams.get('page') && !isNaN(parseInt(searchParams.get('page')!)) ? parseInt(searchParams.get('page')!) : 1
		};
		const routeRoot = 'subdirectory';
		const pageHandler = PagesService(seoService);
		const response = await pageHandler.getPageBySlug(routeRoot);
		const { isSuccess, value } = response.getResultProps();
		if (isSuccess) {
			const page = value as TExtendedPageDto;
			// Get list of Keyword Content Pages according to the "keywordIndexSearchParam"
			if(!KEYWORD_INDEXES.find((item) => item.toLowerCase() === keywordIndex.toLowerCase()))
					throw new NotFoundError(keywordIndex);

			// Get Keywords from Algolia
			const pageSize = 20;
			const algoliaService = AlgoliaService();
			const keywordsListResponse = await pageHandler.getIndexedPagesPaginated(
				{
					pageTypeFilter: 'keyword',
					keywordIndexFilter: `${keywordIndex.toUpperCase()}`,
					start: queryStrings.page <= 1 ? 0 : (queryStrings.page - 1) * pageSize,
					limit: pageSize
				},
				algoliaService,
				true,
				false
			);
			const {isSuccess: keywordsSuccess, value: keywordsValue} = keywordsListResponse.getResultProps();
			const hasNextPage = (queryStrings.page * pageSize) < keywordsValue.total;
			const hasPreviousPage = queryStrings.page > 1;
			const paginationInfo = {
				total: keywordsValue.total,
				hasNextPage, hasPreviousPage,
				nextPageUrl: hasNextPage ? `/${routeRoot}/${keywordIndex.toLowerCase()}?page=${(queryStrings.page + 1)}`: null,
				previousPageUrl: hasPreviousPage ? 
									queryStrings.page > 2 ? `/${routeRoot}/${keywordIndex.toLowerCase()}?page=${(queryStrings.page - 1)}`: 
									`/${routeRoot}/${keywordIndex.toLowerCase()}`
								: null
			};

			const {seo, ...restOfPageData} = page;
			const seoTitle = seoService.getTitleWithPrefix(seo!.title ?? '', `(Specialties starting with ${keywordIndex.toUpperCase()} ${queryStrings.page > 1 ? 'Page #'+queryStrings.page : ''})`);
			const seoDesc = `Browse ${keywordsValue.total ? 'among '+(keywordsValue.total) : ''} specialties starting with ${keywordIndex.toUpperCase()} ${queryStrings.page > 1 ? '(Page #'+queryStrings.page+')' : ''}. ${seo?.description}`;
			const seoCanonicalUrl = `${PUBLIC_SITE_URL}${routeRoot}/${keywordIndex.toLowerCase()}`;
			const parsedSchema = JSON.parse(seo?.schema ?? '')
			parsedSchema['@graph'][0]['@id'] = seoCanonicalUrl;
			parsedSchema['@graph'][0].url = seoCanonicalUrl;
			parsedSchema['@graph'][0].name = seoTitle;
			parsedSchema['@graph'][0].description = seoDesc;

			return {
				page: {...restOfPageData},
				seo: {
					...seo!,
					url: seoCanonicalUrl,
					canonicalUrl: seoCanonicalUrl,
					title: seoTitle,
					description: seoDesc,
					schema: JSON.stringify(parsedSchema),
					breadcrumbs: [
						{
							text: 'Home',
							url: '/'
						},
						{
							text: 'Specialties Subdirectory',
							url: `/${routeRoot}/${KEYWORD_INDEXES[0].toLowerCase()}`
						},
						{
							text: keywordIndex.toUpperCase(),
							url: `/${routeRoot}/${keywordIndex.toLowerCase()}`
						}
					]
				},
				keywords: keywordsSuccess ? keywordsValue.items as TExtendedPageDto[] : [],
				keywordIndexFiltered: keywordIndex.toLowerCase(),
				keywordsIndexes: KEYWORD_INDEXES,
				paginationInfo
			};
		}

		throw value;
	} catch (e: any) {
		let caughtError = new InternalError(e, e?.message ?? e);
		let isNotFound = false;
		if (e instanceof NotFoundError) {
			isNotFound = true;
			caughtError = e;
		}
		loggerService.log(caughtError, LOGGER_ERROR_LEVEL.ERROR, {
			data: caughtError.getControlledErrorReference() as unknown,
			message: caughtError.getMessage(),
			scope: {
				component: 'Keywords Subdirectory - SSR',
				action: 'load'
			}
		} as THoneyBadgerContext);
		if (isNotFound) throw error(404);

		return {
			page: {
				title: '',
				slug: 'subdirectory'
			},
			paginationInfo: {}
		};
	}
};
