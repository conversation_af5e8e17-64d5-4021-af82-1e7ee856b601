import { TExtendedPageDto, TReducedPageDto } from "$lib/shared/api-dtos/internal-rest/page-dtos";
import { TBasePageDataSSR } from "../+page.server";

export type TSubdirectoryPageDataSSR = TBasePageDataSSR & {
	page?: TExtendedPageDto;
	keywords?: TExtendedPageDto[];
	after?: string | undefined;
	before?: string | undefined;
	keywordIndexFiltered?: string;
	keywordsIndexes?: string[] | undefined;
	paginationInfo: {
		total?: number;
		hasNextPage?: boolean;
		hasPreviousPage?: boolean;
		nextPageUrl?: string | null;
		previousPageUrl?: string | null;
	}
};