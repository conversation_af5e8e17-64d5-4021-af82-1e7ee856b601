import { redirect, error } from "@sveltejs/kit";
import { KEYWORD_INDEXES } from "./_constants";

export const load = ({ url }: { url: URL }) => {
	let defaultKeywordIndex = KEYWORD_INDEXES[0].toLowerCase(); // defaultKeywordIndex = a
	if (url.searchParams.has('keywordsIndex')) {
		const keywordsIndex = url.searchParams.get('keywordsIndex');
		if(keywordsIndex && keywordsIndex.length === 1) defaultKeywordIndex = keywordsIndex.toLowerCase();
		else throw error(404);
	}

	
	throw redirect(301, `/subdirectory/${defaultKeywordIndex}`);
}