<script lang="ts">
	import Header from '$lib/ui/components/headers/Header.svelte';
	import { theme } from '$lib/ui/state-management/store/theme';
	import BaseLayout from '$lib/ui/components/layouts/BaseLayout.svelte';
	import '../app.css';
	import { onMount } from 'svelte';
	import GoogleAnalytics from '$lib/ui/components/web-engine/google-analytics/index.svelte';
	import <PERSON><PERSON><PERSON> from '$lib/ui/components/web-engine/hubspot/index.svelte';
	import Footer from '$lib/ui/components/footers/Footer.svelte';
	import type { TLayoutDataSSR } from './+layout.server';
	import { globalInfo } from '$lib/ui/state-management/store/global';
	import type { TGlobalSettingsDto } from '$lib/shared/api-dtos/internal-rest/settings-dtos';

	export let data: TLayoutDataSSR;
	let globalSettings: TGlobalSettingsDto;
	$: {
		if(data) {
			({globalSettings} = data);
		}
	}

	onMount(() => {
		theme.useLocalStorage();
		globalInfo.set(globalSettings);
	});
</script>

<GoogleAnalytics />

<div class={`${$theme ? 'dark' : 'light'}`}>
	<BaseLayout>
		<Header />
		<slot />
		<Footer />
	</BaseLayout>
</div>
