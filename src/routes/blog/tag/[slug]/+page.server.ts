import { sdk } from '$lib/server/services/sdk';
import { acfRestEndpoint } from '$lib/utils/endpoints';

export async function load({ url, params }) {
	const after = url.searchParams.get('after');
	const before = url.searchParams.get('before');
	const slug = params.slug + '';
	const postOptions = { afterCursor: '', beforeCursor: '', tag: slug } as {
		afterCursor: string;
		beforeCursor: string;
		first?: number;
		last?: number;
		tag: string;
	};
	const perPage = 20;
	if (after) {
		postOptions.afterCursor = after;
		postOptions.first = perPage;
	} else if (before) {
		postOptions.beforeCursor = before;
		postOptions.last = perPage;
	} else {
		postOptions.afterCursor = '';
		postOptions.first = perPage;
	}

	async function getPosts(options: {
		afterCursor: string;
		beforeCursor: string;
		first?: number;
		last?: number;
		tag: string;
	}) {
		const postsData = await sdk.GetTagPosts(options);
		return {
			posts: postsData?.posts?.nodes ?? [],
			pageInfo: postsData?.posts?.pageInfo
		};
	}

	async function getCategories(options: {
		afterCursor: string;
		beforeCursor: string;
		first: number;
	}) {
		const categoriesData = await sdk.GetCategories(options);
		return {
			categories: categoriesData?.categories?.nodes ?? [],
			pageInfo: categoriesData?.categories?.pageInfo
		};
	}

	return {
		postData: getPosts(postOptions),
		categoriesData: getCategories({ afterCursor: '', beforeCursor: '', first: 25 }),
		slug
	};
}
