<script lang="ts">
	import BlogTagLanding from '$lib/ui/components/pages/blog/blog-tag-landing.svelte';
	import type { PageServerData } from './$types';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import { env } from '$env/dynamic/public';
	import type { TGlobalSettingsDto } from '$lib/shared/api-dtos/internal-rest/settings-dtos';

	export let data: PageServerData;
</script>

<Seo
	data={{
		title: (data?.slug ?? '') + ' Tagged | Free Referral Blog',
		url: env.PUBLIC_SITE_URL + '/blog/tag/' + data.slug ?? '',
		description: 'Landing Page For Blogs Tagged with ' + data.slug ?? ''
	}}
/>
<BlogTagLanding {data} />
