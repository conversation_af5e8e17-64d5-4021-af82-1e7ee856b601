import { PUBLIC_SITE_URL } from '$env/static/public';
import LoggerService from '$lib/server/services/logger';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter';
import { sdk } from '$lib/server/services/sdk';
import type { CorePostFragment } from '$lib/shared/api-dtos/external-graphql/generated';
import {
  acfRestEndpoint,
  wpGraphQlCategoriesApiEndpoint,
  wpGraphQlMediaApiEndpoint,
  wpGraphQlPostsApiEndpoint,
  wpGraphQlUsersApiEndpoint,
} from '$lib/utils/endpoints';
import SeoService from '$lib/server/services/seo';
import { TSeoFromGraphQL } from '$lib/server/services/seo/_types-interfaces.js';
import { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos.js';

export type TFeaturedBlogs = {
	__typename: string;
	blogs: Array<CorePostFragment>;
};

type Category = {
	id: number;
	name: string;
};

type Author = {
	id: number;
	name: string;
};

export async function load({ url }) {
	const slug = 'blog'; // No needs to do over-engineering to get the slug dynamically in this case.
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);
	const page = url.searchParams.get('page') || '1';
	const pageUrl = `${PUBLIC_SITE_URL}${slug}`;

	const getPageData = async() => (await sdk.QueryBlogPage({ slug: 'blog' })).pages?.nodes[0] || null;

	const fetchResource = async (url: string) => {
		const response = await fetch(url);
		return response.json();
	};

	const fetchPosts = async (page = 1, categoryId: string) => {
		const categories: { [key: number]: Category } = {};
		const authors: { [key: number]: Author } = {};
		const featuredImages: { [key: number]: string } = {};

		const postsToSkip = 6;
		const perPage = 12;
		const offset = postsToSkip + (page - 1) * perPage;

		const response = await fetch(wpGraphQlPostsApiEndpoint({ offset, perPage, categoryId }));
		const data = await response.json();
		const totalPages = parseInt(response.headers.get('X-WP-TotalPages') || '1', 10);

		await Promise.all(
			data.map(async (post: any) => {
				if (post.categories?.length) {
					post.categoriesDetails = await Promise.all(
						post.categories.map(async (id: any) => {
							if (!categories[id]) {
								categories[id] = await fetchResource(wpGraphQlCategoriesApiEndpoint(id));
							}
							return categories[id];
						})
					);
				}
				if (post.author && !authors[post.author]) {
					authors[post.author] = await fetchResource(
						wpGraphQlUsersApiEndpoint(post.author)
					);
					post.authorDetails = authors[post.author];
				}
				if (post.featured_media && !featuredImages[post.featured_media]) {
					const mediaData = await fetchResource(
						wpGraphQlMediaApiEndpoint(post.featured_media)
					);
					featuredImages[post.featured_media] = mediaData.source_url;
					post.featuredImageUrl = featuredImages[post.featured_media];
				}
			})
		);

		return {
			posts: data,
			totalPages
		};
	};

	async function getLastestPosts(options: { first: number; category: string }) {
		const postsData = await sdk.GetCategoryLatestPosts(options);
		return {
			posts: postsData?.posts?.nodes ?? []
		};
	}

	async function getFeaturedPosts(options: { first: number; category: string; tag: string }) {
		const postData = await sdk.GetCategoryFeaturedPosts(options);
		return {
			posts: postData?.posts?.nodes ?? []
		};
	}

	async function getCategories(options: {
		afterCursor: string;
		beforeCursor: string;
		first: number;
	}) {
		const categoriesData = await sdk.GetCategories(options);
		return {
			categories: categoriesData?.categories?.nodes ?? [],
			pageInfo: categoriesData?.categories?.pageInfo
		};
	}

	const getSeoData = (seo: TSeoFromGraphQL): TSeo => {
		const mappedSeo = seoService.mapSeoGqlToDto(seo, pageUrl);
		return {
				...mappedSeo,
				title: mappedSeo.title ?? 'Blog | Free Referral',
				description: mappedSeo.description ?? 'Your go-to resource for all news, updates, and insights within the world of expert witnesses',
				focuskw: mappedSeo.focuskw ?? 'Expert witness news, expert witness blog, newsletter articles'
			};
	};

	const pageData =  await getPageData();
	const seoData = getSeoData(pageData?.seo ?? {});

	return {
		pageData,
		seoData,
		latestPosts: await getLastestPosts({ first: 6, category: '' }),
		featuredPosts: await getFeaturedPosts({ first: 3, category: '', tag: 'featured' }),
		postData: await fetchPosts(parseInt(page), ''),
		categoriesData: await getCategories({ afterCursor: '', beforeCursor: '', first: 25 })
	};
}
