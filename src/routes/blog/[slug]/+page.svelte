<script lang="ts">
	import type { PageServerData } from './$types';
	import Seo from '$lib/ui/components/web-engine/seo/index.svelte';
	import SchemaMarkup from '$lib/ui/components/web-engine/seo/schema-markup/index.svelte';
	import BlogItemPage from '$lib/ui/components/pages/blog/blog-item-landing.svelte';
	import type { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos';

	export let data: PageServerData;
	let schema: TSeo['schema'];
	let restOfData: Omit<PageServerData, 'seoData'>;
	let restOfSeoData: Omit<TSeo, 'schema'>;
	$: {
		const {seoData, ...restData} = data;
		restOfData = restData;
		if(seoData) {
			({ schema, ...restOfSeoData } = seoData);
		}
	}
</script>

{#if restOfSeoData}
	<Seo data={restOfSeoData} />
{/if}
{#if schema}
	<SchemaMarkup {schema} />
{/if}

<BlogItemPage data={{...restOfData}} />
