import { sdk } from '$lib/server/services/sdk';
import { error } from '@sveltejs/kit';
import SeoService from '../../../lib/server/services/seo';
import LoggerService from '../../../lib/server/services/logger';
import {
	HoneyBadgerAdapter,
	type THoneyBadgerContext
} from '../../../lib/server/services/logger/honeybadger/adapter';
import type { TSeo, TSeoRedirectDto } from '../../../lib/shared/api-dtos/internal-rest/base-dtos';
import { PUBLIC_SITE_URL } from '$env/static/public';
import { TSeoFromGraphQL, TSeoService } from '$lib/server/services/seo/_types-interfaces';
import type { TPageError } from '../../_types-interfaces';

async function _getPost(slug: string) {
	const options = {
		slug,
		preview: false
	};
	const postData = await sdk.GetPost(options);
	return postData.post;
}

async function _getFeaturedPosts(slug: string) {
	const featuredPostsOptions = {
		afterCursor: '',
		beforeCursor: '',
		first: 4
		// tag: 'featured'
	} as {
		afterCursor: string;
		beforeCursor: string;
		first?: number;
		last?: number;
		tag: string;
	};

	const featuredPostsData = await sdk.GetTagPosts(featuredPostsOptions);
	const filteredFeaturedPostsData =
		featuredPostsData.posts?.nodes.filter((post) => {
			return post.slug !== slug;
		}) ?? [];

	return filteredFeaturedPostsData?.slice(0, 3);
}

const getSeoData = (seo: TSeoFromGraphQL, pageUrl: string, seoService: TSeoService): TSeo => {
	const mappedSeo = seoService.mapSeoGqlToDto(seo, pageUrl);
	return {
			...mappedSeo,
			title: mappedSeo.title ?? 'Blog | Free Referral',
			description: mappedSeo.description ?? 'Your go-to resource for all news, updates, and insights within the world of expert witnesses',
			focuskw: mappedSeo.focuskw ?? 'Expert witness news, expert witness blog, newsletter articles'
		};
};

export const load = async ({ params }: { params: { slug: string } }) => {
	const slug = params.slug;
	const pageUrl = `${PUBLIC_SITE_URL}blog/${slug}`;
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);

	const post = await _getPost(slug);
	if (!post) {
		const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
		const seoService = SeoService(loggerService);
		const redirectRes = await seoService.findRedirect({ originUrl: slug, status: 301 });
		const { isSuccess, value } = redirectRes.getResultProps();

		let redirectTarget = '';
		if (isSuccess) {
			const target = (value as TSeoRedirectDto).target;
			redirectTarget = target ?? '';
		}
		throw error(404, { 
			message: `The content you are trying to reach was moved to a new URL`,
			data: {
				redirectUrl: redirectTarget && redirectTarget !== 'search' ? `/blog/${redirectTarget}` : null
			}
		} as TPageError);
	}
	const {seo, ...restOfPostData} = post;
	const seoData = getSeoData(seo as TSeoFromGraphQL, pageUrl, seoService);
	const featuredPosts = await _getFeaturedPosts(slug);

	return {
		post: {...restOfPostData},
		seoData,
		featuredPosts
	};
};
