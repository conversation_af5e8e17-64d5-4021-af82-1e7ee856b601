import { PUBLIC_SITE_URL } from '$env/static/public';
import { HoneyBadgerAdapter, THoneyBadgerContext } from '$lib/server/services/logger/honeybadger/adapter.js';
import LoggerService from '$lib/server/services/logger/index.js';
import { sdk } from '$lib/server/services/sdk';
import {
  wpGraphQlCategoriesApiEndpoint,
  wpGraphQlMediaApiEndpoint,
  wpGraphQlPostsApiEndpoint,
  wpGraphQlUsersApiEndpoint,
} from '$lib/utils/endpoints';
import { TSeoFromGraphQL } from '$lib/server/services/seo/_types-interfaces.js';
import SeoService from '$lib/server/services/seo/index.js';
import { TSeo } from '$lib/shared/api-dtos/internal-rest/base-dtos.js';

type Category = {
	id: number;
	name: string;
};

type Author = {
	id: number;
	name: string;
};

export async function load({ url, params }) {
	const category = params.slug + '';
	const page = url.searchParams.get('page') || '1';
	const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
	const seoService = SeoService(loggerService);
	const pageUrl = `${PUBLIC_SITE_URL}blog/category/${category}`;

	async function getCategory(options: { slug?: [string] }) {
		const categoriesData = await sdk.GetCategory(options);
		return {
			category: categoriesData?.categories?.nodes[0] ?? null
		};
	}

	async function getCategories(options: {
		afterCursor: string;
		beforeCursor: string;
		first: number;
	}) {
		const categoriesData = await sdk.GetCategories(options);
		return {
			categories: categoriesData?.categories?.nodes ?? [],
			pageInfo: categoriesData?.categories?.pageInfo
		};
	}

	async function getLastestPosts(options: { first: number; category: string }) {
		const postsData = await sdk.GetCategoryLatestPosts(options);
		return {
			posts: postsData?.posts?.nodes ?? []
		};
	}

	async function getFeaturedPosts(options: { first: number; category: string; tag: string }) {
		const postData = await sdk.GetCategoryFeaturedPosts(options);
		return {
			posts: postData?.posts?.nodes ?? []
		};
	}

	const fetchResource = async (url: string) => {
		const response = await fetch(url);
		return response.json();
	};

	const fetchPosts = async (page = 1, categoryId: string) => {
		const categories: { [key: number]: Category } = {};
		const authors: { [key: number]: Author } = {};
		const featuredImages: { [key: number]: string } = {};

		const postsToSkip = 6;
		const perPage = 12;
		const offset = postsToSkip + (page - 1) * perPage;

		const response = await fetch(wpGraphQlPostsApiEndpoint({ offset, perPage, categoryId }));
		const data = await response.json();
		const totalPages = parseInt(response.headers.get('X-WP-TotalPages') || '1', 10);

		await Promise.all(
			data.map(async (post: any) => {
				if (post.categories?.length) {
					post.categoriesDetails = await Promise.all(
						post.categories.map(async (id: any) => {
							if (!categories[id]) {
								categories[id] = await fetchResource(wpGraphQlCategoriesApiEndpoint(id));
							}
							return categories[id];
						})
					);
				}
				if (post.author && !authors[post.author]) {
					authors[post.author] = await fetchResource(wpGraphQlUsersApiEndpoint(post.author));
					post.authorDetails = authors[post.author];
				}
				if (post.featured_media && !featuredImages[post.featured_media]) {
					const mediaData = await fetchResource(wpGraphQlMediaApiEndpoint(post.featured_media));
					featuredImages[post.featured_media] = mediaData.source_url;
					post.featuredImageUrl = featuredImages[post.featured_media];
				}
			})
		);

		return {
			posts: data,
			totalPages
		};
	};

	// @TODO: Align SEO info(title)
	const getSeoData = (seo: TSeoFromGraphQL, categoryName: string, pageUrl: string): TSeo => {
		const mappedSeo = seoService.mapSeoGqlToDto(seo, pageUrl);
		return {
				...mappedSeo,
				title: `${categoryName} Category | Free Referral Blog`,
				description: mappedSeo.description ?? `Landing Page For Blogs By Category: ${categoryName}`,
				focuskw: mappedSeo.focuskw ?? 'Expert witness news, expert witness blog, newsletter articles'
			};
	};

	const categoryData = await getCategory({ slug: [category] });
	const seo = (categoryData.category?.seo ?? {}) as TSeoFromGraphQL;
	const seoData = getSeoData(
		seo,
		`${categoryData.category?.name ?? ''}`,
		pageUrl
	);

	return {
		postData: await fetchPosts(
			parseInt(page),
			categoryData?.category?.databaseId?.toString() ?? ''
		),
		latestPosts: getLastestPosts({ first: 6, category }) ?? [],
		featuredPosts: getFeaturedPosts({ first: 3, category, tag: 'featured' }) ?? [],
		categoriesData: getCategories({ afterCursor: '', beforeCursor: '', first: 25 }),
		categoryData,
		seoData
	};
}
