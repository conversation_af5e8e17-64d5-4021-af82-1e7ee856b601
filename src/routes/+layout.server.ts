import ConfigService from "$lib/server/services/config";
import LoggerService from "$lib/server/services/logger";
import { LOGGER_ERROR_LEVEL } from "$lib/server/services/logger/_constants";
import { HoneyBadgerAdapter, THoneyBadgerContext } from "$lib/server/services/logger/honeybadger/adapter";
import { TGlobalSettingsDto } from "$lib/shared/api-dtos/internal-rest/settings-dtos";
import InternalError from "$lib/shared/controlled-response/errors/internal-error";
import NotFoundError from "$lib/shared/controlled-response/errors/not-found-error";

// Keeps URLs unchanged and prevents unwanted redirects. It preserves also URLs with trailing slashes.
// This will allow handling those URLs manually and to set a robot-tag to not index using a server-hook.
export const trailingSlash = 'ignore';

export type TLayoutDataSSR = {
    globalSettings: TGlobalSettingsDto;
};

export const load = async({url}: {url: URL}): Promise<TLayoutDataSSR> => {
    const loggerService = LoggerService<THoneyBadgerContext>(HoneyBadgerAdapter());
    let globalSettings = {};

    try {
        const configService = ConfigService(loggerService);
        const {isSuccess, value} = (await configService.getGlobalSettings()).getResultProps();
        if(isSuccess) globalSettings = value;

    } catch (error: any) { //@TODO: Refactor this (use UNKNOWN type and fix references properly)
        let caughtError = new InternalError(error, error?.message ?? error);
		if (error instanceof NotFoundError)  caughtError = error;
		
		loggerService.log(
			caughtError,
			LOGGER_ERROR_LEVEL.ERROR,
			{
				data: caughtError.getControlledErrorReference() as unknown,
				message: caughtError.getMessage(),
				scope: {
					component: 'Main Layout - SSR',
					action: 'load'
				}
			} as THoneyBadgerContext
		);
    }

    return {globalSettings};
};