import fs from 'fs';
import path from 'path';

const routesDir: string = path.join(__dirname, '..', 'src', 'routes');
const outputFile: string = path.join(__dirname, '..', 'src', 'lib', 'routes.ts');

function isStaticRoute(routeName: string): boolean {
    return !routeName.includes('[') && !routeName.includes(']');
}

function getRoutes(dir: string, baseRoute = ''): Record<string, string> {
    let routes: Record<string, string> = {};
    const files: string[] = fs.readdirSync(dir);

    for (const file of files) {
        const filePath: string = path.join(dir, file);
        const stats: fs.Stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
            if (isStaticRoute(file)) {
                const nestedRoutes: Record<string, string> = getRoutes(filePath, `${baseRoute}/${file}`);
                routes = { ...routes, ...nestedRoutes };
            }
        } else {
            const ext: string = path.extname(file);
            const name: string = path.basename(file, ext);
            if ((name === '+page' || name === '+layout') && isStaticRoute(baseRoute)) {
                const routeName: string = baseRoute.toUpperCase().replace(/[^A-Z0-9]+/g, '_') || 'HOME';
                routes[routeName] = baseRoute || '/';
            }
        }
    }

    return routes;
}

const routes: Record<string, string> = getRoutes(routesDir);
const enumContent = `export enum ROUTES {
${Object.entries(routes).map(([key, value]) => `    ${key} = '${value}',`).join('\n')}
}`;
fs.writeFileSync(outputFile, enumContent);
console.log(`Routes enum generated at ${outputFile}`);